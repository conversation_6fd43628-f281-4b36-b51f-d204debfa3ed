# Copyright Statement:                                                                                               
#                                                                                                                    
# (C) 2017  Airoha Technology Corp. All rights reserved.                                                             
#                                                                                                                    
# This software/firmware and related documentation ("Airoha Software") are                                           
# protected under relevant copyright laws. The information contained herein                                          
# is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.                        
# Without the prior written permission of Airoha and/or its licensors,                                               
# any reproduction, modification, use or disclosure of Airoha Software,                                              
# and information contained herein, in whole or in part, shall be strictly prohibited.                               
# You may only use, reproduce, modify, or distribute (as applicable) Airoha Software                                 
# if you have agreed to and been bound by the applicable license agreement with                                      
# Airoha ("License Agreement") and been granted explicit permission to do so within                                  
# the License Agreement ("Permitted User").  If you are not a Permitted User,                                        
# please cease any access or use of Airoha Software immediately.                                                     
# BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES                                        
# THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES                                               
# ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL                          
# WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF                             
# MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.                                              
# NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE                                            
# SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR                                              
# SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH                                            
# THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES                               
# THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES                       
# CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA                                  
# SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR                                   
# STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND                               
# CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,                                   
# AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,                                                 
# OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO                                          
# AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.                                                                          
#                                                                                                                    

### Makefile to build the FreeRTOS library ###

ifeq ($(IOT_SDK_XTENSA_VERSION),9018)
CONFIGDIR   = $(shell xt-clang --show-config=config)
else ifeq ($(IOT_SDK_XTENSA_VERSION),8013)
CONFIGDIR   = $(shell xt-xcc --show-config=config)
endif
# For platform-specific commands
include $(CONFIGDIR)/misc/hostenv.mk

# module makefile settings
ROOTDIR       :=  ../../../..
OUTDIR        ?=  Build
BUILD_LIBDIR  :=  $(strip $(OUTDIR))/lib
LOGDIR        :=  $(strip $(OUTDIR))/log
ERR_LOG       :=  $(strip $(LOGDIR))/err.log
BUILD_LOG     :=  $(strip $(LOGDIR))/build.log

# Output files
TARGET_LIB  :=  $(BUILD_LIBDIR)/libctxsw

# info from project makefile
INC     += $(PROJ_INC)
CCFLAG  += $(PROJ_CCFLAG)
ASFLAG  += $(PROJ_ASFLAG)

#for lib build only
# INC  +=  driver/chip/inc
# INC  +=  driver/chip/mt2811/dsp0/inc/

# include compiler and commands configuration
include $(ROOTDIR)/.config.mk

# Build options
ifneq ($(TARGET),SIM)
DFLAGS      = -DXT_BOARD
else
DFLAGS      = -DXT_SIMULATOR
endif

# Filter out literals option
CCFLAG := $(filter-out -mtext-section-literals, $(CCFLAG))
CCFLAG += $(DFLAGS)
ASFLAG := $(filter-out --text-section-literals, $(ASFLAG))
ASFLAG += $(DFLAGS)

# .S and .c objects list
include module_lib.mk

# .o objects & .d dependency list
OBJ  =  $(ASM_SRC:%.S=$(OUTDIR)/%.o) $(C_SRC:%.c=$(OUTDIR)/%.o)
DEP  =  $(ASM_SRC:%.S=$(OUTDIR)/%.d) $(C_SRC:%.c=$(OUTDIR)/%.d)

# Targets
.PHONY : all clean create_logdir clean_logdir

all : create_logdir $(TARGET_LIB).a
	@echo Build $(TARGET_LIB).a Done

create_logdir:
	@mkdir -p $(LOGDIR)

clean_logdir :
	@if [ -e "$(strip $(LOGDIR))" ]; then rm -rf "$(strip $(LOGDIR))"; fi

clean :
	$(RM_R) $(subst /,\, $(OUTDIR))

include $(ROOTDIR)/.rule.mk
-include $(DEP)
