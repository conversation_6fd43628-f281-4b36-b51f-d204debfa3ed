/* Copyright Statement:
 *
 * (C) 2017  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#include "hal_trng.h"

#ifdef HAL_TRNG_MODULE_ENABLED
#include "hal_trng_internal.h"
#include "hal_resource_assignment.h"
#include "hal_hw_semaphore.h"
#include "hal_log.h"

#ifdef HAL_SLEEP_MANAGER_ENABLED
#include "hal_sleep_manager.h"
#include "hal_sleep_manager_platform.h"
#endif

static volatile uint8_t trng_init_status = 0;

hal_trng_status_t hal_trng_init(void)
{
    hal_trng_status_t busy_status;
    TRNG_CHECK_AND_SET_BUSY(busy_status);
    if (HAL_TRNG_STATUS_ERROR == busy_status) {
        return HAL_TRNG_STATUS_ERROR;
    }

    //trng_init();
    return HAL_TRNG_STATUS_OK;
}

hal_trng_status_t hal_trng_deinit(void)
{
    //trng_deinit();
    TRNG_SET_IDLE();
    return HAL_TRNG_STATUS_OK;
}

hal_trng_status_t hal_trng_get_generated_random_number(uint32_t *random_number)
{
    uint32_t generate_data = 0;
#ifdef HAL_HW_SEMAPHORE_MODULE_ENABLED
    if (HAL_HW_SEMAPHORE_STATUS_OK == hal_hw_semaphore_take(HW_SEMAPHORE_TRNG)) {
#endif
        trng_init();
#ifdef HAL_SLEEP_MANAGER_ENABLED
        hal_sleep_manager_lock_sleep(SLEEP_LOCK_TRNG);
#endif
        trng_config_timeout_limit(0xFFF);
        trng_enable_mode(true, true, true);
        trng_start();
        generate_data = trng_get_random_data();
        trng_stop();
#ifdef HAL_SLEEP_MANAGER_ENABLED
        hal_sleep_manager_unlock_sleep(SLEEP_LOCK_TRNG);
#endif
        if (generate_data == 0) {
            trng_deinit();
            return  HAL_TRNG_STATUS_ERROR;
        } else {
            *random_number = generate_data;
        }
        trng_deinit();
#ifdef HAL_HW_SEMAPHORE_MODULE_ENABLED
        if (HAL_HW_SEMAPHORE_STATUS_OK != hal_hw_semaphore_give(HW_SEMAPHORE_TRNG)) {
            log_hal_msgid_warning("[Warning][TRNG] do not give HW semaphore .\r\n", 0);
        }

    } else {
        log_hal_msgid_warning("[WarningDSP][TRNG] The TRNG is using by other core.\r\n", 0);
        return HAL_TRNG_STATUS_OTHER_CORE_USING;
    }
#endif
    return HAL_TRNG_STATUS_OK;
}

#endif /*HAL_TRNG_MODULE_ENABLED*/
