/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HAL_AUDIO_AFE_CONNECTION_H__
#define __HAL_AUDIO_AFE_CONNECTION_H__

#include "hal_audio.h"

#ifdef HAL_AUDIO_MODULE_ENABLED

#include <stdint.h>
#include <stdbool.h>
#include "hal_audio_path.h"//modify for ab1568
typedef enum {
    /* memory interfrace */
    AUDIO_DIGITAL_BLOCK_MEM_DL1 = 0,
    AUDIO_DIGITAL_BLOCK_MEM_DL2,
    AUDIO_DIGITAL_BLOCK_MEM_DL3,
    AUDIO_DIGITAL_BLOCK_MEM_DL12,
    AUDIO_DIGITAL_BLOCK_MEM_VUL1,
    AUDIO_DIGITAL_BLOCK_MEM_VUL2,
    AUDIO_DIGITAL_BLOCK_MEM_AWB,
    AUDIO_DIGITAL_BLOCK_MEM_AWB2,
    /* Master I2S */
    AUDIO_DIGITAL_BLOCK_I2S0_OUT,
    AUDIO_DIGITAL_BLOCK_I2S1_OUT,
    AUDIO_DIGITAL_BLOCK_I2S2_OUT,
    AUDIO_DIGITAL_BLOCK_I2S3_OUT,
    AUDIO_DIGITAL_BLOCK_I2S0_IN,
    AUDIO_DIGITAL_BLOCK_I2S1_IN,
    AUDIO_DIGITAL_BLOCK_I2S2_IN,
    AUDIO_DIGITAL_BLOCK_I2S3_IN,
    /* ADDA */
    AUDIO_DIGITAL_BLOCK_ADDA_DL,
    AUDIO_DIGITAL_BLOCK_ADDA_UL1,
    AUDIO_DIGITAL_BLOCK_ADDA_UL2,
    AUDIO_DIGITAL_BLOCK_ADDA_UL3,
    /* HW gain contorl */
    AUDIO_DIGITAL_BLOCK_HW_GAIN1,
    AUDIO_DIGITAL_BLOCK_HW_GAIN2,
    /* Side Tone Filter */
    AUDIO_DIGITAL_BLOCK_STF,
    /* megrge interface */
    AUDIO_DIGITAL_BLOCK_NUM_OF_DIGITAL_BLOCK,
    AUDIO_DIGITAL_BLOCK_NUM_OF_MEM_INTERFACE = AUDIO_DIGITAL_BLOCK_MEM_AWB2 + 1
} audio_digital_block_t;

#if 1//modify for ab1568

typedef enum {
    /* memory interfrace */
    AUDIO_AFE_IO_BLOCK_MEM_DL1 = 0,
    AUDIO_AFE_IO_BLOCK_MEM_DL1_CH1 = AUDIO_AFE_IO_BLOCK_MEM_DL1,
    AUDIO_AFE_IO_BLOCK_MEM_DL1_CH2,
    AUDIO_AFE_IO_BLOCK_MEM_DL2,
    AUDIO_AFE_IO_BLOCK_MEM_DL2_CH1 = AUDIO_AFE_IO_BLOCK_MEM_DL2,
    AUDIO_AFE_IO_BLOCK_MEM_DL2_CH2,
    AUDIO_AFE_IO_BLOCK_MEM_DL3,
    AUDIO_AFE_IO_BLOCK_MEM_DL3_CH1 = AUDIO_AFE_IO_BLOCK_MEM_DL3,
    AUDIO_AFE_IO_BLOCK_MEM_DL3_CH2,
    AUDIO_AFE_IO_BLOCK_MEM_DL12,
    AUDIO_AFE_IO_BLOCK_MEM_DL12_CH1 = AUDIO_AFE_IO_BLOCK_MEM_DL12,
    AUDIO_AFE_IO_BLOCK_MEM_DL12_CH2,
    AUDIO_AFE_IO_BLOCK_MEM_VUL1,
    AUDIO_AFE_IO_BLOCK_MEM_VUL1_CH1 = AUDIO_AFE_IO_BLOCK_MEM_VUL1,
    AUDIO_AFE_IO_BLOCK_MEM_VUL1_CH2,
    AUDIO_AFE_IO_BLOCK_MEM_VUL2,
    AUDIO_AFE_IO_BLOCK_MEM_VUL2_CH1 = AUDIO_AFE_IO_BLOCK_MEM_VUL2,
    AUDIO_AFE_IO_BLOCK_MEM_VUL2_CH2,
    AUDIO_AFE_IO_BLOCK_MEM_AWB,
    AUDIO_AFE_IO_BLOCK_MEM_AWB_CH1 = AUDIO_AFE_IO_BLOCK_MEM_AWB,
    AUDIO_AFE_IO_BLOCK_MEM_AWB_CH2,
    AUDIO_AFE_IO_BLOCK_MEM_AWB2,
    AUDIO_AFE_IO_BLOCK_MEM_AWB2_CH1 = AUDIO_AFE_IO_BLOCK_MEM_AWB2,
    AUDIO_AFE_IO_BLOCK_MEM_AWB2_CH2,


    AUDIO_AFE_IO_BLOCK_I2S0_OUT,
    AUDIO_AFE_IO_BLOCK_I2S0_OUT_CH1 = AUDIO_AFE_IO_BLOCK_I2S0_OUT,
    AUDIO_AFE_IO_BLOCK_I2S0_OUT_CH2,
    AUDIO_AFE_IO_BLOCK_I2S1_OUT,
    AUDIO_AFE_IO_BLOCK_I2S1_OUT_CH1 = AUDIO_AFE_IO_BLOCK_I2S1_OUT,
    AUDIO_AFE_IO_BLOCK_I2S1_OUT_CH2,
    AUDIO_AFE_IO_BLOCK_I2S2_OUT,
    AUDIO_AFE_IO_BLOCK_I2S2_OUT_CH1 = AUDIO_AFE_IO_BLOCK_I2S2_OUT,
    AUDIO_AFE_IO_BLOCK_I2S2_OUT_CH2,
    AUDIO_AFE_IO_BLOCK_I2S3_OUT,
    AUDIO_AFE_IO_BLOCK_I2S3_OUT_CH1 = AUDIO_AFE_IO_BLOCK_I2S3_OUT,
    AUDIO_AFE_IO_BLOCK_I2S3_OUT_CH2,

    AUDIO_AFE_IO_BLOCK_I2S0_IN,
    AUDIO_AFE_IO_BLOCK_I2S0_IN_CH1 = AUDIO_AFE_IO_BLOCK_I2S0_IN,
    AUDIO_AFE_IO_BLOCK_I2S0_IN_CH2 ,
    AUDIO_AFE_IO_BLOCK_I2S1_IN,
    AUDIO_AFE_IO_BLOCK_I2S1_IN_CH1 = AUDIO_AFE_IO_BLOCK_I2S1_IN,
    AUDIO_AFE_IO_BLOCK_I2S1_IN_CH2,
    AUDIO_AFE_IO_BLOCK_I2S2_IN,
    AUDIO_AFE_IO_BLOCK_I2S2_IN_CH1 = AUDIO_AFE_IO_BLOCK_I2S2_IN,
    AUDIO_AFE_IO_BLOCK_I2S2_IN_CH2,
    AUDIO_AFE_IO_BLOCK_I2S3_IN,
    AUDIO_AFE_IO_BLOCK_I2S3_IN_CH1 = AUDIO_AFE_IO_BLOCK_I2S3_IN,
    AUDIO_AFE_IO_BLOCK_I2S3_IN_CH2,
    /* ADDA */
    AUDIO_AFE_IO_BLOCK_ADDA_DL,
    AUDIO_AFE_IO_BLOCK_ADDA_DL_CH1 = AUDIO_AFE_IO_BLOCK_ADDA_DL,
    AUDIO_AFE_IO_BLOCK_ADDA_DL_CH2,
    AUDIO_AFE_IO_BLOCK_ADDA_UL1,
    AUDIO_AFE_IO_BLOCK_ADDA_UL1_CH1 = AUDIO_AFE_IO_BLOCK_ADDA_UL1,
    AUDIO_AFE_IO_BLOCK_ADDA_UL1_CH2,
    AUDIO_AFE_IO_BLOCK_ADDA_UL2,
    AUDIO_AFE_IO_BLOCK_ADDA_UL2_CH1 = AUDIO_AFE_IO_BLOCK_ADDA_UL2,
    AUDIO_AFE_IO_BLOCK_ADDA_UL2_CH2,
    AUDIO_AFE_IO_BLOCK_ADDA_UL3,
    AUDIO_AFE_IO_BLOCK_ADDA_UL3_CH1 = AUDIO_AFE_IO_BLOCK_ADDA_UL3,
    AUDIO_AFE_IO_BLOCK_ADDA_UL3_CH2,
    /* HW gain contorl */
    AUDIO_AFE_IO_BLOCK_HW_GAIN1_OUT,
    AUDIO_AFE_IO_BLOCK_HW_GAIN1_OUT_CH1 = AUDIO_AFE_IO_BLOCK_HW_GAIN1_OUT,
    AUDIO_AFE_IO_BLOCK_HW_GAIN1_OUT_CH2,
    AUDIO_AFE_IO_BLOCK_HW_GAIN2_OUT,
    AUDIO_AFE_IO_BLOCK_HW_GAIN2_OUT_CH1 = AUDIO_AFE_IO_BLOCK_HW_GAIN2_OUT,
    AUDIO_AFE_IO_BLOCK_HW_GAIN2_OUT_CH2,
    AUDIO_AFE_IO_BLOCK_HW_GAIN1_IN,
    AUDIO_AFE_IO_BLOCK_HW_GAIN1_IN_CH1 = AUDIO_AFE_IO_BLOCK_HW_GAIN1_IN,
    AUDIO_AFE_IO_BLOCK_HW_GAIN1_IN_CH2,
    AUDIO_AFE_IO_BLOCK_HW_GAIN2_IN,
    AUDIO_AFE_IO_BLOCK_HW_GAIN2_IN_CH1 = AUDIO_AFE_IO_BLOCK_HW_GAIN2_IN,
    AUDIO_AFE_IO_BLOCK_HW_GAIN2_IN_CH2,
    /* STF */
    AUDIO_AFE_IO_BLOCK_STF,
    AUDIO_AFE_IO_BLOCK_STF_CH1 = AUDIO_AFE_IO_BLOCK_STF,
    AUDIO_AFE_IO_BLOCK_STF_CH2,
    /* num of IO block */
    AUDIO_AFE_IO_BLOCK_NUM_OF_IO_BLOCK
} audio_afe_io_block_t;

typedef enum {
    AUDIO_AFE_MEMIF_DIRECTION_OUTPUT,
    AUDIO_AFE_MEMIF_DIRECTION_INPUT
} audio_memif_direction_t;

/*Align AFE RG bit operation*/
#if 0
typedef enum {
    AUDIO_INTERCONNECTION_INPUT_I00 = 0,
    AUDIO_INTERCONNECTION_INPUT_I01,
    AUDIO_INTERCONNECTION_INPUT_I02,
    AUDIO_INTERCONNECTION_INPUT_I03,
    AUDIO_INTERCONNECTION_INPUT_I04,
    AUDIO_INTERCONNECTION_INPUT_I05,
    AUDIO_INTERCONNECTION_INPUT_I06,
    AUDIO_INTERCONNECTION_INPUT_I07,
    AUDIO_INTERCONNECTION_INPUT_I08,
    AUDIO_INTERCONNECTION_INPUT_I09,
    AUDIO_INTERCONNECTION_INPUT_I10,
    AUDIO_INTERCONNECTION_INPUT_I11,
    AUDIO_INTERCONNECTION_INPUT_I12,
    AUDIO_INTERCONNECTION_INPUT_I13,
    AUDIO_INTERCONNECTION_INPUT_I14,
    AUDIO_INTERCONNECTION_INPUT_I15,
    AUDIO_INTERCONNECTION_INPUT_I16,
    AUDIO_INTERCONNECTION_INPUT_I17,
    AUDIO_INTERCONNECTION_INPUT_I18,
    AUDIO_INTERCONNECTION_INPUT_I19,
    AUDIO_INTERCONNECTION_INPUT_I20,
    AUDIO_INTERCONNECTION_INPUT_I21,
    AUDIO_INTERCONNECTION_INPUT_I22,
    AUDIO_INTERCONNECTION_INPUT_I23,
    AUDIO_INTERCONNECTION_INPUT_I24,
    AUDIO_INTERCONNECTION_INPUT_I25,
    AUDIO_INTERCONNECTION_INPUT_NUM
} audio_interconnection_input_t;

/*Align AFE RG bit operation*/
typedef enum {
    AUDIO_INTERCONNECTION_OUTPUT_O00 = 0,
    AUDIO_INTERCONNECTION_OUTPUT_O01,
    AUDIO_INTERCONNECTION_OUTPUT_O02,
    AUDIO_INTERCONNECTION_OUTPUT_O03,
    AUDIO_INTERCONNECTION_OUTPUT_O04,
    AUDIO_INTERCONNECTION_OUTPUT_O05,
    AUDIO_INTERCONNECTION_OUTPUT_O06,
    AUDIO_INTERCONNECTION_OUTPUT_O07,
    AUDIO_INTERCONNECTION_OUTPUT_O08,
    AUDIO_INTERCONNECTION_OUTPUT_O09,
    AUDIO_INTERCONNECTION_OUTPUT_O10,
    AUDIO_INTERCONNECTION_OUTPUT_O11,
    AUDIO_INTERCONNECTION_OUTPUT_O12,
    AUDIO_INTERCONNECTION_OUTPUT_O13,
    AUDIO_INTERCONNECTION_OUTPUT_O14,
    AUDIO_INTERCONNECTION_OUTPUT_O15,
    AUDIO_INTERCONNECTION_OUTPUT_O16,
    AUDIO_INTERCONNECTION_OUTPUT_O17,
    AUDIO_INTERCONNECTION_OUTPUT_O18,
    AUDIO_INTERCONNECTION_OUTPUT_O19,
    AUDIO_INTERCONNECTION_OUTPUT_O20,
    AUDIO_INTERCONNECTION_OUTPUT_O21,
    AUDIO_INTERCONNECTION_OUTPUT_O22,
    AUDIO_INTERCONNECTION_OUTPUT_O23,
    AUDIO_INTERCONNECTION_OUTPUT_NUM
} audio_interconnection_output_t;


typedef enum {
    AUDIO_INTERCONNECTION_DISCONNECT    = 0x0,
    AUDIO_INTERCONNECTION_CONNECT       = 0x1,
    AUDIO_INTERCONNECTION_CONNECTSHIFT  = 0x2
} audio_interconnection_state_t;
#endif

typedef enum {
    AFE_OUTPUT_DATA_FORMAT_16BIT = 0,
    AFE_OUTPUT_DATA_FORMAT_24BIT
} afe_output_data_format_t;
#if 0//modify for ab1568
typedef enum {
    AFE_HW_DIGITAL_GAIN1 = 1,
    AFE_HW_DIGITAL_GAIN2 = 1<<1,
} afe_hardware_digital_gain_t;
#endif
bool hal_audio_afe_set_connection_format(afe_output_data_format_t connection_format, audio_afe_io_block_t audio_block);
//bool hal_audio_afe_set_intf_connection_state(audio_interconnection_state_t connection_state, audio_afe_io_block_t audio_block_in, audio_afe_io_block_t audio_block_out);
#if 0//modify for ab1568
bool hal_audio_afe_set_intf_connection_state(audio_interconnection_state_t connection_state, afe_stream_channel_t stream_channel, audio_afe_io_block_t audio_block_in, audio_afe_io_block_t audio_block_out);
#endif
bool hal_audio_afe_set_connection(void *audio_param, bool is_input, bool enable);
audio_digital_block_t hal_audio_afe_get_memory_digital_block (hal_audio_memory_t memory, bool is_downlink);
#endif

#endif //#ifdef HAL_AUDIO_MODULE_ENABLED
#endif /* __HAL_AUDIO_AFE_CONNECTION_H__ */
