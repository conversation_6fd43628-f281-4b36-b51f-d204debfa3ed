/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HAL_AUDIO_REGISTER_H__
#define __HAL_AUDIO_REGISTER_H__

#include <stdint.h>
#include <stdbool.h>
#include "hal_audio.h"
#include "hal_resource_assignment.h"
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Functiion Declaration //////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
void hal_aduio_set_register_32bit(uint32_t addr, uint32_t val, uint32_t msk);
void hal_aduio_set_register_16bit(uint32_t addr, uint32_t val, uint32_t msk);
void hal_aduio_set_register_8bit(uint32_t addr, uint32_t val, uint32_t msk);

#if 1
#define AFE_READ(addr)                  *((volatile uint32_t *)(addr))
#define AFE_WRITE(addr, val)            *((volatile uint32_t *)(addr)) = val
#define AFE_SET_REG(addr, val, msk)     hal_aduio_set_register_32bit(addr, val, msk)//AFE_WRITE((addr), ((AFE_READ(addr) & (~(msk))) | ((val) & (msk))))
#define AFE_GET_REG(addr)               AFE_READ(addr)

#define ANA_READ(addr)                  *((volatile uint16_t *)(addr))
#define ANA_WRITE(addr, val)            *((volatile uint16_t *)(addr)) = val
#define ANA_SET_REG(addr, val, msk)     hal_aduio_set_register_16bit(addr, val, msk)//ANA_WRITE((addr), ((ANA_READ(addr) & (~(msk))) | ((val) & (msk))))
#define ANA_GET_REG(addr)               ANA_READ(addr)

#define AFE_READ8(addr)                 *((volatile uint8_t *)(addr))
#define AFE_WRITE8(addr, val)           *((volatile uint8_t *)(addr)) = val
#else
//For driver debug log
#include "log.h"
#define AFE_READ(addr)                  *((volatile uint32_t *)(addr))
#define AFE_WRITE(addr, val)            *((volatile uint32_t *)(addr)) = val
#define AFE_SET_REG(addr, val, msk)     AFE_WRITE((addr), ((AFE_READ(addr) & (~(msk))) | ((val) & (msk))))
#define AFE_GET_REG(addr)               AFE_READ(addr)

#define ANA_READ(addr)                  *((volatile uint16_t *)(addr))
#define ANA_WRITE(addr, val)            *((volatile uint16_t *)(addr)) = val
#define ANA_SET_REG(addr, val, msk)     LOG_PRINT_AUDIO("hal_audio Set RG:0x%x, 4, value:0x%x ReadValue:0x%x \n\r", addr, ((val) & (msk)), ANA_READ(addr)); \
                                        ANA_WRITE((addr), ((ANA_READ(addr) & (~(msk))) | ((val) & (msk))))
#define ANA_GET_REG(addr)               ANA_READ(addr)

#define AFE_READ8(addr)                 *((volatile uint8_t *)(addr))
#define AFE_WRITE8(addr, val)           *((volatile uint8_t *)(addr)) = val

#endif


#ifndef AFE_BASE
#define AFE_BASE            (0xC0000000)
#endif
#ifndef I2S_DMA_BASE
#define I2S_DMA_BASE        (0xC9000000)
#endif
#ifndef AFE_I2S_SLV0_BASE
#define AFE_I2S_SLV0_BASE   (0xC9020000)
#endif
#ifndef AFE_I2S_SLV1_BASE
#define AFE_I2S_SLV1_BASE   (0xC9030000)
#endif
#ifndef AFE_I2S_SLV2_BASE
#define AFE_I2S_SLV2_BASE   (0xC9040000)
#endif
#ifndef ANC_BASE
#define ANC_BASE            (0xC9050000)
#endif

/*****************************************************************************
 *                  R E G I S T E R       D E F I N I T I O N
 *****************************************************************************/
#define AUDIO_TOP_CON0                  (AFE_BASE + 0x0000)
#define AUDIO_TOP_CON0_PDN_AFE_POS                  (2)
#define AUDIO_TOP_CON0_PDN_AFE_MASK                 (1<<AUDIO_TOP_CON0_PDN_AFE_POS)
#define AUDIO_TOP_CON0_PDN_I2S_SLV_HCLK_POS         (6)
#define AUDIO_TOP_CON0_PDN_I2S_SLV_HCLK_MASK        (1<<AUDIO_TOP_CON0_PDN_I2S_SLV_HCLK_POS)
#define AUDIO_TOP_CON0_PDN_OL_CLD_POS               (7)
#define AUDIO_TOP_CON0_PDN_OL_CLD_MASK              (1<<AUDIO_TOP_CON0_PDN_OL_CLD_POS)
#define AUDIO_TOP_CON0_PDN_22M_POS                  (8)
#define AUDIO_TOP_CON0_PDN_22M_MASK                 (1<<AUDIO_TOP_CON0_PDN_22M_POS)
#define AUDIO_TOP_CON0_PDN_24M_POS                  (9)
#define AUDIO_TOP_CON0_PDN_24M_MASK                 (1<<AUDIO_TOP_CON0_PDN_24M_POS)
#define AUDIO_TOP_CON0_APB_W2T_POS                  (12)
#define AUDIO_TOP_CON0_APB_W2T_MASK                 (1<<AUDIO_TOP_CON0_APB_W2T_POS)
#define AUDIO_TOP_CON0_APB_R2T_POS                  (13)
#define AUDIO_TOP_CON0_APB_R2T_MASK                 (1<<AUDIO_TOP_CON0_APB_R2T_POS)
#define AUDIO_TOP_CON0_APB3_SEL_POS                 (14)
#define AUDIO_TOP_CON0_APB3_SEL_MASK                (1<<AUDIO_TOP_CON0_APB3_SEL_POS)
#define AUDIO_TOP_CON0_PDN_NLE_POS                  (15)
#define AUDIO_TOP_CON0_PDN_NLE_MASK                 (1<<AUDIO_TOP_CON0_PDN_NLE_POS)
#define AUDIO_TOP_CON0_PDN_APLL2_TUNER_POS          (18)
#define AUDIO_TOP_CON0_PDN_APLL2_TUNER_MASK         (1<<AUDIO_TOP_CON0_PDN_APLL2_TUNER_POS)
#define AUDIO_TOP_CON0_PDN_APLL_TUNER_POS           (19)
#define AUDIO_TOP_CON0_PDN_APLL_TUNER_MASK          (1<<AUDIO_TOP_CON0_PDN_APLL_TUNER_POS)
#define AUDIO_TOP_CON0_PDN_ADC3_POS                 (22)
#define AUDIO_TOP_CON0_PDN_ADC3_MASK                (1<<AUDIO_TOP_CON0_PDN_ADC3_POS)
#define AUDIO_TOP_CON0_PDN_ADC2_POS                 (23)
#define AUDIO_TOP_CON0_PDN_ADC2_MASK                (1<<AUDIO_TOP_CON0_PDN_ADC2_POS)
#define AUDIO_TOP_CON0_PDN_ADC_POS                  (24)
#define AUDIO_TOP_CON0_PDN_ADC_MASK                 (1<<AUDIO_TOP_CON0_PDN_ADC_POS)
#define AUDIO_TOP_CON0_PDN_DAC_POS                  (25)
#define AUDIO_TOP_CON0_PDN_DAC_MASK                 (1<<AUDIO_TOP_CON0_PDN_DAC_POS)
#define AUDIO_TOP_CON0_PDN_DAC_PREDIS_POS           (26)
#define AUDIO_TOP_CON0_PDN_DAC_PREDIS_MASK          (1<<AUDIO_TOP_CON0_PDN_DAC_PREDIS_POS)
#define AUDIO_TOP_CON0_PDN_TML_POS                  (27)
#define AUDIO_TOP_CON0_PDN_TML_MASK                 (1<<AUDIO_TOP_CON0_PDN_TML_POS)
#define AUDIO_TOP_CON0_PDN_CLASSG_POS               (28)
#define AUDIO_TOP_CON0_PDN_CLASSG_MASK              (1<<AUDIO_TOP_CON0_PDN_CLASSG_POS)
#define AUDIO_TOP_CON0_AHB_IDLE_EN_EXT_POS          (29)
#define AUDIO_TOP_CON0_AHB_IDLE_EN_EXT_MASK         (1<<AUDIO_TOP_CON0_AHB_IDLE_EN_EXT_POS)
#define AUDIO_TOP_CON0_AHB_IDLE_EN_INT_POS          (30)
#define AUDIO_TOP_CON0_AHB_IDLE_EN_INT_MASK         (1<<AUDIO_TOP_CON0_AHB_IDLE_EN_INT_POS)
#define AUDIO_TOP_CON0_PDN_ALL_MASK                 (0x3FCCC344)
#define AUDIO_TOP_CON1                  (AFE_BASE + 0x0004)
#define AUDIO_TOP_CON1_PDN_I2S0_POS                 (0)
#define AUDIO_TOP_CON1_PDN_I2S0_MASK                (1<<AUDIO_TOP_CON1_PDN_I2S0_POS)
#define AUDIO_TOP_CON1_PDN_I2S1_POS                 (1)
#define AUDIO_TOP_CON1_PDN_I2S1_MASK                (1<<AUDIO_TOP_CON1_PDN_I2S1_POS)
#define AUDIO_TOP_CON1_PDN_I2S2_POS                 (2)
#define AUDIO_TOP_CON1_PDN_I2S2_MASK                (1<<AUDIO_TOP_CON1_PDN_I2S2_POS)
#define AUDIO_TOP_CON1_PDN_I2S3_POS                 (3)
#define AUDIO_TOP_CON1_PDN_I2S3_MASK                (1<<AUDIO_TOP_CON1_PDN_I2S3_POS)
#define AUDIO_TOP_CON1_PDN_I2S_DMA_POS              (4)
#define AUDIO_TOP_CON1_PDN_I2S_DMA_MASK             (1<<AUDIO_TOP_CON1_PDN_I2S_DMA_POS)
#define AUDIO_TOP_CON1_PDN_I2S_DMA_IRQ_POS          (5)
#define AUDIO_TOP_CON1_PDN_I2S_DMA_IRQ_MASK         (1<<AUDIO_TOP_CON1_PDN_I2S_DMA_IRQ_POS)
#define AUDIO_TOP_CON1_PDN_ADC_HIRES_POS            (16)
#define AUDIO_TOP_CON1_PDN_ADC_HIRES_MASK           (1<<AUDIO_TOP_CON1_PDN_ADC_HIRES_POS)
#define AUDIO_TOP_CON1_PDN_ADC_HIRES_TML_POS        (17)
#define AUDIO_TOP_CON1_PDN_ADC_HIRES_TML_MASK       (1<<AUDIO_TOP_CON1_PDN_ADC_HIRES_TML_POS)
#define AUDIO_TOP_CON1_PDN_ADDA2_POS                (19)
#define AUDIO_TOP_CON1_PDN_ADDA2_MASK               (1<<AUDIO_TOP_CON1_PDN_ADDA2_POS)
#define AUDIO_TOP_CON1_PDN_ADDA6_POS                (20)
#define AUDIO_TOP_CON1_PDN_ADDA6_MASK               (1<<AUDIO_TOP_CON1_PDN_ADDA6_POS)
#define AUDIO_TOP_CON1_PDN_ADDA_ANC_POS             (22)
#define AUDIO_TOP_CON1_PDN_ADDA_ANC_MASK            (1<<AUDIO_TOP_CON1_PDN_ADDA_ANC_POS)
#define AUDIO_TOP_CON1_PDN_DAC_HIRES_POS            (23)
#define AUDIO_TOP_CON1_PDN_DAC_HIRES_MASK           (1<<AUDIO_TOP_CON1_PDN_DAC_HIRES_POS)
#define AUDIO_TOP_CON1_PDN_ASRC1_POS                (28)
#define AUDIO_TOP_CON1_PDN_ASRC1_MASK               (1<<AUDIO_TOP_CON1_PDN_ASRC1_POS)
#define AUDIO_TOP_CON1_PDN_ASRC2_POS                (29)
#define AUDIO_TOP_CON1_PDN_ASRC2_MASK               (1<<AUDIO_TOP_CON1_PDN_ASRC2_POS)
#define AUDIO_TOP_CON1_PDN_DRAM_BRIDGE_POS          (31)
#define AUDIO_TOP_CON1_PDN_DRAM_BRIDGE_MASK         (1<<AUDIO_TOP_CON1_PDN_DRAM_BRIDGE_POS)
#define AUDIO_TOP_CON1_PDN_ALL_MASK                 (0xB0DB003F)
#define AUDIO_TOP_CON2                  (AFE_BASE + 0x0008)
#define AUDIO_TOP_CON2_ADC01_INV_POS                (0)
#define AUDIO_TOP_CON2_ADC01_INV_MASK               (1<<AUDIO_TOP_CON2_ADC01_INV_POS)
#define AUDIO_TOP_CON2_ADC23_INV_POS                (1)
#define AUDIO_TOP_CON2_ADC23_INV_MASK               (1<<AUDIO_TOP_CON2_ADC23_INV_POS)
#define AUDIO_TOP_CON2_ADC45_INV_POS                (2)
#define AUDIO_TOP_CON2_ADC45_INV_MASK               (1<<AUDIO_TOP_CON2_ADC45_INV_POS)
#define AUDIO_TOP_CON3                  (AFE_BASE + 0x000C)
#define AFE_DAC_CON0                    (AFE_BASE + 0x0010)
#define AFE_DAC_CON0_AFE_ON_POS                     (0)
#define AFE_DAC_CON0_AFE_ON_MASK                    (1<<AFE_DAC_CON0_AFE_ON_POS)
#define AFE_DAC_CON0_DL1_ON_POS                     (1)
#define AFE_DAC_CON0_DL1_ON_MASK                    (1<<AFE_DAC_CON0_DL1_ON_POS)
#define AFE_DAC_CON0_DL2_ON_POS                     (2)
#define AFE_DAC_CON0_DL2_ON_MASK                    (1<<AFE_DAC_CON0_DL2_ON_POS)
#define AFE_DAC_CON0_VUL_ON_POS                     (3)
#define AFE_DAC_CON0_VUL_ON_MASK                    (1<<AFE_DAC_CON0_VUL_ON_POS)
#define AFE_DAC_CON0_DL3_ON_POS                     (5)
#define AFE_DAC_CON0_DL3_ON_MASK                    (1<<AFE_DAC_CON0_DL3_ON_POS)
#define AFE_DAC_CON0_AWB_ON_POS                     (6)
#define AFE_DAC_CON0_AWB_ON_MASK                    (1<<AFE_DAC_CON0_AWB_ON_POS)
#define AFE_DAC_CON0_DL12_ON_POS                    (8)
#define AFE_DAC_CON0_DL12_ON_MASK                   (1<<AFE_DAC_CON0_DL12_ON_POS)
#define AFE_DAC_CON0_VUL3_ON_POS                    (9)
#define AFE_DAC_CON0_VUL3_ON_MASK                   (1<<AFE_DAC_CON0_VUL3_ON_POS)
#define AFE_DAC_CON0_DL12_RATE_POS                  (16)
#define AFE_DAC_CON0_DL12_RATE_MASK                 (0xF<<AFE_DAC_CON0_DL12_RATE_POS)
#define AFE_DAC_CON0_VUL2_ON_POS                    (27)
#define AFE_DAC_CON0_VUL2_ON_MASK                   (1<<AFE_DAC_CON0_VUL2_ON_POS)
#define AFE_DAC_CON0_AWB2_ON_POS                    (29)
#define AFE_DAC_CON0_AWB2_ON_MASK                   (1<<AFE_DAC_CON0_AWB2_ON_POS)
#define AFE_DAC_CON1                    (AFE_BASE + 0x0014)
#define AFE_CONN0                       (AFE_BASE + 0x0020)
#define AFE_CONN1                       (AFE_BASE + 0x0024)
#define AFE_CONN2                       (AFE_BASE + 0x0028)
#define AFE_CONN3                       (AFE_BASE + 0x002C)
#define AFE_CONN4                       (AFE_BASE + 0x0030)
#define AFE_DL1_BASE                    (AFE_BASE + 0x0040)
#define AFE_DL1_CUR                     (AFE_BASE + 0x0044)
#define AFE_DL1_END                     (AFE_BASE + 0x0048)
#define AFE_DL2_BASE                    (AFE_BASE + 0x0050)
#define AFE_DL2_CUR                     (AFE_BASE + 0x0054)
#define AFE_DL2_END                     (AFE_BASE + 0x0058)
#define AFE_CONN5                       (AFE_BASE + 0x005C)
#define AFE_CONN_24BIT                  (AFE_BASE + 0x006C)
#define AFE_AWB_BASE                    (AFE_BASE + 0x0070)
#define AFE_AWB_END                     (AFE_BASE + 0x0078)
#define AFE_AWB_CUR                     (AFE_BASE + 0x007C)
#define AFE_VUL_BASE                    (AFE_BASE + 0x0080)
#define AFE_VUL_END                     (AFE_BASE + 0x0088)
#define AFE_VUL_CUR                     (AFE_BASE + 0x008C)
#define AFE_CONN6                       (AFE_BASE + 0x00BC)
#define AFE_MEMIF_MSB                   (AFE_BASE + 0x00CC)
#define AFE_MEMIF_MON0                  (AFE_BASE + 0x00d0)
#define AFE_MEMIF_MON1                  (AFE_BASE + 0x00d4)
#define AFE_MEMIF_MON2                  (AFE_BASE + 0x00d8)
#define AFE_MEMIF_MON3                  (AFE_BASE + 0x00dc)
#define AFE_MEMIF_MON4                  (AFE_BASE + 0x00e0)
#define AFE_MEMIF_MON5                  (AFE_BASE + 0x00e4)
#define AFE_MEMIF_MON6                  (AFE_BASE + 0x00e8)
#define AFE_MEMIF_MON7                  (AFE_BASE + 0x00ec)
#define AFE_MEMIF_MON8                  (AFE_BASE + 0x00f0)
#define AFE_MEMIF_MON9                  (AFE_BASE + 0x00f4)
#define AFE_ADDA_DL_SRC2_CON0           (AFE_BASE + 0x0108)
#define AFE_ADDA_DL_SRC2_CON0_DL_2_SRC_ON_TMP_CTL_PRE_POS   (0)
#define AFE_ADDA_DL_SRC2_CON0_DL_2_SRC_ON_TMP_CTL_PRE_MASK  (1<<AFE_ADDA_DL_SRC2_CON0_DL_2_SRC_ON_TMP_CTL_PRE_POS)
#define AFE_ADDA_DL_SRC2_CON0_DL_2_GAIN_ON_CTL_PRE_POS      (1)
#define AFE_ADDA_DL_SRC2_CON0_DL_2_GAIN_ON_CTL_PRE_MASK     (1<<AFE_ADDA_DL_SRC2_CON0_DL_2_GAIN_ON_CTL_PRE_POS)
#define AFE_ADDA_DL_SRC2_CON0_DL_2_OUTPUT_SEL_CTL_POS       (24)
#define AFE_ADDA_DL_SRC2_CON0_DL_2_OUTPUT_SEL_CTL_MASK      (3<<AFE_ADDA_DL_SRC2_CON0_DL_2_OUTPUT_SEL_CTL_POS)
#define AFE_ADDA_DL_SRC2_CON0_DL_2_INPUT_MODE_CTL_POS       (28)
#define AFE_ADDA_DL_SRC2_CON0_DL_2_INPUT_MODE_CTL_MASK      (15<<AFE_ADDA_DL_SRC2_CON0_DL_2_INPUT_MODE_CTL_POS)
#define AFE_ADDA_DL_SRC2_CON0_DL_SRC_ON_POS         (0)
#define AFE_ADDA_DL_SRC2_CON0_DL_SRC_ON_MASK        (1<<AFE_ADDA_DL_SRC2_CON0_DL_SRC_ON_POS)
#define AFE_ADDA_DL_SRC2_CON0_DL_ANC_2_SDM_KEEP_ON_POS (18)
#define AFE_ADDA_DL_SRC2_CON0_DL_ANC_2_SDM_KEEP_ON_MASK (1<<AFE_ADDA_DL_SRC2_CON0_DL_ANC_2_SDM_KEEP_ON_POS)
#define AFE_ADDA_DL_SRC2_CON0_RATE_POS              (28)
#define AFE_ADDA_DL_SRC2_CON0_RATE_MASK             (0xF<<AFE_ADDA_DL_SRC2_CON0_RATE_POS)
#define AFE_ADDA_DL_SRC2_CON1           (AFE_BASE + 0x010C)
#define AFE_ADDA_DL_SRC2_CON1_DL_2_GAIN_CTL_PRE_POS     (16)
#define AFE_ADDA_DL_SRC2_CON1_DL_2_GAIN_CTL_PRE_MASK    (0xFFFF<<AFE_ADDA_DL_SRC2_CON1_DL_2_GAIN_CTL_PRE_POS)


#define AFE_ADDA_UL_SRC_CON0            (AFE_BASE + 0x0114)
#define AFE_ADDA_UL_SRC_CON0_ON_POS                 (0)
#define AFE_ADDA_UL_SRC_CON0_ON_MASK                (0x1<<AFE_ADDA_UL_SRC_CON0_ON_POS)
#define AFE_ADDA_UL_SRC_CON0_SDM3_FOR_DMIC_POS      (1)
#define AFE_ADDA_UL_SRC_CON0_SDM3_FOR_DMIC_MASK     (0x1<<AFE_ADDA_UL_SRC_CON0_SDM3_FOR_DMIC_POS)
#define AFE_ADDA_UL_SRC_CON0_DA_LOOPBACK_POS        (2)
#define AFE_ADDA_UL_SRC_CON0_DA_LOOPBACK_MASK       (0x1<<AFE_ADDA_UL_SRC_CON0_DA_LOOPBACK_POS)
#define AFE_ADDA_UL_SRC_CON0_IIR_POS                (10)
#define AFE_ADDA_UL_SRC_CON0_IIR_MASK               (0x1<<AFE_ADDA_UL_SRC_CON0_IIR_POS)
#define AFE_ADDA_UL_SRC_CON0_IIR_MODE_POS           (7)
#define AFE_ADDA_UL_SRC_CON0_IIR_MODE_MASK          (0x7<<AFE_ADDA_UL_SRC_CON0_IIR_MODE_POS)
#define AFE_ADDA_UL_SRC_CON0_DMIC_SEL_CTRL_POS      (5)
#define AFE_ADDA_UL_SRC_CON0_DMIC_SEL_CTRL_MASK     (0x1<<AFE_ADDA_UL_SRC_CON0_DMIC_SEL_CTRL_POS)
#define AFE_ADDA_UL_SRC_CON0_DMIC_CLK_RATE_POS      (14)
#define AFE_ADDA_UL_SRC_CON0_DMIC_CLK_RATE_MASK     (0x3<<AFE_ADDA_UL_SRC_CON0_DMIC_CLK_RATE_POS)
#define AFE_ADDA_UL_SRC_CON0_RATE_POS               (17)
#define AFE_ADDA_UL_SRC_CON0_RATE_MASK              (0x7<<AFE_ADDA_UL_SRC_CON0_RATE_POS)
#define AFE_ADDA_UL_SRC_CON0_DMIC_CH1_POS           (21)
#define AFE_ADDA_UL_SRC_CON0_DMIC_CH1_MASK          (0x1<<AFE_ADDA_UL_SRC_CON0_DMIC_CH1_POS)
#define AFE_ADDA_UL_SRC_CON0_DMIC_CH2_POS           (22)
#define AFE_ADDA_UL_SRC_CON0_DMIC_CH2_MASK          (0x1<<AFE_ADDA_UL_SRC_CON0_DMIC_CH2_POS)
#define AFE_ADDA_UL_SRC_CON0_DMIC_PHASE_CH2_POS     (24)
#define AFE_ADDA_UL_SRC_CON0_DMIC_PHASE_CH2_MASK    (0x7<<AFE_ADDA_UL_SRC_CON0_DMIC_PHASE_CH2_POS)
#define AFE_ADDA_UL_SRC_CON0_DMIC_PHASE_CH1_POS     (27)
#define AFE_ADDA_UL_SRC_CON0_DMIC_PHASE_CH1_MASK    (0x7<<AFE_ADDA_UL_SRC_CON0_DMIC_PHASE_CH1_POS)

#define AFE_ADDA_UL_SRC_CON1            (AFE_BASE + 0x0118)
#define AFE_ADDA_TOP_CON0               (AFE_BASE + 0x0120)
#define AFE_ADDA_UL_DL_CON0             (AFE_BASE + 0x0124)
#define AFE_ADDA_UL_DL_CON0_ADDA_AFE_ON_POS         (0)
#define AFE_ADDA_UL_DL_CON0_ADDA_AFE_ON_MASK        (1<<AFE_ADDA_UL_DL_CON0_ADDA_AFE_ON_POS)


#define AFE_ADDA_UL_DL_CON0_ADDA_ON_POS             (0)
#define AFE_ADDA_UL_DL_CON0_ADDA_ON_MASK            (1<<AFE_ADDA_UL_DL_CON0_ADDA_ON_POS)
#define AFE_ADDA_UL_DL_CON0_ADDA6_FIFO_RST_POS      (13)
#define AFE_ADDA_UL_DL_CON0_ADDA6_FIFO_RST_MASK     (1<<AFE_ADDA_UL_DL_CON0_ADDA6_FIFO_RST_POS)
#define AFE_ADDA_UL_DL_CON0_ADDA6_SWAP_POS          (15)
#define AFE_ADDA_UL_DL_CON0_ADDA6_SWAP_MASK         (1<<AFE_ADDA_UL_DL_CON0_ADDA6_SWAP_POS)
#define AFE_ADDA_UL_DL_CON0_ADDA2_FIFO_RST_POS      (21)
#define AFE_ADDA_UL_DL_CON0_ADDA2_FIFO_RST_MASK     (1<<AFE_ADDA_UL_DL_CON0_ADDA2_FIFO_RST_POS)
#define AFE_ADDA_UL_DL_CON0_ADDA2_SWAP_POS          (23)
#define AFE_ADDA_UL_DL_CON0_ADDA2_SWAP_MASK         (1<<AFE_ADDA_UL_DL_CON0_ADDA2_SWAP_POS)
#define AFE_ADDA_UL_DL_CON0_ADDA_FIFO_RST_POS       (29)
#define AFE_ADDA_UL_DL_CON0_ADDA_FIFO_RST_MASK      (1<<AFE_ADDA_UL_DL_CON0_ADDA_FIFO_RST_POS)
#define AFE_ADDA_UL_DL_CON0_ADDA_SWAP_POS           (31)
#define AFE_ADDA_UL_DL_CON0_ADDA_SWAP_MASK          (1<<AFE_ADDA_UL_DL_CON0_ADDA_SWAP_POS)

#define AFE_ADDA_SRC_DEBUG              (AFE_BASE + 0x012C)
#define AFE_ADDA_SRC_DEBUG_MON0         (AFE_BASE + 0x0130)
#define AFE_ADDA_SRC_DEBUG_MON1         (AFE_BASE + 0x0134)
#define AFE_ADDA_UL_SRC_MON0            (AFE_BASE + 0x0148)
#define AFE_ADDA_UL_SRC_MON1            (AFE_BASE + 0x014C)
#define AFE_SIDETONE_DEBUG              (AFE_BASE + 0x01D0)
#define AFE_SIDETONE_DEBUG_SRC_SEL_POS              (16)
#define AFE_SIDETONE_DEBUG_SRC_SEL_MASK             (7<<AFE_SIDETONE_DEBUG_SRC_SEL_POS)
#define AFE_SIDETONE_DEBUG_SEL_CTRL_POS             (20)
#define AFE_SIDETONE_DEBUG_SEL_CTRL_MASK            (1<<AFE_SIDETONE_DEBUG_SEL_CTRL_POS)
#define AFE_SIDETONE_MON                (AFE_BASE + 0x01D4)
#define AFE_SINEGEN_CON2                (AFE_BASE + 0x01DC)
#define AFE_SIDETONE_CON0               (AFE_BASE + 0x01E0)
#define AFE_SIDETONE_CON0_COEFFICIENT_VALUE_POS     (0)
#define AFE_SIDETONE_CON0_COEFFICIENT_VALUE_MASK    (0xFFFF<<AFE_SIDETONE_CON0_COEFFICIENT_VALUE_POS)
#define AFE_SIDETONE_CON0_COEFFICIENT_NUMBER_POS    (16)
#define AFE_SIDETONE_CON0_COEFFICIENT_NUMBER_MASK   (0x1F<<AFE_SIDETONE_CON0_COEFFICIENT_NUMBER_POS)
#define AFE_SIDETONE_CON0_CHANNEL_SEL_POS           (23)
#define AFE_SIDETONE_CON0_CHANNEL_SEL_MASK          (1<<AFE_SIDETONE_CON0_CHANNEL_SEL_POS)
#define AFE_SIDETONE_CON0_READ_WRITE_SEL_POS        (24)
#define AFE_SIDETONE_CON0_READ_WRITE_SEL_MASK       (1<<AFE_SIDETONE_CON0_READ_WRITE_SEL_POS)
#define AFE_SIDETONE_CON0_READ_WRITE_ENABLE_POS     (25)
#define AFE_SIDETONE_CON0_READ_WRITE_ENABLE_MASK    (1<<AFE_SIDETONE_CON0_READ_WRITE_ENABLE_POS)
#define AFE_SIDETONE_CON0_WRITE_READY_POS           (29)
#define AFE_SIDETONE_CON0_WRITE_READY_MASK          (1<<AFE_SIDETONE_CON0_WRITE_READY_POS)
#define AFE_SIDETONE_CON0_READ_READY_POS            (30)
#define AFE_SIDETONE_CON0_READ_READY_MASK           (1<<AFE_SIDETONE_CON0_READ_READY_POS)
#define AFE_SIDETONE_COEFF              (AFE_BASE + 0x01E4)
#define AFE_SIDETONE_CON1               (AFE_BASE + 0x01E8)
#define AFE_SIDETONE_CON1_HALF_TAP_NUM_POS          (0)
#define AFE_SIDETONE_CON1_HALF_TAP_NUM_MASK         (0x3F<<AFE_SIDETONE_CON1_HALF_TAP_NUM_POS)
#define AFE_SIDETONE_CON1_ENABLE_POS                (8)
#define AFE_SIDETONE_CON1_ENABLE_MASK               (1<<AFE_SIDETONE_CON1_ENABLE_POS)
#define AFE_SIDETONE_CON1_INTO_I2S0_SLAVE_POS       (9)
#define AFE_SIDETONE_CON1_INTO_I2S0_SLAVE_MASK      (1<<AFE_SIDETONE_CON1_INTO_I2S0_SLAVE_POS)
#define AFE_SIDETONE_CON1_INTO_I2S1_SLAVE_POS       (10)
#define AFE_SIDETONE_CON1_INTO_I2S1_SLAVE_MASK      (1<<AFE_SIDETONE_CON1_INTO_I2S1_SLAVE_POS)
#define AFE_SIDETONE_CON1_INTO_I2S2_SLAVE_POS       (11)
#define AFE_SIDETONE_CON1_INTO_I2S2_SLAVE_MASK      (1<<AFE_SIDETONE_CON1_INTO_I2S2_SLAVE_POS)
#define AFE_SIDETONE_CON1_INTO_STF_ON_POS           (16)
#define AFE_SIDETONE_CON1_INTO_STF_ON_MASK          (1<<AFE_SIDETONE_CON1_INTO_STF_ON_POS)
#define AFE_SIDETONE_CON1_INTO_I2S0_MASTER_POS      (24)
#define AFE_SIDETONE_CON1_INTO_I2S0_MASTER_MASK     (1<<AFE_SIDETONE_CON1_INTO_I2S0_MASTER_POS)
#define AFE_SIDETONE_CON1_INTO_I2S1_MASTER_POS      (25)
#define AFE_SIDETONE_CON1_INTO_I2S1_MASTER_MASK     (1<<AFE_SIDETONE_CON1_INTO_I2S1_MASTER_POS)
#define AFE_SIDETONE_CON1_INTO_I2S2_MASTER_POS      (26)
#define AFE_SIDETONE_CON1_INTO_I2S2_MASTER_MASK     (1<<AFE_SIDETONE_CON1_INTO_I2S2_MASTER_POS)
#define AFE_SIDETONE_CON1_INTO_I2S3_MASTER_POS      (27)
#define AFE_SIDETONE_CON1_INTO_I2S3_MASTER_MASK     (1<<AFE_SIDETONE_CON1_INTO_I2S3_MASTER_POS)
#define AFE_SIDETONE_CON1_INTO_DAC_POS              (28)
#define AFE_SIDETONE_CON1_INTO_DAC_MASK             (1<<AFE_SIDETONE_CON1_INTO_DAC_POS)
#define AFE_SIDETONE_CON1_SET_96K_POS               (29)
#define AFE_SIDETONE_CON1_SET_96K_MASK              (1<<AFE_SIDETONE_CON1_SET_96K_POS)
#define AFE_SIDETONE_CON1_OUTPUT_MASK               (0x1F000E00)
#define AFE_SIDETONE_GAIN               (AFE_BASE + 0x01EC)
#define AFE_SINEGEN_CON0                (AFE_BASE + 0x01F0)
#define AFE_BUS_CFG                     (AFE_BASE + 0x0240)
#define AFE_BUS_MON0                    (AFE_BASE + 0x0244)
#define AFE_ADDA_PREDIS_CON0            (AFE_BASE + 0x0260)
#define AFE_ADDA_PREDIS_CON0_PREDIS_A3_CH1_CTL_POS  (0)
#define AFE_ADDA_PREDIS_CON0_PREDIS_A3_CH1_CTL_MASK (4095<<AFE_ADDA_PREDIS_CON0_PREDIS_A3_CH1_CTL_POS)
#define AFE_ADDA_PREDIS_CON0_PREDIS_A2_CH1_CTL_POS  (16)
#define AFE_ADDA_PREDIS_CON0_PREDIS_A2_CH1_CTL_MASK (4095<<AFE_ADDA_PREDIS_CON0_PREDIS_A2_CH1_CTL_POS)
#define AFE_ADDA_PREDIS_CON0_PREDIS_ON_CH1_CTL_POS  (31)
#define AFE_ADDA_PREDIS_CON0_PREDIS_ON_CH1_CTL_MASK (1<<AFE_ADDA_PREDIS_CON0_PREDIS_ON_CH1_CTL_POS)
#define AFE_ADDA_PREDIS_CON1            (AFE_BASE + 0x0264)
#define AFE_ADDA_PREDIS_CON1_PREDIS_A3_CH2_CTL_POS  (0)
#define AFE_ADDA_PREDIS_CON1_PREDIS_A3_CH2_CTL_MASK (4095<<AFE_ADDA_PREDIS_CON1_PREDIS_A3_CH2_CTL_POS)
#define AFE_ADDA_PREDIS_CON1_PREDIS_A2_CH2_CTL_POS  (16)
#define AFE_ADDA_PREDIS_CON1_PREDIS_A2_CH2_CTL_MASK (4095<<AFE_ADDA_PREDIS_CON1_PREDIS_A2_CH2_CTL_POS)
#define AFE_ADDA_PREDIS_CON1_PREDIS_ON_CH2_CTL_POS  (31)
#define AFE_ADDA_PREDIS_CON1_PREDIS_ON_CH2_CTL_MASK (1<<AFE_ADDA_PREDIS_CON1_PREDIS_ON_CH2_CTL_POS)
#define AFE_ADDA_IIR_COEF_02_01         (AFE_BASE + 0x0290)
#define AFE_ADDA_IIR_COEF_04_03         (AFE_BASE + 0x0294)
#define AFE_ADDA_IIR_COEF_06_05         (AFE_BASE + 0x0298)
#define AFE_ADDA_IIR_COEF_08_07         (AFE_BASE + 0x029C)
#define AFE_ADDA_IIR_COEF_10_09         (AFE_BASE + 0x02A0)
#define AFE_DAC_CON2                    (AFE_BASE + 0x02E0)
#define AFE_IRQ_MCU_CON1                (AFE_BASE + 0x02E4)
#define AFE_IRQ_MCU_CON2                (AFE_BASE + 0x02E8)
#define AFE_DAC_MON                     (AFE_BASE + 0x02Ec)
#define AFE_VUL2_BASE                   (AFE_BASE + 0x02F0)
#define AFE_VUL2_END                    (AFE_BASE + 0x02F8)
#define AFE_VUL2_CUR                    (AFE_BASE + 0x02FC)
#define AFE_IRQ_MCU_CNT0                (AFE_BASE + 0x0300)
#define AFE_IRQ_MCU_CNT6                (AFE_BASE + 0x0304)
#define AFE_IRQ_MCU_EN1                 (AFE_BASE + 0x030C)
#define AFE_IRQ0_MCU_CNT_MON            (AFE_BASE + 0x0310)
#define AFE_IRQ6_MCU_CNT_MON            (AFE_BASE + 0x0314)
#define AFE_DL12_BASE                   (AFE_BASE + 0x0340)
#define AFE_DL12_CUR                    (AFE_BASE + 0x0344)
#define AFE_DL12_END                    (AFE_BASE + 0x0348)
#define AFE_DL3_BASE                    (AFE_BASE + 0x0360)
#define AFE_DL3_CUR                     (AFE_BASE + 0x0364)
#define AFE_DL3_END                     (AFE_BASE + 0x0368)
#define AFE_IRQ3_MCU_CNT_MON            (AFE_BASE + 0x0398)
#define AFE_IRQ4_MCU_CNT_MON            (AFE_BASE + 0x039C)
#define AFE_IRQ_MCU_CON0                (AFE_BASE + 0x03A0)
#define AFE_IRQ_MCU_CON0_IRQ0_POS                   (0)
#define AFE_IRQ_MCU_CON0_IRQ0_MASK                  (1<<AFE_IRQ_MCU_CON0_IRQ0_POS)
#define AFE_IRQ_MCU_CON0_IRQ1_POS                   (1)
#define AFE_IRQ_MCU_CON0_IRQ1_MASK                  (1<<AFE_IRQ_MCU_CON0_IRQ1_POS)
#define AFE_IRQ_MCU_CON0_IRQ2_POS                   (2)
#define AFE_IRQ_MCU_CON0_IRQ2_MASK                  (1<<AFE_IRQ_MCU_CON0_IRQ2_POS)
#define AFE_IRQ_MCU_CON0_IRQ3_POS                   (3)
#define AFE_IRQ_MCU_CON0_IRQ3_MASK                  (1<<AFE_IRQ_MCU_CON0_IRQ3_POS)
#define AFE_IRQ_MCU_CON0_IRQ4_POS                   (4)
#define AFE_IRQ_MCU_CON0_IRQ4_MASK                  (1<<AFE_IRQ_MCU_CON0_IRQ4_POS)
#define AFE_IRQ_MCU_CON0_IRQ5_POS                   (5)
#define AFE_IRQ_MCU_CON0_IRQ5_MASK                  (1<<AFE_IRQ_MCU_CON0_IRQ5_POS)
#define AFE_IRQ_MCU_CON0_IRQ6_POS                   (6)
#define AFE_IRQ_MCU_CON0_IRQ6_MASK                  (1<<AFE_IRQ_MCU_CON0_IRQ6_POS)
#define AFE_IRQ_MCU_CON0_IRQ7_POS                   (7)
#define AFE_IRQ_MCU_CON0_IRQ7_MASK                  (1<<AFE_IRQ_MCU_CON0_IRQ7_POS)
#define AFE_IRQ_MCU_CON0_IRQ11_POS                  (11)
#define AFE_IRQ_MCU_CON0_IRQ11_MASK                 (1<<AFE_IRQ_MCU_CON0_IRQ11_POS)
#define AFE_IRQ_MCU_CON0_IRQ12_POS                  (12)
#define AFE_IRQ_MCU_CON0_IRQ12_MASK                 (1<<AFE_IRQ_MCU_CON0_IRQ12_POS)
#define AFE_IRQ_MCU_STATUS              (AFE_BASE + 0x03A4)
#define AFE_IRQ_MCU_STATUS_IRQ0_POS                 (0)
#define AFE_IRQ_MCU_STATUS_IRQ0_MASK                (1<<AFE_IRQ_MCU_STATUS_IRQ0_POS)
#define AFE_IRQ_MCU_STATUS_IRQ1_POS                 (1)
#define AFE_IRQ_MCU_STATUS_IRQ1_MASK                (1<<AFE_IRQ_MCU_STATUS_IRQ1_POS)
#define AFE_IRQ_MCU_STATUS_IRQ2_POS                 (2)
#define AFE_IRQ_MCU_STATUS_IRQ2_MASK                (1<<AFE_IRQ_MCU_STATUS_IRQ2_POS)
#define AFE_IRQ_MCU_STATUS_IRQ3_POS                 (3)
#define AFE_IRQ_MCU_STATUS_IRQ3_MASK                (1<<AFE_IRQ_MCU_STATUS_IRQ3_POS)
#define AFE_IRQ_MCU_STATUS_IRQ4_POS                 (4)
#define AFE_IRQ_MCU_STATUS_IRQ4_MASK                (1<<AFE_IRQ_MCU_STATUS_IRQ4_POS)
#define AFE_IRQ_MCU_STATUS_IRQ5_POS                 (5)
#define AFE_IRQ_MCU_STATUS_IRQ5_MASK                (1<<AFE_IRQ_MCU_STATUS_IRQ5_POS)
#define AFE_IRQ_MCU_STATUS_IRQ6_POS                 (6)
#define AFE_IRQ_MCU_STATUS_IRQ6_MASK                (1<<AFE_IRQ_MCU_STATUS_IRQ6_POS)
#define AFE_IRQ_MCU_STATUS_IRQ7_POS                 (7)
#define AFE_IRQ_MCU_STATUS_IRQ7_MASK                (1<<AFE_IRQ_MCU_STATUS_IRQ7_POS)
#define AFE_IRQ_MCU_STATUS_ANC_DET_LCH_L_POS        (8)
#define AFE_IRQ_MCU_STATUS_ANC_DET_LCH_L_MASK       (1<<AFE_IRQ_MCU_STATUS_ANC_DET_LCH_L_POS)
#define AFE_IRQ_MCU_STATUS_ANC_DET_LCH_H_POS        (9)
#define AFE_IRQ_MCU_STATUS_ANC_DET_LCH_H_MASK       (1<<AFE_IRQ_MCU_STATUS_ANC_DET_LCH_H_POS)
#define AFE_IRQ_MCU_STATUS_IRQ11_POS                (11)
#define AFE_IRQ_MCU_STATUS_IRQ11_MASK               (1<<AFE_IRQ_MCU_STATUS_IRQ11_POS)
#define AFE_IRQ_MCU_STATUS_IRQ12_POS                (12)
#define AFE_IRQ_MCU_STATUS_IRQ12_MASK               (1<<AFE_IRQ_MCU_STATUS_IRQ12_POS)
#define AFE_IRQ_MCU_STATUS_ANC_DET_RCH_L_POS        (13)
#define AFE_IRQ_MCU_STATUS_ANC_DET_RCH_L_MASK       (1<<AFE_IRQ_MCU_STATUS_ANC_DET_RCH_L_POS)
#define AFE_IRQ_MCU_STATUS_ANC_DET_RCH_H_POS        (14)
#define AFE_IRQ_MCU_STATUS_ANC_DET_RCH_H_MASK       (1<<AFE_IRQ_MCU_STATUS_ANC_DET_RCH_H_POS)
#define AFE_IRQ_MCU_CLR                 (AFE_BASE + 0x03A8)
#define AFE_IRQ_MCU_CNT1                (AFE_BASE + 0x03AC)
#define AFE_IRQ_MCU_CNT2                (AFE_BASE + 0x03B0)
#define AFE_IRQ_MCU_EN                  (AFE_BASE + 0x03B4)
#define AFE_IRQ_MCU_MON2                (AFE_BASE + 0x03B8)
#define AFE_IRQ_MCU_CNT5                (AFE_BASE + 0x03BC)
#define AFE_IRQ1_MCU_CNT_MON            (AFE_BASE + 0x03C0)
#define AFE_IRQ2_MCU_CNT_MON            (AFE_BASE + 0x03C4)
#define AFE_IRQ1_MCU_EN_CNT_MON         (AFE_BASE + 0x03C8)
#define AFE_IRQ5_MCU_CNT_MON            (AFE_BASE + 0x03CC)
#define AFE_MEMIF_MINLEN                (AFE_BASE + 0x03D0)
#define AFE_MEMIF_MAXLEN                (AFE_BASE + 0x03D4)
#define AFE_MEMIF_PBUF_SIZE             (AFE_BASE + 0x03D8)
#define AFE_IRQ_MCU_CNT7                (AFE_BASE + 0x03DC)
#define AFE_IRQ7_MCU_CNT_MON            (AFE_BASE + 0x03E0)
#define AFE_IRQ_MCU_CNT3                (AFE_BASE + 0x03E4)
#define AFE_IRQ_MCU_CNT4                (AFE_BASE + 0x03E8)
#define AFE_IRQ_MCU_CNT11               (AFE_BASE + 0x03EC)
#define AFE_APLL1_TUNER_CFG             (AFE_BASE + 0x03F0)
#define AFE_APLL2_TUNER_CFG             (AFE_BASE + 0x03F4)
#define AFE_MEMIF_HD_MODE               (AFE_BASE + 0x03F8)
#define AFE_MEMIF_HDALIGN               (AFE_BASE + 0x03FC)
#define AFE_IRQ_MCU_CNT12               (AFE_BASE + 0x040C)
#define AFE_GAIN1_CON0                  (AFE_BASE + 0x0410)
#define AFE_GAIN1_CON0_EN_POS                       (0)
#define AFE_GAIN1_CON0_EN_MASK                      (1<<AFE_GAIN1_CON0_EN_POS)
#define AFE_GAIN1_CON0_RATE_POS                     (4)
#define AFE_GAIN1_CON0_RATE_MASK                    (0xF<<AFE_GAIN1_CON0_RATE_POS)
#define AFE_GAIN1_CON0_PER_STEP_POS                 (8)
#define AFE_GAIN1_CON0_PER_STEP_MASK                (0xFF<<AFE_GAIN1_CON0_PER_STEP_POS)
#define AFE_GAIN1_CON0_EXTEND_POS                   (16)
#define AFE_GAIN1_CON0_EXTEND_MASK                  (1<<AFE_GAIN1_CON0_EXTEND_POS)
#define AFE_GAIN1_CON1                  (AFE_BASE + 0x0414)
#define AFE_GAIN1_CON2                  (AFE_BASE + 0x0418)
#define AFE_GAIN1_CON3                  (AFE_BASE + 0x041C)
#define AFE_CONN7                       (AFE_BASE + 0x0420)
#define AFE_GAIN1_CUR                   (AFE_BASE + 0x0424)
#define AFE_GAIN2_CON0                  (AFE_BASE + 0x0428)
#define AFE_GAIN2_CON1                  (AFE_BASE + 0x042C)
#define AFE_GAIN2_CON2                  (AFE_BASE + 0x0430)
#define AFE_GAIN2_CON3                  (AFE_BASE + 0x0434)
#define AFE_CONN8                       (AFE_BASE + 0x0438)
#define AFE_GAIN2_CUR                   (AFE_BASE + 0x043C)
#define AFE_CONN9                       (AFE_BASE + 0x0440)
#define AFE_CONN10                      (AFE_BASE + 0x0444)
#define AFE_CONN11                      (AFE_BASE + 0x0448)
#define AFE_CONN12                      (AFE_BASE + 0x044C)
#define AFE_CONN13                      (AFE_BASE + 0x0450)
#define AFE_CONN14                      (AFE_BASE + 0x0454)
#define AFE_CONN15                      (AFE_BASE + 0x0458)
#define AFE_CONN16                      (AFE_BASE + 0x045C)
#define AFE_CONN17                      (AFE_BASE + 0x0460)
#define AFE_CONN18                      (AFE_BASE + 0x0464)
#define AFE_CONN19                      (AFE_BASE + 0x0468)
#define AFE_CONN20                      (AFE_BASE + 0x046C)
#define AFE_CONN21                      (AFE_BASE + 0x0470)
#define AFE_CONN22                      (AFE_BASE + 0x0474)
#define AFE_CONN23                      (AFE_BASE + 0x0478)
#define AFE_CONN_RS                     (AFE_BASE + 0x0494)
#define AFE_CONN_DI                     (AFE_BASE + 0x0498)
#define AFE_SRAM_DELSEL_CON0            (AFE_BASE + 0x04F0)
#define AFE_SRAM_DELSEL_CON1            (AFE_BASE + 0x04F4)
#define AFE_SRAM_DELSEL_CON2            (AFE_BASE + 0x04F8)
#define FPGA_CFG0                       (AFE_BASE + 0x05B0)
#define FPGA_CFG1                       (AFE_BASE + 0x05B4)
#define FPGA_CFG2                       (AFE_BASE + 0x05C0)
#define FPGA_CFG3                       (AFE_BASE + 0x05C4)
#define AUDIO_TOP_DBG_CON               (AFE_BASE + 0x05C8)
#define AUDIO_TOP_DBG_MON0              (AFE_BASE + 0x05CC)
#define AUDIO_TOP_DBG_MON1              (AFE_BASE + 0x05D0)
#define AFE_IRQ8_MCU_CNT_MON            (AFE_BASE + 0x05E4)
#define AFE_IRQ11_MCU_CNT_MON           (AFE_BASE + 0x05E8)
#define AFE_IRQ12_MCU_CNT_MON           (AFE_BASE + 0x05EC)
#define AFE_ADDA2_UL_SRC_CON0           (AFE_BASE + 0x0604)
#define AFE_ADDA2_UL_SRC_CON1           (AFE_BASE + 0x0608)
#define AFE_ADDA2_SRC_DEBUG             (AFE_BASE + 0x060c)
#define AFE_ADDA2_SRC_DEBUG_MON0        (AFE_BASE + 0x0610)
#define AFE_ADDA2_UL_SRC_MON0           (AFE_BASE + 0x0618)
#define AFE_ADDA2_UL_SRC_MON1           (AFE_BASE + 0x061C)
#define AFE_ADDA2_IIR_COEF_02_01        (AFE_BASE + 0x0620)
#define AFE_ADDA2_IIR_COEF_04_03        (AFE_BASE + 0x0624)
#define AFE_ADDA2_IIR_COEF_06_05        (AFE_BASE + 0x0628)
#define AFE_ADDA2_IIR_COEF_08_07        (AFE_BASE + 0x062C)
#define AFE_ADDA2_IIR_COEF_10_09        (AFE_BASE + 0x0630)
#define PWR2_TOP_CON                    (AFE_BASE + 0x0634)
#define PWR2_TOP_CON_PDN_MEM_ASRC1_POS              (10)
#define PWR2_TOP_CON_PDN_MEM_ASRC1_MASK             (1<<PWR2_TOP_CON_PDN_MEM_ASRC1_POS)
#define AFE_GENERAL_REG0                (AFE_BASE + 0x0800)
#define AFE_GENERAL_REG1                (AFE_BASE + 0x0804)
#define AFE_GENERAL_REG2                (AFE_BASE + 0x0808)
#define AFE_GENERAL_REG3                (AFE_BASE + 0x080C)
#define AFE_GENERAL_REG4                (AFE_BASE + 0x0810)
#define AFE_GENERAL_REG5                (AFE_BASE + 0x0814)
#define AFE_GENERAL_REG6                (AFE_BASE + 0x0818)
#define AFE_GENERAL_REG7                (AFE_BASE + 0x081C)
#define AFE_GENERAL_REG8                (AFE_BASE + 0x0820)
#define AFE_GENERAL_REG9                (AFE_BASE + 0x0824)
#define AFE_GENERAL_REG10               (AFE_BASE + 0x0828)
#define AFE_GENERAL_REG11               (AFE_BASE + 0x082C)
#define AFE_GENERAL_REG12               (AFE_BASE + 0x0830)
#define AFE_GENERAL_REG13               (AFE_BASE + 0x0834)
#define AFE_GENERAL_REG14               (AFE_BASE + 0x0838)
#define AFE_GENERAL_REG15               (AFE_BASE + 0x083C)
#define AFE_CBIP_CFG0                   (AFE_BASE + 0x0840)
#define AFE_CBIP_MON0                   (AFE_BASE + 0x0844)
#define AFE_CBIP_SLV_MUX_MON0           (AFE_BASE + 0x0848)
#define AFE_CBIP_SLV_DECODER_MON0       (AFE_BASE + 0x084C)
#define AFE_I2S0_CON                    (AFE_BASE + 0x0860)
#define AFE_I2S0_CON_ENABLE_POS                     (0)
#define AFE_I2S0_CON_ENABLE_MASK                    (1<<AFE_I2S0_CON_ENABLE_POS)
#define AFE_I2S0_CON_WLEN32BIT_POS                  (1)
#define AFE_I2S0_CON_WLEN32BIT_MASK                 (1<<AFE_I2S0_CON_WLEN32BIT_POS)
#define AFE_I2S0_CON_SLAVE_POS                      (2)
#define AFE_I2S0_CON_SLAVE_MASK                     (1<<AFE_I2S0_CON_SLAVE_POS)
#define AFE_I2S0_CON_I2S_FORMAT_POS                 (3)
#define AFE_I2S0_CON_I2S_FORMAT_MASK                (1<<AFE_I2S0_CON_I2S_FORMAT_POS)
#define AFE_I2S0_CON_BCK_INV_POS                    (4)
#define AFE_I2S0_CON_BCK_INV_MASK                   (1<<AFE_I2S0_CON_BCK_INV_POS)
#define AFE_I2S0_CON_WS_INV_POS                     (5)
#define AFE_I2S0_CON_WS_INV_MASK                    (1<<AFE_I2S0_CON_WS_INV_POS)
#define AFE_I2S0_CON_BYPASS_POS                     (6)
#define AFE_I2S0_CON_BYPASS_MASK                    (1<<AFE_I2S0_CON_BYPASS_POS)
#define AFE_I2S0_CON_RST_POS                        (7)
#define AFE_I2S0_CON_RST_MASK                       (1<<AFE_I2S0_CON_RST_POS)
#define AFE_I2S0_CON_RATE_POS                       (8)
#define AFE_I2S0_CON_RATE_MASK                      (0xF<<AFE_I2S0_CON_RATE_POS)
#define AFE_I2S0_CON_LOW_JITTER_POS                 (12)
#define AFE_I2S0_CON_LOW_JITTER_MASK                (1<<AFE_I2S0_CON_LOW_JITTER_POS)
#define AFE_I2S0_CON_OUT_SWAP_POS                   (13)
#define AFE_I2S0_CON_OUT_SWAP_MASK                  (1<<AFE_I2S0_CON_OUT_SWAP_POS)
#define AFE_I2S0_CON_IN_SWAP_POS                    (14)
#define AFE_I2S0_CON_IN_SWAP_MASK                   (1<<AFE_I2S0_CON_IN_SWAP_POS)
#define AFE_I2S0_CON_NEG_LATCH_POS                  (15)
#define AFE_I2S0_CON_NEG_LATCH_MASK                 (1<<AFE_I2S0_CON_NEG_LATCH_POS)
#define AFE_I2S0_CON_OUT_SHIFT_POS                  (16)
#define AFE_I2S0_CON_OUT_SHIFT_MASK                 (0x1F<<AFE_I2S0_CON_OUT_SHIFT_POS)
#define AFE_I2S0_CON_OUT_LSB_POS                    (21)
#define AFE_I2S0_CON_OUT_LSB_MASK                   (1<<AFE_I2S0_CON_OUT_LSB_POS)
#define AFE_I2S0_CON_OUT_RJ_POS                     (22)
#define AFE_I2S0_CON_OUT_RJ_MASK                    (1<<AFE_I2S0_CON_OUT_RJ_POS)
#define AFE_I2S0_CON_IN_SHIFT_POS                   (24)
#define AFE_I2S0_CON_IN_SHIFT_MASK                  (0x1F<<AFE_I2S0_CON_IN_SHIFT_POS)
#define AFE_I2S0_CON_IN_LSB_POS                     (29)
#define AFE_I2S0_CON_IN_LSB_MASK                    (1<<AFE_I2S0_CON_IN_LSB_POS)
#define AFE_I2S0_CON_IN_RJ_POS                      (30)
#define AFE_I2S0_CON_IN_RJ_MASK                     (1<<AFE_I2S0_CON_IN_RJ_POS)
#define AFE_I2S0_CON_IN_RECOMB_POS                  (31)
#define AFE_I2S0_CON_IN_RECOMB_MASK                 (1<<AFE_I2S0_CON_IN_RECOMB_POS)
#define AFE_I2S1_CON                    (AFE_BASE + 0x0864)
#define AFE_I2S2_CON                    (AFE_BASE + 0x0868)
#define AFE_I2S3_CON                    (AFE_BASE + 0x086c)
#define AFE_I2S_TOP_CON                 (AFE_BASE + 0x0870)
#define AFE_I2S_TOP_CON_I2S0_LOOPBACK_POS           (0)
#define AFE_I2S_TOP_CON_I2S0_LOOPBACK_MASK          (1<<AFE_I2S_TOP_CON_I2S0_LOOPBACK_POS)
#define AFE_I2S_TOP_CON_I2S1_LOOPBACK_POS           (1)
#define AFE_I2S_TOP_CON_I2S1_LOOPBACK_MASK          (1<<AFE_I2S_TOP_CON_I2S1_LOOPBACK_POS)
#define AFE_I2S_TOP_CON_I2S2_LOOPBACK_POS           (2)
#define AFE_I2S_TOP_CON_I2S2_LOOPBACK_MASK          (1<<AFE_I2S_TOP_CON_I2S2_LOOPBACK_POS)
#define AFE_I2S_TOP_CON_I2S3_LOOPBACK_POS           (3)
#define AFE_I2S_TOP_CON_I2S3_LOOPBACK_MASK          (1<<AFE_I2S_TOP_CON_I2S3_LOOPBACK_POS)
#define AFE_I2S_CK_ENABLE_MON           (AFE_BASE + 0x0874)
#define AFE_I2S_BCOUNT_MON              (AFE_BASE + 0x0878)
#define AFUNC_AUD_CON0                  (AFE_BASE + 0x0900)
#define AFUNC_AUD_CON0_CCI_LCH_INV_POS              (10)
#define AFUNC_AUD_CON0_CCI_LCH_INV_MASK             (1<<AFUNC_AUD_CON0_CCI_LCH_INV_POS)
#define AFUNC_AUD_CON1                  (AFE_BASE + 0x0904)
#define AFUNC_AUD_CON2                  (AFE_BASE + 0x0908)
#define AFUNC_AUD_CON2_SDM_FIFO_RSTB_POS            (0)
#define AFUNC_AUD_CON2_SDM_FIFO_RSTB_MASK           (1<<AFUNC_AUD_CON2_SDM_FIFO_RSTB_POS)
#define AFUNC_AUD_CON2_SDM_CLK_POS                  (1)
#define AFUNC_AUD_CON2_SDM_CLK_MASK                 (1<<AFUNC_AUD_CON2_SDM_CLK_POS)
#define AFUNC_AUD_CON2_SDM_FT_TEST_POS              (2)
#define AFUNC_AUD_CON2_SDM_FT_TEST_MASK             (1<<AFUNC_AUD_CON2_SDM_FT_TEST_POS)
#define AFUNC_AUD_CON2_SDM_ENABLE_POS               (3)
#define AFUNC_AUD_CON2_SDM_ENABLE_MASK              (1<<AFUNC_AUD_CON2_SDM_ENABLE_POS)
#define AFUNC_AUD_CON2_SDM_PHASE_SEL_POS            (4)
#define AFUNC_AUD_CON2_SDM_PHASE_SEL_MASK           (1<<AFUNC_AUD_CON2_SDM_PHASE_SEL_POS)
#define AFUNC_AUD_CON2_ANA_RSTB_SEL_POS             (6)
#define AFUNC_AUD_CON2_ANA_RSTB_SEL_MASK            (1<<AFUNC_AUD_CON2_ANA_RSTB_SEL_POS)
#define AFUNC_AUD_CON2_ANA_MUTE_POS                 (7)
#define AFUNC_AUD_CON2_ANA_MUTE_MASK                (1<<AFUNC_AUD_CON2_ANA_MUTE_POS)
#define AFUNC_AUD_CON3                  (AFE_BASE + 0x090C)
#define AFUNC_AUD_CON4                  (AFE_BASE + 0x0910)
#define AFUNC_AUD_CON4_ADC_DAT_SEL_POS              (4)
#define AFUNC_AUD_CON4_ADC_DAT_SEL_MASK             (3<<AFUNC_AUD_CON4_ADC_DAT_SEL_POS)
#define AFUNC_AUD_CON4_ADC_CLK_SEL_POS              (6)
#define AFUNC_AUD_CON4_ADC_CLK_SEL_MASK             (3<<AFUNC_AUD_CON4_ADC_CLK_SEL_POS)
#define AFUNC_AUD_CON4_ANA_CLK_SEL_POS              (11)
#define AFUNC_AUD_CON4_ANA_CLK_SEL_MASK             (1<<AFUNC_AUD_CON4_ANA_CLK_SEL_POS)
#define AFUNC_AUD_CON5                  (AFE_BASE + 0x0914)
#define AFUNC_AUD_CON6                  (AFE_BASE + 0x0918)
#define AFUNC_AUD_MON0                  (AFE_BASE + 0x091C)
#define AFUNC_AUD_CON4_2                (AFE_BASE + 0x0920)
#define AFUNC_AUD_CON4_2_ADC_DAT_SEL_POS            (4)
#define AFUNC_AUD_CON4_2_ADC_DAT_SEL_MASK           (3<<AFUNC_AUD_CON4_2_ADC_DAT_SEL_POS)
#define AFUNC_AUD_CON4_2_ADC_CLK_SEL_POS            (6)
#define AFUNC_AUD_CON4_2_ADC_CLK_SEL_MASK           (3<<AFUNC_AUD_CON4_2_ADC_CLK_SEL_POS)
#define AFUNC_AUD_CON4_2_ANA_CLK_SEL_POS            (11)
#define AFUNC_AUD_CON4_2_ANA_CLK_SEL_MASK           (1<<AFUNC_AUD_CON4_2_ANA_CLK_SEL_POS)
#define AFUNC_AUD_CON4_3                (AFE_BASE + 0x0924)
#define AFUNC_AUD_CON4_3_ANA_CLK_SEL_POS            (11)
#define AFUNC_AUD_CON4_3_ANA_CLK_SEL_MASK           (1<<AFUNC_AUD_CON4_3_ANA_CLK_SEL_POS)
#define AFE_MON_DEBUG0                  (AFE_BASE + 0x0930)
#define AFE_ANC_PWR_THR_L               (AFE_BASE + 0x0960)
#define AFE_ANC_PWR_DET_L               (AFE_BASE + 0x0964)
#define AFE_ANC_PWR_THR_R               (AFE_BASE + 0x0968)
#define AFE_ANC_PWR_DET_R               (AFE_BASE + 0x096C)
#define AFE_ADDA6_UL_SRC_CON0           (AFE_BASE + 0x0a84)
#define AFE_ADD6_UL_SRC_CON1            (AFE_BASE + 0x0a88)
#define AFE_ADDA6_SRC_DEBUG             (AFE_BASE + 0x0a8c)
#define AFE_ADDA6_SRC_DEBUG_MON0        (AFE_BASE + 0x0a90)
#define AFE_ADDA6_UL_SRC_MON0           (AFE_BASE + 0x0ae4)
#define AFE_ADDA6_UL_SRC_MON1           (AFE_BASE + 0x0ae8)
#define AFE_AWB2_BASE                   (AFE_BASE + 0x0bd0)
#define AFE_AWB2_END                    (AFE_BASE + 0x0bd8)
#define AFE_AWB2_CUR                    (AFE_BASE + 0x0bdc)
#define AFE_ADDA_DL_SDM_DCCOMP_CON      (AFE_BASE + 0x0C50)
#define AFE_ADDA_DL_SDM_DCCOMP_CON_ATTGAIN_CTL_POS  (0)
#define AFE_ADDA_DL_SDM_DCCOMP_CON_ATTGAIN_CTL_MASK (63<<AFE_ADDA_DL_SDM_DCCOMP_CON_ATTGAIN_CTL_POS)
#define AFE_ADDA_DL_SDM_DCCOMP_CON_ANC_TO_DL_POS    (7)
#define AFE_ADDA_DL_SDM_DCCOMP_CON_ANC_TO_DL_MASK   (1<<AFE_ADDA_DL_SDM_DCCOMP_CON_ANC_TO_DL_POS)
#define AFE_ADDA_DL_SDM_MONO_POS                    (9)
#define AFE_ADDA_DL_SDM_MONO_MASK                   (1<<AFE_ADDA_DL_SDM_MONO_POS)
#define AFE_ADDA_DL_SDM_RST_EN_POS                  (11)
#define AFE_ADDA_DL_SDM_RST_EN_MASK                 (1<<AFE_ADDA_DL_SDM_RST_EN_POS)
#define AFE_ADDA_DL_SDM_DCCOMP_CON_7BIT_POS         (18)
#define AFE_ADDA_DL_SDM_DCCOMP_CON_7BIT_MASK        (1<<AFE_ADDA_DL_SDM_DCCOMP_CON_7BIT_POS)
#define AFE_ADDA_DL_SDM_DCCOMP_CON_FIFO_SWAP_POS    (20)
#define AFE_ADDA_DL_SDM_DCCOMP_CON_FIFO_SWAP_MASK   (1<<AFE_ADDA_DL_SDM_DCCOMP_CON_FIFO_SWAP_POS)
#define AFE_ADDA_DL_SDM_TEST            (AFE_BASE + 0x0C54)
#define AFE_ADDA_DL_DC_COMP_CFG0        (AFE_BASE + 0x0C58)
#define AFE_ADDA_DL_DC_COMP_CFG1        (AFE_BASE + 0x0C5C)
#define AFE_ADDA_DL_SDM_FIFO_MON        (AFE_BASE + 0x0C60)
#define AFE_ADDA_DL_SRC_LCH_MON         (AFE_BASE + 0x0C64)
#define AFE_ADDA_DL_SRC_RCH_MON         (AFE_BASE + 0x0C68)
#define AFE_ADDA_DL_SDM_OUT_MON         (AFE_BASE + 0x0C6C)
#define AFE_ADDA_DL_DITHER_CON          (AFE_BASE + 0x0C70)
#define AFE_ADDA6_IIR_COEF_02_01        (AFE_BASE + 0x0CE0)
#define AFE_ADDA6_IIR_COEF_04_03        (AFE_BASE + 0x0CE4)
#define AFE_ADDA6_IIR_COEF_06_05        (AFE_BASE + 0x0CE8)
#define AFE_ADDA6_IIR_COEF_08_07        (AFE_BASE + 0x0CEC)
#define AFE_ADDA6_IIR_COEF_10_09        (AFE_BASE + 0x0CF0)
#define AFE_ADDA_PREDIS_CON2            (AFE_BASE + 0x0D40)
#define AFE_ADDA_PREDIS_CON2_PREDIS_A5_CH2_CTL_POS          (0)
#define AFE_ADDA_PREDIS_CON2_PREDIS_A5_CH2_CTL_MASK         (4095<<AFE_ADDA_PREDIS_CON2_PREDIS_A5_CH2_CTL_POS)
#define AFE_ADDA_PREDIS_CON2_PREDIS_A4_CH2_CTL_POS          (16)
#define AFE_ADDA_PREDIS_CON2_PREDIS_A4_CH2_CTL_MASK         (4095<<AFE_ADDA_PREDIS_CON2_PREDIS_A4_CH2_CTL_POS)
#define AFE_ADDA_PREDIS_CON2_PREDIS_ON_5TH_ORDER_CTL_POS    (31)
#define AFE_ADDA_PREDIS_CON2_PREDIS_ON_5TH_ORDER_CTL_MASK   (1<<AFE_ADDA_PREDIS_CON2_PREDIS_ON_5TH_ORDER_CTL_POS)
#define AFE_ADDA_PREDIS_CON3            (AFE_BASE + 0x0D44)
#define AFE_MEMIF_MON12                 (AFE_BASE + 0x0d70)
#define AFE_MEMIF_MON13                 (AFE_BASE + 0x0d74)
#define AFE_MEMIF_MON14                 (AFE_BASE + 0x0d78)
#define AFE_MEMIF_MON15                 (AFE_BASE + 0x0d7c)
#define AFE_MEMIF_MON18                 (AFE_BASE + 0x0d88)
#define AFE_MEMIF_MON19                 (AFE_BASE + 0x0d8c)
#define AFE_MEMIF_MON23                 (AFE_BASE + 0x0d9c)
#define AFE_MEMIF_MON24                 (AFE_BASE + 0x0da0)
#define AFE_HD_ENGEN_ENABLE             (AFE_BASE + 0x0DD0)
#define AFE_DL_NLE_R_CFG0               (AFE_BASE + 0x0E44)
#define AFE_DL_NLE_R_CFG1               (AFE_BASE + 0x0E48)
#define AFE_DL_NLE_L_CFG0               (AFE_BASE + 0x0E4C)
#define AFE_DL_NLE_L_CFG1               (AFE_BASE + 0x0E50)
#define AFE_DL_NLE_R_MON0               (AFE_BASE + 0x0E54)
#define AFE_DL_NLE_R_MON1               (AFE_BASE + 0x0E58)
#define AFE_DL_NLE_R_MON2               (AFE_BASE + 0x0E5C)
#define AFE_DL_NLE_L_MON0               (AFE_BASE + 0x0E60)
#define AFE_DL_NLE_L_MON1               (AFE_BASE + 0x0E64)
#define AFE_DL_NLE_L_MON2               (AFE_BASE + 0x0E68)
#define AFE_ANA_GAIN_MUX                (AFE_BASE + 0x0E6C)
#define AFE_ANA_GAIN_MUX_R_CLASSG_GAIN_POS          (0)
#define AFE_ANA_GAIN_MUX_R_CLASSG_GAIN_MASK         (3<<AFE_ANA_GAIN_MUX_R_CLASSG_GAIN_POS)
#define AFE_ANA_GAIN_MUX_L_CLASSG_GAIN_POS          (2)
#define AFE_ANA_GAIN_MUX_L_CLASSG_GAIN_MASK         (3<<AFE_ANA_GAIN_MUX_L_CLASSG_GAIN_POS)
#define AFE_ANA_GAIN_MUX_R_ZCD_GAIN_EN_POS          (4)
#define AFE_ANA_GAIN_MUX_R_ZCD_GAIN_EN_MASK         (1<<AFE_ANA_GAIN_MUX_R_ZCD_GAIN_EN_POS)
#define AFE_ANA_GAIN_MUX_L_ZCD_GAIN_EN_POS          (6)
#define AFE_ANA_GAIN_MUX_L_ZCD_GAIN_EN_MASK         (1<<AFE_ANA_GAIN_MUX_L_ZCD_GAIN_EN_POS)

#define AFE_CLASSOP_CFG0          (AFE_BASE + 0x0EF4)
#define AFE_CLASSOP_CFG0_CLASSOP_MODE_POS           (0)
#define AFE_CLASSOP_CFG0_CLASSOP_MODE_MASK          (3<<AFE_CLASSOP_CFG0_CLASSOP_MODE_POS)
#define AFE_CLASSOP_CFG0_USE_ANC_EN                 (4)
#define AFE_CLASSOP_CFG0_USE_ANC_EN_MASK            (1<<AFE_CLASSOP_CFG0_USE_ANC_EN)
#define AFE_CLASSOP_CFG0_USE_ANC_DATA               (5)
#define AFE_CLASSOP_CFG0_USE_ANC_DATA_MASK          (1<<AFE_CLASSOP_CFG0_USE_ANC_DATA)

#define AFE_CLASSG_LPSRCH_CFG0          (AFE_BASE + 0x0EC4)
#define AFE_CLASSG_LPSRCH_CFG0_STG_CTRL_POS         (19)
#define AFE_CLASSG_LPSRCH_CFG0_STG_CTRL_MASK        (7<<AFE_CLASSG_LPSRCH_CFG0_STG_CTRL_POS)
#define AFE_CLASSG_LPSRCH_CFG1          (AFE_BASE + 0x0EC8)
#define AFE_CLASSG_LPSRCH_CFG2          (AFE_BASE + 0x0ECC)
#define AFE_CLASSG_LPSLCH_CFG0          (AFE_BASE + 0x0ED0)
#define AFE_CLASSG_LPSLCH_CFG0_ZCD_EN_POS           (0)
#define AFE_CLASSG_LPSLCH_CFG0_ZCD_EN_MASK          (1<<AFE_CLASSG_LPSLCH_CFG0_ZCD_EN_POS)
#define AFE_CLASSG_LPSLCH_CFG0_RST_POS              (1)
#define AFE_CLASSG_LPSLCH_CFG0_RST_MASK             (1<<AFE_CLASSG_LPSLCH_CFG0_RST_POS)
#define AFE_CLASSG_LPSLCH_CFG0_ZERO_FLAG_POS        (2)
#define AFE_CLASSG_LPSLCH_CFG0_ZERO_FLAG_MASK       (1<<AFE_CLASSG_LPSLCH_CFG0_ZERO_FLAG_POS)
#define AFE_CLASSG_LPSLCH_CFG0_AUTO_POS             (3)
#define AFE_CLASSG_LPSLCH_CFG0_AUTO_MASK            (1<<AFE_CLASSG_LPSLCH_CFG0_AUTO_POS)
#define AFE_CLASSG_LPSLCH_CFG0_MODE_POS             (4)
#define AFE_CLASSG_LPSLCH_CFG0_MODE_MASK            (3<<AFE_CLASSG_LPSLCH_CFG0_MODE_POS)
#define AFE_CLASSG_LPSLCH_CFG0_CMP_SEL_POS          (6)
#define AFE_CLASSG_LPSLCH_CFG0_CMP_SEL_MASK         (3<<AFE_CLASSG_LPSLCH_CFG0_CMP_SEL_POS)
#define AFE_CLASSG_LPSLCH_CFG0_CMP_SEL_POS          (6)
#define AFE_CLASSG_LPSLCH_CFG0_CMP_SEL_MASK         (3<<AFE_CLASSG_LPSLCH_CFG0_CMP_SEL_POS)
#define AFE_CLASSG_LPSLCH_CFG0_STEP_SIZE_POS        (8)
#define AFE_CLASSG_LPSLCH_CFG0_STEP_SIZE_MASK       (7<<AFE_CLASSG_LPSLCH_CFG0_STEP_SIZE_POS)
#define AFE_CLASSG_LPSLCH_CFG0_HOLD_TIME_POS        (11)
#define AFE_CLASSG_LPSLCH_CFG0_HOLD_TIME_MASK       (7<<AFE_CLASSG_LPSLCH_CFG0_HOLD_TIME_POS)
#define AFE_CLASSG_LPSLCH_CFG0_CHG_STEP_POS         (14)
#define AFE_CLASSG_LPSLCH_CFG0_CHG_STEP_MASK        (7<<AFE_CLASSG_LPSLCH_CFG0_CHG_STEP_POS)
#define AFE_CLASSG_LPSLCH_CFG0_OUT_CTRL_POS         (17)
#define AFE_CLASSG_LPSLCH_CFG0_OUT_CTRL_MASK        (1<<AFE_CLASSG_LPSLCH_CFG0_OUT_CTRL_POS)
#define AFE_CLASSG_LPSLCH_CFG0_CTRL_VA33_POS        (18)
#define AFE_CLASSG_LPSLCH_CFG0_CTRL_VA33_MASK       (1<<AFE_CLASSG_LPSLCH_CFG0_CTRL_VA33_POS)
#define AFE_CLASSG_LPSLCH_CFG0_STG_CTRL_POS         (19)
#define AFE_CLASSG_LPSLCH_CFG0_STG_CTRL_MASK        (7<<AFE_CLASSG_LPSLCH_CFG0_STG_CTRL_POS)
#define AFE_CLASSG_LPSLCH_CFG0_VA33_MIN_POS         (22)
#define AFE_CLASSG_LPSLCH_CFG0_VA33_MIN_MASK        (7<<AFE_CLASSG_LPSLCH_CFG0_VA33_MIN_POS)
#define AFE_CLASSG_LPSLCH_CFG0_VA33_MAX_POS         (25)
#define AFE_CLASSG_LPSLCH_CFG0_VA33_MAX_MASK        (7<<AFE_CLASSG_LPSLCH_CFG0_VA33_MAX_POS)
#define AFE_CLASSG_LPSLCH_CFG0_GEN_MODE_POS         (28)
#define AFE_CLASSG_LPSLCH_CFG0_GEN_MODE_MASK        (1<<AFE_CLASSG_LPSLCH_CFG0_GEN_MODE_POS)
#define AFE_CLASSG_LPSLCH_CFG1          (AFE_BASE + 0x0ED4)
#define AFE_CLASSG_LPSLCH_CFG2          (AFE_BASE + 0x0ED8)
#define AFE_CLASSG_CFG0                 (AFE_BASE + 0x0EDC)
#define AFE_CLASSG_CFG1                 (AFE_BASE + 0x0EE0)
#define AFE_CLASSG_CFG2                 (AFE_BASE + 0x0EE4)
#define AFE_CLASSG_CFG3                 (AFE_BASE + 0x0EE8)
#define AFE_CLASSG_CFG4                 (AFE_BASE + 0x0EEC)
#define AFE_CLASSG_CFG4_FALLING_COUNT_TIME_POS      (0)
#define AFE_CLASSG_CFG4_FALLING_COUNT_TIME_MASK     (0x3<<AFE_CLASSG_CFG4_FALLING_COUNT_TIME_POS)
#define AFE_CLASSG_CFG4_RAISING_COUNT_TIME_POS      (2)
#define AFE_CLASSG_CFG4_RAISING_COUNT_TIME_MASK     (0x3<<AFE_CLASSG_CFG4_RAISING_COUNT_TIME_POS)
#define AFE_CLASSG_CFG4_VCM_CMPOUT_SEL_POS          (4)
#define AFE_CLASSG_CFG4_VCM_CMPOUT_SEL_MASK         (0x3<<AFE_CLASSG_CFG4_VCM_CMPOUT_SEL_POS)
#define AFE_CLASSG_CFG4_HIFI_MODE_POS               (6)
#define AFE_CLASSG_CFG4_HIFI_MODE_MASK              (1<<AFE_CLASSG_CFG4_HIFI_MODE_POS)
#define AFE_CLASSG_CFG4_GAIN_CONFIG_POS             (7)
#define AFE_CLASSG_CFG4_GAIN_CONFIG_MASK            (1<<AFE_CLASSG_CFG4_GAIN_CONFIG_POS)
#define AFE_CLASSG_CFG4_CH1_GAIN_POS                (8)
#define AFE_CLASSG_CFG4_CH1_GAIN_MASK               (0xFFF<<AFE_CLASSG_CFG4_CH1_GAIN_POS)
#define AFE_CLASSG_CFG4_CH2_GAIN_POS                (20)
#define AFE_CLASSG_CFG4_CH2_GAIN_MASK               (0xFFF<<AFE_CLASSG_CFG4_CH2_GAIN_POS)
#define AFE_CLASSG_CFG5                 (AFE_BASE + 0x0EF0)
#define AFE_CLASSG_CFG5_CLASSG_AD_VCMT_FASTVCM_POS  (4)
#define AFE_CLASSG_CFG5_CLASSG_AD_VCMT_FASTVCM_MASK (1<<AFE_CLASSG_CFG5_CLASSG_AD_VCMT_FASTVCM_POS)
#define AFE_CLASSG_CFG5_CLASSG_AD_VCMT_FASTVCM_SW_MODE_POS  (7)
#define AFE_CLASSG_CFG5_CLASSG_AD_VCMT_FASTVCM_SW_MODE_MASK (1<<AFE_CLASSG_CFG5_CLASSG_AD_VCMT_FASTVCM_SW_MODE_POS)
#define AFE_CLASSG_MON0                 (AFE_BASE + 0x0EFC)
#define AFE_CLASSG_MON0_VAUD18SEL_POS               (29)
#define AFE_CLASSG_MON0_VAUD18SEL_MASK              (1<<AFE_CLASSG_MON0_VAUD18SEL_POS)
#define TOP_DMIC_CK_SEL                 (AFE_BASE + 0x0F48)
#define TOP_DMIC_CK_SEL_GPIO_DMIC0_POS              (0)
#define TOP_DMIC_CK_SEL_GPIO_DMIC0_MASK             (3<<TOP_DMIC_CK_SEL_GPIO_DMIC0_POS)
#define TOP_DMIC_CK_SEL_GPIO_DMIC1_POS              (2)
#define TOP_DMIC_CK_SEL_GPIO_DMIC1_MASK             (3<<TOP_DMIC_CK_SEL_GPIO_DMIC1_POS)
#define TOP_DMIC_CK_SEL_ANA_DMIC0_POS               (4)
#define TOP_DMIC_CK_SEL_ANA_DMIC0_MASK              (3<<TOP_DMIC_CK_SEL_ANA_DMIC0_POS)
#define TOP_DMIC_CK_SEL_ANA_DMIC1_POS               (6)
#define TOP_DMIC_CK_SEL_ANA_DMIC1_MASK              (3<<TOP_DMIC_CK_SEL_ANA_DMIC1_POS)
#define TOP_DMIC_CK_SEL_ANA_DMIC2_POS               (8)
#define TOP_DMIC_CK_SEL_ANA_DMIC2_MASK              (3<<TOP_DMIC_CK_SEL_ANA_DMIC2_POS)
#define TOP_DMIC_CK_SEL_ANA_DMIC3_POS               (10)
#define TOP_DMIC_CK_SEL_ANA_DMIC3_MASK              (3<<TOP_DMIC_CK_SEL_ANA_DMIC3_POS)
#define TOP_DMIC_CK_SEL_ANA_DMIC4_POS               (12)
#define TOP_DMIC_CK_SEL_ANA_DMIC4_MASK              (3<<TOP_DMIC_CK_SEL_ANA_DMIC4_POS)
#define TOP_DMIC_CK_SEL_ANA_DMIC5_POS               (14)
#define TOP_DMIC_CK_SEL_ANA_DMIC5_MASK              (3<<TOP_DMIC_CK_SEL_ANA_DMIC5_POS)
#define TOP_DMIC_DAT_SEL                (AFE_BASE + 0x0F4C)
#define TOP_DMIC_DAT_SEL_UL1_POS                    (0)
#define TOP_DMIC_DAT_SEL_UL1_MASK                   (3<<TOP_DMIC_DAT_SEL_UL1_POS)
#define TOP_DMIC_DAT_SEL_UL2_POS                    (2)
#define TOP_DMIC_DAT_SEL_UL2_MASK                   (3<<TOP_DMIC_DAT_SEL_UL2_POS)
#define TOP_DMIC_DAT_SEL_UL3_POS                    (4)
#define TOP_DMIC_DAT_SEL_UL3_MASK                   (3<<TOP_DMIC_DAT_SEL_UL3_POS)
#define TOP_DMIC_DAT_SEL_UL4_POS                    (6)
#define TOP_DMIC_DAT_SEL_UL4_MASK                   (3<<TOP_DMIC_DAT_SEL_UL4_POS)
#define TOP_DMIC_DAT_SEL_UL1_PRE_POS                (8)
#define TOP_DMIC_DAT_SEL_UL1_PRE_MASK               (3<<TOP_DMIC_DAT_SEL_UL1_PRE_POS)
#define TOP_DMIC_DAT_SEL_UL2_PRE_POS                (10)
#define TOP_DMIC_DAT_SEL_UL2_PRE_MASK               (3<<TOP_DMIC_DAT_SEL_UL2_PRE_POS)
#define TOP_DMIC_DAT_SEL_UL3_PRE_POS                (12)
#define TOP_DMIC_DAT_SEL_UL3_PRE_MASK               (3<<TOP_DMIC_DAT_SEL_UL3_PRE_POS)
#define TOP_DMIC_DAT_SEL_UL4_PRE_POS                (14)
#define TOP_DMIC_DAT_SEL_UL4_PRE_MASK               (3<<TOP_DMIC_DAT_SEL_UL4_PRE_POS)
#define TOP_DMIC_DAT_SEL_UL1_SEL_POS                (16)
#define TOP_DMIC_DAT_SEL_UL1_SEL_MASK               (1<<TOP_DMIC_DAT_SEL_UL1_SEL_POS)
#define TOP_DMIC_DAT_SEL_UL2_SEL_POS                (17)
#define TOP_DMIC_DAT_SEL_UL2_SEL_MASK               (1<<TOP_DMIC_DAT_SEL_UL2_SEL_POS)
#define TOP_DMIC_DAT_SEL_UL3_SEL_POS                (18)
#define TOP_DMIC_DAT_SEL_UL3_SEL_MASK               (1<<TOP_DMIC_DAT_SEL_UL3_SEL_POS)
#define TOP_DMIC_DAT_SEL_UL4_SEL_POS                (19)
#define TOP_DMIC_DAT_SEL_UL4_SEL_MASK               (1<<TOP_DMIC_DAT_SEL_UL4_SEL_POS)
#define ZCD_CON0                        (AFE_BASE + 0x0F50)
#define ZCD_CON0_ZCD_EN_POS                         (0)
#define ZCD_CON0_ZCD_EN_MASK                        (1<<ZCD_CON0_ZCD_EN_POS)
#define ZCD_CON2                        (AFE_BASE + 0x0F58)
#define ZCD_CON2_L_GAIN_POS                         (0)
#define ZCD_CON2_L_GAIN_MASK                        (0x3F<<ZCD_CON2_L_GAIN_POS)
#define ZCD_CON2_R_GAIN_POS                         (6)
#define ZCD_CON2_R_GAIN_MASK                        (0x3F<<ZCD_CON2_R_GAIN_POS)
#define ZCD_CON5                        (AFE_BASE + 0x0F64)
#define ZCD_CON6                        (AFE_BASE + 0x0F68)
#define AFE_MBIST_MODE                  (AFE_BASE + 0x0F80)
#define AFE_MBIST_HOLDB                 (AFE_BASE + 0x0F84)
#define AFE_MBIST_BACKGROUND            (AFE_BASE + 0x0F88)
#define AFE_MBIST_DONE                  (AFE_BASE + 0x0F8C)
#define AFE_MBIST_FAIL                  (AFE_BASE + 0x0F90)
#define AFE_MBIST_BSEL                  (AFE_BASE + 0x0F94)
#define AFE_DCCLK_CFG                   (AFE_BASE + 0x0F98)
#define AFE_DCCLK_CFG_PHASE_POS                     (0)
#define AFE_DCCLK_CFG_PHASE_MASK                    (7<<AFE_DCCLK_CFG_PHASE_POS)
#define AFE_DCCLK_CFG_RESYNC_BYPASS_POS             (4)
#define AFE_DCCLK_CFG_RESYNC_BYPASS_MASK            (1<<AFE_DCCLK_CFG_RESYNC_BYPASS_POS)
#define AFE_DCCLK_CFG_RESYNC_CLK_INV_POS            (5)
#define AFE_DCCLK_CFG_RESYNC_CLK_INV_MASK           (1<<AFE_DCCLK_CFG_RESYNC_CLK_INV_POS)
#define AFE_DCCLK_CFG_RESYNC_CLK_SEL_POS            (6)
#define AFE_DCCLK_CFG_RESYNC_CLK_SEL_MASK           (3<<AFE_DCCLK_CFG_RESYNC_CLK_SEL_POS)
#define AFE_DCCLK_CFG_GEN_ON_POS                    (8)
#define AFE_DCCLK_CFG_GEN_ON_MASK                   (1<<AFE_DCCLK_CFG_GEN_ON_POS)
#define AFE_DCCLK_CFG_POWER_DOWN_POS                (9)
#define AFE_DCCLK_CFG_POWER_DOWN_MASK               (1<<AFE_DCCLK_CFG_POWER_DOWN_POS)
#define AFE_DCCLK_CFG_DCCLK_INV_POS                 (10)
#define AFE_DCCLK_CFG_DCCLK_INV_MASK                (1<<AFE_DCCLK_CFG_DCCLK_INV_POS)
#define AFE_DCCLK_CFG_DCCLK_DIV_POS                 (11)
#define AFE_DCCLK_CFG_DCCLK_DIV_MASK                (0x7FF<<AFE_DCCLK_CFG_DCCLK_DIV_POS)


#define AFE_DL_UP_PTR_SEL               (AFE_BASE + 0x0FA0)
#define AFE_DL1_UP_PTR_MON              (AFE_BASE + 0x0FA4)
#define AFE_DL2_UP_PTR_MON              (AFE_BASE + 0x0FA8)
#define AFE_DL_UP_LEVEL_MON             (AFE_BASE + 0x0FAC)
#define AFE_AUDIO_BT_SYNC_CON0          (AFE_BASE + 0x0FD0)
#define AFE_AUDIO_BT_SYNC_MON0          (AFE_BASE + 0x0FD4)
#define AFE_AUDIO_BT_SYNC_MON1          (AFE_BASE + 0x0FD8)
#define AFE_AUDIO_BT_SYNC_MON1_DL1_MONITOR_POS      (0)
#define AFE_AUDIO_BT_SYNC_MON1_DL1_MONITOR_MASK     (0xFFFF<<AFE_AUDIO_BT_SYNC_MON1_DL1_MONITOR_POS)
#define AFE_AUDIO_BT_SYNC_MON1_DL2_MONITOR_POS      (16)
#define AFE_AUDIO_BT_SYNC_MON1_DL2_MONITOR_MASK     (0xFFFF<<AFE_AUDIO_BT_SYNC_MON1_DL2_MONITOR_POS)
#define AFE_AUDIO_BT_SYNC_MON2          (AFE_BASE + 0x0FDC)
#define AFE_AUDIO_BT_SYNC_MON2_HW_SETTED_POS        (0)
#define AFE_AUDIO_BT_SYNC_MON2_HW_SETTED_MASK       (1<<AFE_AUDIO_BT_SYNC_MON2_HW_SETTED_POS)
#define AFE_AUDIO_BT_SYNC_MON2_DL1_SETTED_POS       (4)
#define AFE_AUDIO_BT_SYNC_MON2_DL1_SETTED_MASK      (1<<AFE_AUDIO_BT_SYNC_MON2_DL1_SETTED_POS)
#define AFE_AUDIO_BT_SYNC_MON2_DL2_SETTED_POS       (5)
#define AFE_AUDIO_BT_SYNC_MON2_DL2_SETTED_MASK      (1<<AFE_AUDIO_BT_SYNC_MON2_DL2_SETTED_POS)
#define AFE_AUDIO_BT_SYNC_MON2_DL12_SETTED_POS      (6)
#define AFE_AUDIO_BT_SYNC_MON2_DL12_SETTED_MASK     (1<<AFE_AUDIO_BT_SYNC_MON2_DL12_SETTED_POS)
#define AFE_AUDIO_BT_SYNC_MON2_DL3_SETTED_POS       (7)
#define AFE_AUDIO_BT_SYNC_MON2_DL3_SETTED_MASK      (1<<AFE_AUDIO_BT_SYNC_MON2_DL3_SETTED_POS)
#define AFE_AUDIO_BT_SYNC_MON2_DL1_EN_POS           (8)
#define AFE_AUDIO_BT_SYNC_MON2_DL1_EN_MASK          (1<<AFE_AUDIO_BT_SYNC_MON2_DL1_EN_POS)
#define AFE_AUDIO_BT_SYNC_MON2_DL2_EN_POS           (9)
#define AFE_AUDIO_BT_SYNC_MON2_DL2_EN_MASK          (1<<AFE_AUDIO_BT_SYNC_MON2_DL2_EN_POS)
#define AFE_AUDIO_BT_SYNC_MON2_DL12_EN_POS          (10)
#define AFE_AUDIO_BT_SYNC_MON2_DL12_EN_MASK         (1<<AFE_AUDIO_BT_SYNC_MON2_DL12_EN_POS)
#define AFE_AUDIO_BT_SYNC_MON2_DL3_EN_POS           (11)
#define AFE_AUDIO_BT_SYNC_MON2_DL3_EN_MASK          (1<<AFE_AUDIO_BT_SYNC_MON2_DL3_EN_POS)
#define AFE_AUDIO_BT_SYNC_MON3          (AFE_BASE + 0x0FE0)
#define AFE_AUDIO_BT_SYNC_MON4          (AFE_BASE + 0x0FE4)
#define MEM_ASRC_TOP_CON0               (AFE_BASE + 0x1000)
#define MEM_ASRC_TOP_CON0_MASM1_RST_POS             (0)
#define MEM_ASRC_TOP_CON0_MASM1_RST_MASK            (1<<MEM_ASRC_TOP_CON0_MASM1_RST_POS)
#define MEM_ASRC_TOP_CON0_MASM2_RST_POS             (1)
#define MEM_ASRC_TOP_CON0_MASM2_RST_MASK            (1<<MEM_ASRC_TOP_CON0_MASM2_RST_POS)
#define MEM_ASRC_TOP_CON1               (AFE_BASE + 0x1004)
#define MEM_ASRC_TOP_CON2               (AFE_BASE + 0x1008)
#define MEM_ASRC_TOP_CON3               (AFE_BASE + 0x100C)
#define MEM_ASRC_TOP_MON0               (AFE_BASE + 0x1018)
#define MEM_ASRC_TOP_MON1               (AFE_BASE + 0x101C)
#define PWR2_ASM_CON2                   (AFE_BASE + 0x1074)
#define PWR2_ASM_CON2_MEM_ASRC_1_RESET_POS          (20)
#define PWR2_ASM_CON2_MEM_ASRC_1_RESET_MASK         (1<<PWR2_ASM_CON2_MEM_ASRC_1_RESET_POS)
#define MEM_ASRC_TRAC_CON1              (AFE_BASE + 0x108C)
#define MEM_ASRC_TRAC_CON1_CALC_LRCK_SEL_POS        (0)
#define MEM_ASRC_TRAC_CON1_CALC_LRCK_SEL_MASK       (7<<MEM_ASRC_TRAC_CON1_CALC_LRCK_SEL_POS)

#define ASM_GEN_CONF                    (AFE_BASE + 0x1100)
#define ASM_GEN_CONF_HW_UPDATE_OBUF_RDPNT_POS       (28)
#define ASM_GEN_CONF_HW_UPDATE_OBUF_RDPNT_MASK      (1<<ASM_GEN_CONF_HW_UPDATE_OBUF_RDPNT_POS)
#define ASM_GEN_CONF_CH_CNTX_SWEN_POS               (20)
#define ASM_GEN_CONF_CH_CNTX_SWEN_MASK              (1<<ASM_GEN_CONF_CH_CNTX_SWEN_POS)
#define ASM_GEN_CONF_CH_CLEAR_POS                   (16)
#define ASM_GEN_CONF_CH_CLEAR_MASK                  (1<<ASM_GEN_CONF_CH_CLEAR_POS)
#define ASM_GEN_CONF_CH_EN_POS                      (12)
#define ASM_GEN_CONF_CH_EN_MASK                     (1<<ASM_GEN_CONF_CH_EN_POS)
#define ASM_GEN_CONF_DSP_CTRL_COEFF_SRAM_POS        (11)
#define ASM_GEN_CONF_DSP_CTRL_COEFF_SRAM_MASK       (1<<ASM_GEN_CONF_DSP_CTRL_COEFF_SRAM_POS)
#define ASM_GEN_CONF_ASRC_BUSY_POS                  (9)
#define ASM_GEN_CONF_ASRC_BUSY_MASK                 (1<<ASM_GEN_CONF_ASRC_BUSY_POS)
#define ASM_GEN_CONF_ASRC_EN_POS                    (8)
#define ASM_GEN_CONF_ASRC_EN_MASK                   (1<<ASM_GEN_CONF_ASRC_EN_POS)
#define ASM_GEN_CONF_ASRC_CONTINUOUS_EN_POS         (0)
#define ASM_GEN_CONF_ASRC_CONTINUOUS_EN_MASK        (1<<ASM_GEN_CONF_ASRC_CONTINUOUS_EN_POS)


#define ASM_IER                         (AFE_BASE + 0x1104)
#define ASM_IER_IBUF_EMPTY_INTEN_POS                (20)
#define ASM_IER_IBUF_EMPTY_INTEN_MASK               (1<<ASM_IER_IBUF_EMPTY_INTEN_POS)
#define ASM_IER_IBUF_AMOUNT_INTEN_POS               (16)
#define ASM_IER_IBUF_AMOUNT_INTEN_MASK              (1<<ASM_IER_IBUF_AMOUNT_INTEN_POS)
#define ASM_IER_OBUF_OV_INTEN_POS                   (12)
#define ASM_IER_OBUF_OV_INTEN_MASK                  (1<<ASM_IER_OBUF_OV_INTEN_POS)
#define ASM_IER_OBUF_AMOUNT_INTEN_POS               (8)
#define ASM_IER_OBUF_AMOUNT_INTEN_MASK              (1<<ASM_IER_OBUF_AMOUNT_INTEN_POS)
#define ASM_IFR                         (AFE_BASE + 0x1108)
#define ASM_IFR_IBUF_EMPTY_INT_POS                  (20)
#define ASM_IFR_IBUF_EMPTY_INT_MASK                 (1<<ASM_IFR_IBUF_EMPTY_INT_POS)
#define ASM_IFR_IBUF_AMOUNT_INT_POS                 (16)
#define ASM_IFR_IBUF_AMOUNT_INT_MASK                (1<<ASM_IFR_IBUF_AMOUNT_INT_POS)
#define ASM_IFR_OBUF_OV_INT_POS                     (12)
#define ASM_IFR_OBUF_OV_INT_MASK                    (1<<ASM_IFR_OBUF_OV_INT_POS)
#define ASM_IFR_OBUF_AMOUNT_INT_POS                 (8)
#define ASM_IFR_OBUF_AMOUNT_INT_MASK                (1<<ASM_IFR_OBUF_AMOUNT_INT_POS)

//#ifndef ENABLE_HWSRC_CLKSKEW
#define ASM_IFR_MASK                                (ASM_IFR_IBUF_EMPTY_INT_MASK|ASM_IFR_IBUF_AMOUNT_INT_MASK|ASM_IFR_OBUF_OV_INT_MASK|ASM_IFR_OBUF_AMOUNT_INT_MASK)
//#else
//#define ASM_IFR_MASK                                ASM_IFR_IBUF_EMPTY_INT_MASK//(ASM_IFR_IBUF_EMPTY_INT_MASK|ASM_IFR_IBUF_AMOUNT_INT_MASK|ASM_IFR_OBUF_OV_INT_MASK|ASM_IFR_OBUF_AMOUNT_INT_MASK)
//#endif

#define ASM_CH01_CNFG                   (AFE_BASE + 0x1110)
#define ASM_CH01_CNFG_CLR_IIR_BUF_POS               (23)
#define ASM_CH01_CNFG_CLR_IIR_BUF_MASK              (1<<ASM_CH01_CNFG_CLR_IIR_BUF_POS)
#define ASM_CH01_CNFG_OBIT_WIDTH_POS                (22)
#define ASM_CH01_CNFG_OBIT_WIDTH_MASK               (1<<ASM_CH01_CNFG_OBIT_WIDTH_POS)
#define ASM_CH01_CNFG_IBIT_WIDTH_POS                (21)
#define ASM_CH01_CNFG_IBIT_WIDTH_MASK               (1<<ASM_CH01_CNFG_IBIT_WIDTH_POS)
#define ASM_CH01_CNFG_MONO_POS                      (20)
#define ASM_CH01_CNFG_MONO_MASK                     (1<<ASM_CH01_CNFG_MONO_POS)
#define ASM_CH01_CNFG_OFS_POS                       (18)
#define ASM_CH01_CNFG_OFS_MASK                      (3<<ASM_CH01_CNFG_OFS_POS)
#define ASM_CH01_CNFG_IFS_POS                       (16)
#define ASM_CH01_CNFG_IFS_MASK                      (3<<ASM_CH01_CNFG_IFS_POS)
#define ASM_CH01_CNFG_CLAC_AMOUNT_POS               (8)
#define ASM_CH01_CNFG_CLAC_AMOUNT_MASK              (0xFF<<ASM_CH01_CNFG_CLAC_AMOUNT_POS)
#define ASM_CH01_CNFG_IIR_EN_POS                    (7)
#define ASM_CH01_CNFG_IIR_EN_MASK                   (1<<ASM_CH01_CNFG_IIR_EN_POS)
#define ASM_CH01_CNFG_IIR_STAGE_POS                 (4)
#define ASM_CH01_CNFG_IIR_STAGE_MASK                (7<<ASM_CH01_CNFG_IIR_STAGE_POS)
#define ASM_FREQUENCY_0                 (AFE_BASE + 0x1120)
#define ASM_FREQUENCY_1                 (AFE_BASE + 0x1124)
#define ASM_FREQUENCY_2                 (AFE_BASE + 0x1128)
#define ASM_FREQUENCY_3                 (AFE_BASE + 0x112C)
#define ASM_IBUF_SADR                   (AFE_BASE + 0x1130)
#define ASM_IBUF_SADR_POS                           (0)//align 128-bit
#define ASM_IBUF_SADR_MASK                          (0xFFFFFFFF<<ASM_IBUF_SADR_POS)
#define ASM_IBUF_SIZE                   (AFE_BASE + 0x1134)
#define ASM_IBUF_SIZE_POS                           (0)
#define ASM_IBUF_SIZE_MASK                          (0xFFFFF<<ASM_IBUF_SIZE_POS)
#define ASM_OBUF_SADR                   (AFE_BASE + 0x1138)
#define ASM_OBUF_SADR_POS                           (0)
#define ASM_OBUF_SADR_MASK                          (0xFFFFFFFF<<ASM_OBUF_SADR_POS)
#define ASM_OBUF_SIZE                   (AFE_BASE + 0x113C)
#define ASM_OBUF_SIZE_POS                           (0)
#define ASM_OBUF_SIZE_MASK                          (0xFFFFF<<ASM_OBUF_SIZE_POS)
#define ASM_CH01_IBUF_RDPNT             (AFE_BASE + 0x1140)
#define ASM_CH01_IBUF_RDPNT_POS                     (0)
#define ASM_CH01_IBUF_RDPNT_MASK                    (0xFFFFFFFF<<ASM_CH01_IBUF_RDPNT_POS)
#define ASM_CH01_IBUF_WRPNT             (AFE_BASE + 0x1150)
#define ASM_CH01_IBUF_WRPNT_POS                     (0)
#define ASM_CH01_IBUF_WRPNT_MASK                    (0xFFFFFFFF<<ASM_CH01_IBUF_WRPNT_POS)
#define ASM_CH01_OBUF_WRPNT             (AFE_BASE + 0x1160)
#define ASM_CH01_OBUF_WRPNT_POS                     (0)
#define ASM_CH01_OBUF_WRPNT_MASK                    (0xFFFFFFFF<<ASM_CH01_OBUF_WRPNT_POS)
#define ASM_CH01_OBUF_RDPNT             (AFE_BASE + 0x1170)
#define ASM_CH01_OBUF_RDPNT_POS                     (0)
#define ASM_CH01_OBUF_RDPNT_MASK                    (0xFFFFFFFF<<ASM_CH01_OBUF_RDPNT_POS)
#define ASM_IBUF_INTR_CNT0              (AFE_BASE + 0x1180)
#define ASM_IBUF_INTR_CNT0_POS                      (8)
#define ASM_IBUF_INTR_CNT0_MASK                     (0x3FFF<<ASM_IBUF_INTR_CNT0_POS)
#define ASM_OBUF_INTR_CNT0              (AFE_BASE + 0x1188)
#define ASM_OBUF_INTR_CNT0_POS                      (8)
#define ASM_OBUF_INTR_CNT0_MASK                     (0x3FFF<<ASM_OBUF_INTR_CNT0_POS)
#define ASM_BAK_REG                     (AFE_BASE + 0x1190)
#define ASM_BAK_REG_RESULT_SEL_POS                  (0)
#define ASM_BAK_REG_RESULT_SEL_MASK                 (7<<ASM_BAK_REG_RESULT_SEL_POS)
#define ASM_FREQ_CALI_CTRL              (AFE_BASE + 0x1194)
#define ASM_FREQ_CALI_CTRL_FREQ_CALC_BUSY_POS       (20)
#define ASM_FREQ_CALI_CTRL_FREQ_CALC_BUSY_MASK      (1<<ASM_FREQ_CALI_CTRL_FREQ_CALC_BUSY_POS)
#define ASM_FREQ_CALI_CTRL_COMP_FREQRES_EN_POS      (19)
#define ASM_FREQ_CALI_CTRL_COMP_FREQRES_EN_MASK     (1<<ASM_FREQ_CALI_CTRL_COMP_FREQRES_EN_POS)
#define ASM_FREQ_CALI_CTRL_SRC_SEL_POS              (16)
#define ASM_FREQ_CALI_CTRL_SRC_SEL_MASK             (3<<ASM_FREQ_CALI_CTRL_SRC_SEL_POS)
#define ASM_FREQ_CALI_CTRL_BYPASS_DEGLITCH_POS      (15)
#define ASM_FREQ_CALI_CTRL_BYPASS_DEGLITCH_MASK     (1<<ASM_FREQ_CALI_CTRL_BYPASS_DEGLITCH_POS)
#define ASM_FREQ_CALI_CTRL_MAX_GWIDTH_POS           (12)
#define ASM_FREQ_CALI_CTRL_MAX_GWIDTH_MASK          (7<<ASM_FREQ_CALI_CTRL_MAX_GWIDTH_POS)
#define ASM_FREQ_CALI_CTRL_AUTO_FS2_UPDATE_POS      (11)
#define ASM_FREQ_CALI_CTRL_AUTO_FS2_UPDATE_MASK     (1<<ASM_FREQ_CALI_CTRL_AUTO_FS2_UPDATE_POS)
#define ASM_FREQ_CALI_CTRL_AUTO_RESTART_POS         (10)
#define ASM_FREQ_CALI_CTRL_AUTO_RESTART_MASK        (1<<ASM_FREQ_CALI_CTRL_AUTO_RESTART_POS)
#define ASM_FREQ_CALI_CTRL_FREQ_UPDATE_FS2_POS      (9)
#define ASM_FREQ_CALI_CTRL_FREQ_UPDATE_FS2_MASK     (1<<ASM_FREQ_CALI_CTRL_FREQ_UPDATE_FS2_POS)
#define ASM_FREQ_CALI_CTRL_CALI_EN_POS              (8)
#define ASM_FREQ_CALI_CTRL_CALI_EN_MASK             (1<<ASM_FREQ_CALI_CTRL_CALI_EN_POS)
#define ASM_FREQ_CALI_CYC               (AFE_BASE + 0x1198)
#define ASM_FREQ_CALI_CYC_POS                       (8)
#define ASM_FREQ_CALI_CYC_MASK                      (0xFFFF<<ASM_FREQ_CALI_CYC_POS)
#define ASM_PRD_CALI_RESULT             (AFE_BASE + 0x119C)
#define ASM_PRD_CALI_RESULT_POS                     (0)
#define ASM_PRD_CALI_RESULT_MASK                    (0xFFFFFF<<ASM_PRD_CALI_RESULT_POS)
#define ASM_FREQ_CALI_RESULT            (AFE_BASE + 0x11A0)
#define ASM_FREQ_CALI_RESULT_POS                    (0)
#define ASM_FREQ_CALI_RESULT_MASK                   (0xFFFFFF<<ASM_FREQ_CALI_RESULT_POS)
#define ASM_IBUF_SAMPLE_CNT             (AFE_BASE + 0x11B0)
#define ASM_OBUF_SAMPLE_CNT             (AFE_BASE + 0x11B4)
#define ASM_OBUF_LACK_IRQ_CTL           (AFE_BASE + 0x11B8)
#define ASM_OBUF_LACK_IRQ_THD           (AFE_BASE + 0x11BC)
#define ASM_SMPCNT_CONF                 (AFE_BASE + 0x11C0)
#define ASM_SMPCNT_CONF_IRQ_SEL_POS                 (10)
#define ASM_SMPCNT_CONF_IRQ_SEL_MASK                (1<<ASM_SMPCNT_CONF_IRQ_SEL_POS)
#define ASM_SMPCNT_CONF_IRQ_MASK_POS                (9)
#define ASM_SMPCNT_CONF_IRQ_MASK_MASK               (1<<ASM_SMPCNT_CONF_IRQ_MASK_POS)
#define ASM_SMPCNT_CONF_IRQ_CLEAR_POS               (8)
#define ASM_SMPCNT_CONF_IRQ_CLEAR_MASK              (1<<ASM_SMPCNT_CONF_IRQ_CLEAR_POS)
#define ASM_SMPCNT_CONF_HW_LATCH_EN_POS             (5)
#define ASM_SMPCNT_CONF_HW_LATCH_EN_MASK            (1<<ASM_SMPCNT_CONF_HW_LATCH_EN_POS)
#define ASM_SMPCNT_CONF_SW_LATCH_POS                (4)
#define ASM_SMPCNT_CONF_SW_LATCH_MASK               (1<<ASM_SMPCNT_CONF_SW_LATCH_POS)
#define ASM_SMPCNT_CONF_RESET_POS                   (3)
#define ASM_SMPCNT_CONF_RESET_MASK                  (1<<ASM_SMPCNT_CONF_RESET_POS)
#define ASM_SMPCNT_CONF_SMPCNT_ENABLE2_POS          (1)
#define ASM_SMPCNT_CONF_SMPCNT_ENABLE2_MASK         (1<<ASM_SMPCNT_CONF_SMPCNT_ENABLE2_POS)
#define ASM_SMPCNT_CONF_SMPCNT_ENABLE1_POS          (0)
#define ASM_SMPCNT_CONF_SMPCNT_ENABLE1_MASK         (1<<ASM_SMPCNT_CONF_SMPCNT_ENABLE1_POS)
#define ASM_SMPCNT_WRAP_VAL             (AFE_BASE + 0x11C4)
#define ASM_SMPCNT_IRQ_VAL              (AFE_BASE + 0x11C8)
#define ASM_SMPCNT1_LATCH               (AFE_BASE + 0x11CC)
#define ASM_SMPCNT2_LATCH               (AFE_BASE + 0x11D0)
#define ASM_CALI_DENOMINATOR            (AFE_BASE + 0x11D8)
#define ASM_CALI_DENOMINATOR_POS                    (0)
#define ASM_CALI_DENOMINATOR_MASK                   (0xFFFFFF<<ASM_CALI_DENOMINATOR_POS)
#define ASM_MAX_OUT_PER_IN0             (AFE_BASE + 0x11E0)
#define ASM_MAX_OUT_PER_IN0_POS                     (8)
#define ASM_MAX_OUT_PER_IN0_MASK                    (0xF<<ASM_MAX_OUT_PER_IN0_POS)
#define ASM_IN_BUF_MON0                 (AFE_BASE + 0x11E8)
#define ASM_IN_BUF_MON1                 (AFE_BASE + 0x11EC)
#define ASM_IIR_CRAM_ADDR               (AFE_BASE + 0x11F0)
#define ASM_IIR_CRAM_ADDR_POS                       (8)
#define ASM_IIR_CRAM_ADDR_MASK                      (0xFF<<ASM_IIR_CRAM_ADDR_POS)
#define ASM_IIR_CRAM_DATA               (AFE_BASE + 0x11F4)
#define ASM_IIR_CRAM_DATA_POS                       (0)
#define ASM_IIR_CRAM_DATA_MASK                      (0xFFFFFFFF<<ASM_IIR_CRAM_DATA_POS)
#define ASM_OUT_BUF_MON0                (AFE_BASE + 0x11F8)
#define ASM_OUT_BUF_MON0_WDLE_CNT_POS               (8)
#define ASM_OUT_BUF_MON0_WDLE_CNT_MASK              (0xFF<<ASM_OUT_BUF_MON0_WDLE_CNT_POS)
#define ASM_OUT_BUF_MON0_ASRC_WRITE_DONE_POS        (0)
#define ASM_OUT_BUF_MON0_ASRC_WRITE_DONE_MASK       (1<<ASM_OUT_BUF_MON0_ASRC_WRITE_DONE_POS)
#define ASM_OUT_BUF_MON1                (AFE_BASE + 0x11FC)
#define ASM_OUT_BUF_MON1_ASRC_WR_ADR_POS            (4)
#define ASM_OUT_BUF_MON1_ASRC_WR_ADR_MASK           (0x0FFFFFFF<<ASM_OUT_BUF_MON1_ASRC_WR_ADR_POS)
#define ASM2_GEN_CONF                   (AFE_BASE + 0x1200)
#define ASM2_IER                        (AFE_BASE + 0x1204)
#define ASM2_IFR                        (AFE_BASE + 0x1208)
#define ASM2_CH01_CNFG                  (AFE_BASE + 0x1210)
#define ASM2_FREQUENCY_0                (AFE_BASE + 0x1220)
#define ASM2_FREQUENCY_1                (AFE_BASE + 0x1224)
#define ASM2_FREQUENCY_2                (AFE_BASE + 0x1228)
#define ASM2_FREQUENCY_3                (AFE_BASE + 0x122C)
#define ASM2_IBUF_SADR                  (AFE_BASE + 0x1230)
#define ASM2_IBUF_SIZE                  (AFE_BASE + 0x1234)
#define ASM2_OBUF_SADR                  (AFE_BASE + 0x1238)
#define ASM2_OBUF_SIZE                  (AFE_BASE + 0x123C)
#define ASM2_CH01_IBUF_RDPNT            (AFE_BASE + 0x1240)
#define ASM2_CH01_IBUF_WRPNT            (AFE_BASE + 0x1250)
#define ASM2_CH01_IBUF_WRPNT_POS        (0)
#define ASM2_CH01_IBUF_WRPNT_MASK       (0xFFFFFFFF<<ASM2_CH01_IBUF_WRPNT_POS)
#define ASM2_CH01_OBUF_WRPNT            (AFE_BASE + 0x1260)
#define ASM2_CH01_OBUF_RDPNT            (AFE_BASE + 0x1270)
#define ASM2_IBUF_INTR_CNT0             (AFE_BASE + 0x1280)
#define ASM2_OBUF_INTR_CNT0             (AFE_BASE + 0x1288)
#define ASM2_BAK_REG                    (AFE_BASE + 0x1290)
#define ASM2_FREQ_CALI_CTRL             (AFE_BASE + 0x1294)
#define ASM2_FREQ_CALI_CYC              (AFE_BASE + 0x1298)
#define ASM2_PRD_CALI_RESULT            (AFE_BASE + 0x129C)
#define ASM2_FREQ_CALI_RESULT           (AFE_BASE + 0x12A0)
#define ASM2_IBUF_SAMPLE_CNT            (AFE_BASE + 0x12B0)
#define ASM2_OBUF_SAMPLE_CNT            (AFE_BASE + 0x12B4)
#define ASM2_OBUF_LACK_IRQ_CTL          (AFE_BASE + 0x12B8)
#define ASM2_OBUF_LACK_IRQ_THD          (AFE_BASE + 0x12BC)
#define ASM2_SMPCNT_CONF                (AFE_BASE + 0x12C0)
#define ASM2_SMPCNT_WRAP_VAL            (AFE_BASE + 0x12C4)
#define ASM2_SMPCNT_IRQ_VAL             (AFE_BASE + 0x12C8)
#define ASM2_SMPCNT1_LATCH              (AFE_BASE + 0x12CC)
#define ASM2_SMPCNT2_LATCH              (AFE_BASE + 0x12D0)
#define ASM2_CALI_DENOMINATOR           (AFE_BASE + 0x12D8)
#define ASM2_MAX_OUT_PER_IN0            (AFE_BASE + 0x12E0)
#define ASM2_IN_BUF_MON0                (AFE_BASE + 0x12E8)
#define ASM2_IN_BUF_MON1                (AFE_BASE + 0x12EC)
#define ASM2_IIR_CRAM_ADDR              (AFE_BASE + 0x12F0)
#define ASM2_IIR_CRAM_DATA              (AFE_BASE + 0x12F4)
#define ASM2_OUT_BUF_MON0               (AFE_BASE + 0x12F8)
#define ASM2_OUT_BUF_MON1               (AFE_BASE + 0x12FC)
#define AFE_CONN24                      (AFE_BASE + 0x1300)
#define AFE_CONN25                      (AFE_BASE + 0x1304)
#define AFE_CONN26                      (AFE_BASE + 0x1308)
#define AFE_CONN27                      (AFE_BASE + 0x130c)
#define AFE_CONN28                      (AFE_BASE + 0x1310)
#define AFE_CONN29                      (AFE_BASE + 0x1314)
#define AFE_CONN30                      (AFE_BASE + 0x1318)
#define AFE_CONN31                      (AFE_BASE + 0x131c)
#define AFE_CONN32                      (AFE_BASE + 0x1320)
#define AFE_CONN33                      (AFE_BASE + 0x1324)
#define AFE_CONN34                      (AFE_BASE + 0x1328)
#define AFE_CONN35                      (AFE_BASE + 0x132c)
#define AFE_CONN36                      (AFE_BASE + 0x1330)
#define AFE_CONN37                      (AFE_BASE + 0x1334)
#define AFE_CONN38                      (AFE_BASE + 0x1598)
#define AFE_CONN39                      (AFE_BASE + 0x159c)
#define AFE_CONN40                      (AFE_BASE + 0x15a0)
#define AFE_CONN41                      (AFE_BASE + 0x15a4)
#define AFE_CONN42                      (AFE_BASE + 0x15a8)
#define AFE_CONN43                      (AFE_BASE + 0x15ac)
#define AFE_CONN0_1                     (AFE_BASE + 0x1340)
#define AFE_CONN1_1                     (AFE_BASE + 0x1344)
#define AFE_CONN2_1                     (AFE_BASE + 0x1348)
#define AFE_CONN3_1                     (AFE_BASE + 0x134c)
#define AFE_CONN4_1                     (AFE_BASE + 0x1350)
#define AFE_CONN5_1                     (AFE_BASE + 0x1354)
#define AFE_CONN6_1                     (AFE_BASE + 0x1358)
#define AFE_CONN7_1                     (AFE_BASE + 0x135c)
#define AFE_CONN8_1                     (AFE_BASE + 0x1360)
#define AFE_CONN9_1                     (AFE_BASE + 0x1364)
#define AFE_CONN10_1                    (AFE_BASE + 0x1368)
#define AFE_CONN11_1                    (AFE_BASE + 0x136c)
#define AFE_CONN12_1                    (AFE_BASE + 0x1370)
#define AFE_CONN13_1                    (AFE_BASE + 0x1374)
#define AFE_CONN14_1                    (AFE_BASE + 0x1378)
#define AFE_CONN15_1                    (AFE_BASE + 0x137c)
#define AFE_CONN16_1                    (AFE_BASE + 0x1380)
#define AFE_CONN17_1                    (AFE_BASE + 0x1384)
#define AFE_CONN18_1                    (AFE_BASE + 0x1388)
#define AFE_CONN19_1                    (AFE_BASE + 0x138c)
#define AFE_CONN20_1                    (AFE_BASE + 0x1390)
#define AFE_CONN21_1                    (AFE_BASE + 0x1394)
#define AFE_CONN22_1                    (AFE_BASE + 0x1398)
#define AFE_CONN23_1                    (AFE_BASE + 0x139c)
#define AFE_CONN24_1                    (AFE_BASE + 0x13a0)
#define AFE_CONN25_1                    (AFE_BASE + 0x13a4)
#define AFE_CONN26_1                    (AFE_BASE + 0x13a8)
#define AFE_CONN27_1                    (AFE_BASE + 0x13ac)
#define AFE_CONN28_1                    (AFE_BASE + 0x13b0)
#define AFE_CONN29_1                    (AFE_BASE + 0x13b4)
#define AFE_CONN30_1                    (AFE_BASE + 0x13b8)
#define AFE_CONN31_1                    (AFE_BASE + 0x13bc)
#define AFE_CONN32_1                    (AFE_BASE + 0x13c0)
#define AFE_CONN33_1                    (AFE_BASE + 0x13c4)
#define AFE_CONN34_1                    (AFE_BASE + 0x13c8)
#define AFE_CONN35_1                    (AFE_BASE + 0x13cc)
#define AFE_CONN36_1                    (AFE_BASE + 0x13d0)
#define AFE_CONN37_1                    (AFE_BASE + 0x13d4)
#define AFE_CONN38_1                    (AFE_BASE + 0x15b0)
#define AFE_CONN39_1                    (AFE_BASE + 0x15b4)
#define AFE_CONN40_1                    (AFE_BASE + 0x15b8)
#define AFE_CONN41_1                    (AFE_BASE + 0x15bc)
#define AFE_CONN42_1                    (AFE_BASE + 0x15c0)
#define AFE_CONN43_1                    (AFE_BASE + 0x15c4)
#define AFE_CONN_RS_1                   (AFE_BASE + 0x13d8)
#define AFE_CONN_DI_1                   (AFE_BASE + 0x13dc)
#define AFE_CONN_24BIT_1                (AFE_BASE + 0x13e0)
#define AFE_GAIN3_CON0                  (AFE_BASE + 0x13e4)
#define AFE_GAIN3_CON1                  (AFE_BASE + 0x13e8)
#define AFE_GAIN3_CON2                  (AFE_BASE + 0x13ec)
#define AFE_GAIN3_CON3                  (AFE_BASE + 0x13f0)
#define AFE_GAIN3_CUR                   (AFE_BASE + 0x13f4)
#define AFE_GAIN4_CON0                  (AFE_BASE + 0x1420)
#define AFE_GAIN4_CON1                  (AFE_BASE + 0x1424)
#define AFE_GAIN4_CON2                  (AFE_BASE + 0x1428)
#define AFE_GAIN4_CON3                  (AFE_BASE + 0x142c)
#define AFE_GAIN4_CUR                   (AFE_BASE + 0x1430)
#define AFE_SRC_CONT_CON0               (AFE_BASE + 0x13f8)
#define AFE_I2S_SLV_ENGEN_CON0          (AFE_BASE + 0x1400)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN3_SEL_POS        (29)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN3_SEL_MASK       (7<<AFE_I2S_SLV_ENGEN_CON0_GAIN3_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN3_MODE_POS       (28)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN3_MODE_MASK      (1<<AFE_I2S_SLV_ENGEN_CON0_GAIN3_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN2_SEL_POS        (25)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN2_SEL_MASK       (7<<AFE_I2S_SLV_ENGEN_CON0_GAIN2_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN2_MODE_POS       (24)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN2_MODE_MASK      (1<<AFE_I2S_SLV_ENGEN_CON0_GAIN2_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN_SEL_POS         (21)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON0_GAIN_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN_MODE_POS        (20)
#define AFE_I2S_SLV_ENGEN_CON0_GAIN_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON0_GAIN_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON0_VUL3_SEL_POS         (17)
#define AFE_I2S_SLV_ENGEN_CON0_VUL3_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON0_VUL3_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON0_VUL3_MODE_POS        (16)
#define AFE_I2S_SLV_ENGEN_CON0_VUL3_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON0_VUL3_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON0_VUL2_SEL_POS         (13)
#define AFE_I2S_SLV_ENGEN_CON0_VUL2_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON0_VUL2_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON0_VUL2_MODE_POS        (12)
#define AFE_I2S_SLV_ENGEN_CON0_VUL2_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON0_VUL2_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON0_VUL_SEL_POS          (9)
#define AFE_I2S_SLV_ENGEN_CON0_VUL_SEL_MASK         (7<<AFE_I2S_SLV_ENGEN_CON0_VUL_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON0_VUL_MODE_POS         (8)
#define AFE_I2S_SLV_ENGEN_CON0_VUL_MODE_MASK        (1<<AFE_I2S_SLV_ENGEN_CON0_VUL_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON0_AWB2_SEL_POS         (5)
#define AFE_I2S_SLV_ENGEN_CON0_AWB2_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON0_AWB2_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON0_AWB2_MODE_POS        (4)
#define AFE_I2S_SLV_ENGEN_CON0_AWB2_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON0_AWB2_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON0_AWB_SEL_POS          (1)
#define AFE_I2S_SLV_ENGEN_CON0_AWB_SEL_MASK         (7<<AFE_I2S_SLV_ENGEN_CON0_AWB_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON0_AWB_MODE_POS         (0)
#define AFE_I2S_SLV_ENGEN_CON0_AWB_MODE_MASK        (1<<AFE_I2S_SLV_ENGEN_CON0_AWB_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON1          (AFE_BASE + 0x1404)
#define AFE_I2S_SLV_ENGEN_CON1_IRQ1_SEL_POS         (29)
#define AFE_I2S_SLV_ENGEN_CON1_IRQ1_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON1_IRQ1_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON1_IRQ1_MODE_POS        (28)
#define AFE_I2S_SLV_ENGEN_CON1_IRQ1_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON1_IRQ1_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON1_IRQ0_SEL_POS         (25)
#define AFE_I2S_SLV_ENGEN_CON1_IRQ0_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON1_IRQ0_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON1_IRQ0_MODE_POS        (24)
#define AFE_I2S_SLV_ENGEN_CON1_IRQ0_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON1_IRQ0_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON1_DL3_SEL_POS          (21)
#define AFE_I2S_SLV_ENGEN_CON1_DL3_SEL_MASK         (3<<AFE_I2S_SLV_ENGEN_CON1_DL3_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON1_DL3_MODE_POS         (20)
#define AFE_I2S_SLV_ENGEN_CON1_DL3_MODE_MASK        (1<<AFE_I2S_SLV_ENGEN_CON1_DL3_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON1_DL2_SEL_POS          (17)
#define AFE_I2S_SLV_ENGEN_CON1_DL2_SEL_MASK         (3<<AFE_I2S_SLV_ENGEN_CON1_DL2_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON1_DL2_MODE_POS         (16)
#define AFE_I2S_SLV_ENGEN_CON1_DL2_MODE_MASK        (1<<AFE_I2S_SLV_ENGEN_CON1_DL2_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON1_DL12_SEL_POS         (13)
#define AFE_I2S_SLV_ENGEN_CON1_DL12_SEL_MASK        (3<<AFE_I2S_SLV_ENGEN_CON1_DL12_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON1_DL12_MODE_POS        (12)
#define AFE_I2S_SLV_ENGEN_CON1_DL12_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON1_DL12_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON1_DL1_SEL_POS          (9)
#define AFE_I2S_SLV_ENGEN_CON1_DL1_SEL_MASK         (3<<AFE_I2S_SLV_ENGEN_CON1_DL1_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON1_DL1_MODE_POS         (8)
#define AFE_I2S_SLV_ENGEN_CON1_DL1_MODE_MASK        (1<<AFE_I2S_SLV_ENGEN_CON1_DL1_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON1_SRC2_SEL_POS         (5)
#define AFE_I2S_SLV_ENGEN_CON1_SRC2_SEL_MASK        (3<<AFE_I2S_SLV_ENGEN_CON1_SRC2_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON1_SRC2_MODE_POS        (4)
#define AFE_I2S_SLV_ENGEN_CON1_SRC2_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON1_SRC2_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON1_SRC1_SEL_POS         (1)
#define AFE_I2S_SLV_ENGEN_CON1_SRC1_SEL_MASK        (3<<AFE_I2S_SLV_ENGEN_CON1_SRC1_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON1_SRC1_MODE_POS        (0)
#define AFE_I2S_SLV_ENGEN_CON1_SRC1_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON1_SRC1_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON2          (AFE_BASE + 0x1408)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ12_SEL_POS        (29)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ12_SEL_MASK       (7<<AFE_I2S_SLV_ENGEN_CON2_IRQ12_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ12_MODE_POS       (28)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ12_MODE_MASK      (1<<AFE_I2S_SLV_ENGEN_CON2_IRQ12_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ11_SEL_POS        (25)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ11_SEL_MASK       (7<<AFE_I2S_SLV_ENGEN_CON2_IRQ11_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ11_MODE_POS       (24)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ11_MODE_MASK      (1<<AFE_I2S_SLV_ENGEN_CON2_IRQ11_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ7_SEL_POS         (21)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ7_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON2_IRQ7_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ7_MODE_POS        (20)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ7_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON2_IRQ7_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ6_SEL_POS         (17)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ6_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON2_IRQ6_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ6_MODE_POS        (16)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ6_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON2_IRQ6_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ5_SEL_POS         (13)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ5_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON2_IRQ5_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ5_MODE_POS        (12)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ5_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON2_IRQ5_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ4_SEL_POS         (9)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ4_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON2_IRQ4_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ4_MODE_POS        (8)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ4_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON2_IRQ4_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ3_SEL_POS         (5)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ3_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON2_IRQ3_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ3_MODE_POS        (4)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ3_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON2_IRQ3_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ2_SEL_POS         (1)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ2_SEL_MASK        (7<<AFE_I2S_SLV_ENGEN_CON2_IRQ2_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ2_MODE_POS        (0)
#define AFE_I2S_SLV_ENGEN_CON2_IRQ2_MODE_MASK       (1<<AFE_I2S_SLV_ENGEN_CON2_IRQ2_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON3          (AFE_BASE + 0x140c)
#define AFE_I2S_SLV_ENGEN_CON3_UP_IN_SEL_POS        (21)
#define AFE_I2S_SLV_ENGEN_CON3_UP_IN_SEL_MASK       (7<<AFE_I2S_SLV_ENGEN_CON3_UP_IN_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON3_UP_IN_MODE_POS       (20)
#define AFE_I2S_SLV_ENGEN_CON3_UP_IN_MODE_MASK      (1<<AFE_I2S_SLV_ENGEN_CON3_UP_IN_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN23_IN_SEL_POS    (17)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN23_IN_SEL_MASK   (7<<AFE_I2S_SLV_ENGEN_CON3_DOWN23_IN_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN23_IN_MODE_POS   (16)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN23_IN_MODE_MASK  (1<<AFE_I2S_SLV_ENGEN_CON3_DOWN23_IN_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN01_IN_SEL_POS    (13)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN01_IN_SEL_MASK   (7<<AFE_I2S_SLV_ENGEN_CON3_DOWN01_IN_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN01_IN_MODE_POS   (12)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN01_IN_MODE_MASK  (1<<AFE_I2S_SLV_ENGEN_CON3_DOWN01_IN_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON3_UP_OUT_SEL_POS       (9)
#define AFE_I2S_SLV_ENGEN_CON3_UP_OUT_SEL_MASK      (7<<AFE_I2S_SLV_ENGEN_CON3_UP_OUT_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON3_UP_OUT_MODE_POS      (8)
#define AFE_I2S_SLV_ENGEN_CON3_UP_OUT_MODE_MASK     (1<<AFE_I2S_SLV_ENGEN_CON3_UP_OUT_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN23_OUT_SEL_POS   (5)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN23_OUT_SEL_MASK  (7<<AFE_I2S_SLV_ENGEN_CON3_DOWN23_OUT_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN23_OUT_MODE_POS  (4)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN23_OUT_MODE_MASK (1<<AFE_I2S_SLV_ENGEN_CON3_DOWN23_OUT_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN01_OUT_SEL_POS   (1)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN01_OUT_SEL_MASK  (7<<AFE_I2S_SLV_ENGEN_CON3_DOWN01_OUT_SEL_POS)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN01_OUT_MODE_POS  (0)
#define AFE_I2S_SLV_ENGEN_CON3_DOWN01_OUT_MODE_MASK (1<<AFE_I2S_SLV_ENGEN_CON3_DOWN01_OUT_MODE_POS)
#define AFE_I2S_SLV_ENGEN_CON4          (AFE_BASE + 0x1410)
#define AFE_ADDA_ANC_UL_SRC_CON0        (AFE_BASE + 0x1504)
#define AFE_ADDA_ANC_UL_SRC_CON0_ANC_LOOPBACK_POS   (11)
#define AFE_ADDA_ANC_UL_SRC_CON0_ANC_LOOPBACK_MASK  (1<<AFE_ADDA_ANC_UL_SRC_CON0_ANC_LOOPBACK_POS)
#define AFE_ADDA_ANC_UL_SRC_CON0_LOOPBACK_MUX_POS   (24)
#define AFE_ADDA_ANC_UL_SRC_CON0_LOOPBACK_MUX_MASK  (1<<AFE_ADDA_ANC_UL_SRC_CON0_LOOPBACK_MUX_POS)
#define AFE_ADDA_ANC_UL_SRC_CON1        (AFE_BASE + 0x1508)
#define AFE_ADDA_ANC_SRC_DEBUG          (AFE_BASE + 0x150c)
#define AFE_ADDA_ANC_SRC_DEBUG_MON0     (AFE_BASE + 0x1510)
#define AFE_ADDA_ANC_UL_SRC_MON0        (AFE_BASE + 0x1518)
#define AFE_ADDA_ANC_UL_SRC_MON1        (AFE_BASE + 0x151C)
#define AFE_ADDA_ANC_IIR_COEF_02_01     (AFE_BASE + 0x1520)
#define AFE_ADDA_ANC_IIR_COEF_04_03     (AFE_BASE + 0x1524)
#define AFE_ADDA_ANC_IIR_COEF_06_05     (AFE_BASE + 0x1528)
#define AFE_ADDA_ANC_IIR_COEF_08_07     (AFE_BASE + 0x152C)
#define AFE_ADDA_ANC_IIR_COEF_10_09     (AFE_BASE + 0x1530)
#define AFE_DEC_INT_CON0                (AFE_BASE + 0x1570)
#define AFE_DEC_INT_CON0_UP_CH23_ENABLE_POS         (6)
#define AFE_DEC_INT_CON0_UP_CH23_ENABLE_MASK        (0x03<<AFE_DEC_INT_CON0_UP_CH23_ENABLE_POS)
#define AFE_DEC_INT_CON0_UP_CH01_ENABLE_POS         (4)
#define AFE_DEC_INT_CON0_UP_CH01_ENABLE_MASK        (0x03<<AFE_DEC_INT_CON0_UP_CH01_ENABLE_POS)
#define AFE_DEC_INT_CON0_DOWN_CH23_ENABLE_POS       (2)
#define AFE_DEC_INT_CON0_DOWN_CH23_ENABLE_MASK      (0x03<<AFE_DEC_INT_CON0_DOWN_CH23_ENABLE_POS)
#define AFE_DEC_INT_CON0_DOWN_CH01_ENABLE_POS       (0)
#define AFE_DEC_INT_CON0_DOWN_CH01_ENABLE_MASK      (0x03<<AFE_DEC_INT_CON0_DOWN_CH01_ENABLE_POS)
#define AFE_DEC_INT_CON1                (AFE_BASE + 0x1574)
#define AFE_DEC_INT_CON1_UP_CH23_INPUT_RATE_POS     (28)
#define AFE_DEC_INT_CON1_UP_CH23_INPUT_RATE_MASK    (0x0F<<AFE_DEC_INT_CON1_UP_CH23_INPUT_RATE_POS)
#define AFE_DEC_INT_CON1_UP_CH23_OUTPUT_RATE_POS    (24)
#define AFE_DEC_INT_CON1_UP_CH23_OUTPUT_RATE_MASK   (0x0F<<AFE_DEC_INT_CON1_UP_CH23_OUTPUT_RATE_POS)
#define AFE_DEC_INT_CON1_UP_CH01_INPUT_RATE_POS     (20)
#define AFE_DEC_INT_CON1_UP_CH01_INPUT_RATE_MASK    (0x0F<<AFE_DEC_INT_CON1_UP_CH01_INPUT_RATE_POS)
#define AFE_DEC_INT_CON1_DOWN_CH23_INPUT_RATE_POS   (16)
#define AFE_DEC_INT_CON1_DOWN_CH23_INPUT_RATE_MASK  (0x0F<<AFE_DEC_INT_CON1_DOWN_CH23_INPUT_RATE_POS)
#define AFE_DEC_INT_CON1_DOWN_CH01_INPUT_RATE_POS   (12)
#define AFE_DEC_INT_CON1_DOWN_CH01_INPUT_RATE_MASK  (0x0F<<AFE_DEC_INT_CON1_DOWN_CH01_INPUT_RATE_POS)
#define AFE_DEC_INT_CON1_UP_CH01_OUTPUT_RATE_POS    (8)
#define AFE_DEC_INT_CON1_UP_CH01_OUTPUT_RATE_MASK   (0x0F<<AFE_DEC_INT_CON1_UP_CH01_OUTPUT_RATE_POS)
#define AFE_DEC_INT_CON1_DOWN_CH23_OUTPUT_RATE_POS  (4)
#define AFE_DEC_INT_CON1_DOWN_CH23_OUTPUT_RATE_MASK (0x0F<<AFE_DEC_INT_CON1_DOWN_CH23_OUTPUT_RATE_POS)
#define AFE_DEC_INT_CON1_DOWN_CH01_OUTPUT_RATE_POS  (0)
#define AFE_DEC_INT_CON1_DOWN_CH01_OUTPUT_RATE_MASK (0x0F<<AFE_DEC_INT_CON1_DOWN_CH01_OUTPUT_RATE_POS)
#define AFE_DEC_INT_CON2                (AFE_BASE + 0x1578)
#define AFE_DEC_INT_CON2_UP_CH23_RATIO_POS          (12)
#define AFE_DEC_INT_CON2_UP_CH23_RATIO_MASK         (0x07<<AFE_DEC_INT_CON2_UP_CH23_RATIO_POS)
#define AFE_DEC_INT_CON2_UP_CH01_CIC_POS            (11)
#define AFE_DEC_INT_CON2_UP_CH01_CIC_MASK           (0x01<<AFE_DEC_INT_CON2_UP_CH01_CIC_POS)
#define AFE_DEC_INT_CON2_UP_CH01_RATIO_POS          (8)
#define AFE_DEC_INT_CON2_UP_CH01_RATIO_MASK         (0x07<<AFE_DEC_INT_CON2_UP_CH01_RATIO_POS)
#define AFE_DEC_INT_CON2_DOWN_CH23_RATIO_POS        (4)
#define AFE_DEC_INT_CON2_DOWN_CH23_RATIO_MASK       (0x07<<AFE_DEC_INT_CON2_DOWN_CH23_RATIO_POS)
#define AFE_DEC_INT_CON2_DOWN_CH01_RATIO_POS        (0)
#define AFE_DEC_INT_CON2_DOWN_CH01_RATIO_MASK       (0x07<<AFE_DEC_INT_CON2_DOWN_CH01_RATIO_POS)
#define AFE_VUL3_BASE                   (AFE_BASE + 0x1580)
#define AFE_VUL3_END                    (AFE_BASE + 0x1584)
#define AFE_VUL3_CUR                    (AFE_BASE + 0x158c)
#define AFE_I2S_SPDIF_CON0              (AFE_BASE + 0x1590)
#define AFE_I2S_SPDIF_CON0_ENABLE_POS               (0)
#define AFE_I2S_SPDIF_CON0_ENABLE_MASK              (1<<AFE_I2S_SPDIF_CON0_ENABLE_POS)
#define AFE_I2S_SPDIF_CON0_CLOCK_ON_POS             (1)
#define AFE_I2S_SPDIF_CON0_CLOCK_ON_MASK            (1<<AFE_I2S_SPDIF_CON0_CLOCK_ON_POS)
#define AFE_I2S_SPDIF_CON0_AFIFO_RING_POS           (4)
#define AFE_I2S_SPDIF_CON0_AFIFO_RING_MASK          (1<<AFE_I2S_SPDIF_CON0_AFIFO_RING_POS)
#define AFE_I2S_SPDIF_CON0_CLOCK_SOURCE_POS         (5)
#define AFE_I2S_SPDIF_CON0_CLOCK_SOURCE_MASK        (3<<AFE_I2S_SPDIF_CON0_CLOCK_SOURCE_POS)
#define AFE_I2S_SPDIF_CON0_DIVIDER_POS              (12)
#define AFE_I2S_SPDIF_CON0_DIVIDER_MASK             (7<<AFE_I2S_SPDIF_CON0_DIVIDER_POS)
#define AFE_I2S_SPDIF_CON0_RELOAD_POS               (16)
#define AFE_I2S_SPDIF_CON0_RELOAD_MASK              (1<<AFE_I2S_SPDIF_CON0_RELOAD_POS)
#define AFE_I2S_SPDIF_CON0_SELECTION_POS            (17)
#define AFE_I2S_SPDIF_CON0_SELECTION_MASK           (3<<AFE_I2S_SPDIF_CON0_SELECTION_POS)
#define AFE_I2S_SPDIF_CON0_SINETONE_POS             (20)
#define AFE_I2S_SPDIF_CON0_SINETONE_MASK            (1<<AFE_I2S_SPDIF_CON0_SINETONE_POS)
#define AFE_I2S_SPDIF_CON0_DEBUG_POS                (28)
#define AFE_I2S_SPDIF_CON0_DEBUG_MASK               (7<<AFE_I2S_SPDIF_CON0_DEBUG_POS)

#define AFE_CLD_CL_CON0                 (AFE_BASE + 0x1900)
#define AFE_CLD_CL_CON0_AUD_CLD_ENABLE_POS                  (0)
#define AFE_CLD_CL_CON0_AUD_CLD_ENABLE_MASK                 (1<<AFE_CLD_CL_CON0_AUD_CLD_ENABLE_POS)
#define AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_RST_POS              (17)
#define AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_RST_MASK             (1<<AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_RST_POS)
#define AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_FORCE_RST_POS        (18)
#define AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_FORCE_RST_MASK       (1<<AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_FORCE_RST_POS)
#define AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_RST_MODE_SEL_POS     (19)
#define AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_RST_MODE_SEL_MASK    (1<<AFE_CLD_CL_CON0_AUD_CLD_SDM_SW_RST_MODE_SEL_POS)
#define AFE_CLD_CL_CON0_SDM_ZERO_CNT_POS                    (20)
#define AFE_CLD_CL_CON0_SDM_ZERO_CNT_MASK                   (31<<AFE_CLD_CL_CON0_SDM_ZERO_CNT_POS)

#define AFE_CLD_CL_MISC_SET             (AFE_BASE + 0x190C)
#define AFE_CLD_CL_MISC_SET_OL_CLD_LR_SWAP_POS      (0)
#define AFE_CLD_CL_MISC_SET_OL_CLD_LR_SWAP_MASK     (1<<AFE_CLD_CL_MISC_SET_OL_CLD_LR_SWAP_POS)
#define AFE_CLD_CL_DA_812P5K_SET        (AFE_BASE + 0x1910)
#define AFE_CLD_CL_DA_812P5K_SET_AD_812P5K_POS_NEG_INV_POS  (0)
#define AFE_CLD_CL_DA_812P5K_SET_AD_812P5K_POS_NEG_INV_MASK (1<<AFE_CLD_CL_DA_812P5K_SET_AD_812P5K_POS_NEG_INV_POS)
#define AFE_CLD_DA_GEN                  (AFE_BASE + 0x191C)
#define AFE_CLD_DA_GEN_RG_ENABLE_POS                (0)
#define AFE_CLD_DA_GEN_RG_ENABLE_MASK               (1<<AFE_CLD_DA_GEN_RG_ENABLE_POS)

#define AFE_CLD_DA_GEN_SEL              (AFE_BASE + 0x1920)
#define AFE_CLD_DA_GEN_SEL_EN_H_HOLD_SW_MODE_POS        (0)
#define AFE_CLD_DA_GEN_SEL_EN_H_HOLD_SW_MODE_MASK       (1<<AFE_CLD_DA_GEN_SEL_EN_H_HOLD_SW_MODE_POS)
#define AFE_CLD_DA_GEN_SEL_EN_H_HOLD_SW_POS             (1)
#define AFE_CLD_DA_GEN_SEL_EN_H_HOLD_SW_MASK            (1<<AFE_CLD_DA_GEN_SEL_EN_H_HOLD_SW_POS)

#define AFE_CLD_DA_GEN_SEL_SET_ZCD_OL_6P5M_DLY_POS      (16)
#define AFE_CLD_DA_GEN_SEL_SET_ZCD_OL_6P5M_DLY_MAKS     (31<<AFE_CLD_DA_GEN_SEL_SET_ZCD_OL_6P5M_DLY_POS)
#define AFE_CLD_DA_GEN_SEL_SET_ZCD_OL_26M_DLY_POS       (21)
#define AFE_CLD_DA_GEN_SEL_SET_ZCD_OL_26M_DLY_MASK      (3<<AFE_CLD_DA_GEN_SEL_SET_ZCD_OL_26M_DLY_POS)
#define AFE_CLD_DA_GEN_SEL_SET_ZCD_CL_6P5M_DLY_POS      (24)
#define AFE_CLD_DA_GEN_SEL_SET_ZCD_CL_6P5M_DLY_MAKS     (31<<AFE_CLD_DA_GEN_SEL_SET_ZCD_CL_6P5M_DLY_POS)
#define AFE_CLD_DA_GEN_SEL_SET_ZCD_CL_26M_DLY_POS       (29)
#define AFE_CLD_DA_GEN_SEL_SET_ZCD_CL_26M_DLY_MASK      (3<<AFE_CLD_DA_GEN_SEL_SET_ZCD_CL_26M_DLY_POS)

#define AFE_CFG_EFUSE_CLASSD0           (AFE_BASE + 0x19A0)
#define AFE_CFG_EFUSE_CLASSD0_AUDOLD_GAIN_COMP_LCH_EN_POS   (0)
#define AFE_CFG_EFUSE_CLASSD0_AUDOLD_GAIN_COMP_LCH_EN_MASK  (1<<AFE_CFG_EFUSE_CLASSD0_AUDOLD_GAIN_COMP_LCH_EN_POS)
#define AFE_CFG_EFUSE_CLASSD0_AUDOLD_DC_COMP_LCH_EN_POS     (1)
#define AFE_CFG_EFUSE_CLASSD0_AUDOLD_DC_COMP_LCH_EN_MASK    (1<<AFE_CFG_EFUSE_CLASSD0_AUDOLD_DC_COMP_LCH_EN_POS)
#define AFE_CFG_EFUSE_CLASSD0_AUDOLD_GAIN_COMP_LCH_POS      (16)
#define AFE_CFG_EFUSE_CLASSD0_AUDOLD_GAIN_COMP_LCH_MASK     (255<<AFE_CFG_EFUSE_CLASSD0_AUDOLD_GAIN_COMP_LCH_POS)
#define AFE_CFG_EFUSE_CLASSD0_AUDOLD_DC_COMP_LCH_POS        (24)
#define AFE_CFG_EFUSE_CLASSD0_AUDOLD_DC_COMP_LCH_MASK       (255<<AFE_CFG_EFUSE_CLASSD0_AUDOLD_DC_COMP_LCH_POS)
#define AFE_CFG_EFUSE_CLASSD1           (AFE_BASE + 0x19A4)
#define AFE_CFG_EFUSE_CLASSD1_AUDOLD_CLKDLY26M_COMP_LCH_POS     (12)
#define AFE_CFG_EFUSE_CLASSD1_AUDOLD_CLKDLY26M_COMP_LCH_MASK    (31<<AFE_CFG_EFUSE_CLASSD1_AUDOLD_CLKDLY26M_COMP_LCH_POS)
#define AFE_CFG_EFUSE_CLASSD1_AUDOLD_PNDLY26M_COMP_LCH_POS      (17)
#define AFE_CFG_EFUSE_CLASSD1_AUDOLD_PNDLY26M_COMP_LCH_MASK     (7<<AFE_CFG_EFUSE_CLASSD1_AUDOLD_PNDLY26M_COMP_LCH_POS)

#define AFE_GAIN_REMAP                    (AFE_BASE + 0x1924)
#define AFE_GAIN_REMAP_EN_REMAP_CLG_POS             (0)
#define AFE_GAIN_REMAP_EN_REMAP_CLG_MASK            (1<<AFE_GAIN_REMAP_EN_REMAP_CLG_POS)
#define AFE_GAIN_REMAP_EN_HP_CLG_POS                (1)
#define AFE_GAIN_REMAP_EN_HP_CLG_MASK               (1<<AFE_GAIN_REMAP_EN_HP_CLG_POS)
#define AFE_GAIN_REMAP_EN_REMAP_CLD_POS             (2)
#define AFE_GAIN_REMAP_EN_REMAP_CLD_MASK            (1<<AFE_GAIN_REMAP_EN_REMAP_CLD_POS)
#define AFE_GAIN_REMAP_EN_HP_CLD_POS                (3)
#define AFE_GAIN_REMAP_EN_HP_CLD_MASK               (1<<AFE_GAIN_REMAP_EN_HP_CLD_POS)
#define AFE_GAIN_REMAP_CLD_L_GAIN_CAP_POS           (4)
#define AFE_GAIN_REMAP_CLD_L_GAIN_CAP_MASK          (0x1F<<AFE_GAIN_REMAP_CLD_L_GAIN_CAP_POS)
#define AFE_GAIN_REMAP_CLD_R_GAIN_CAP_POS           (9)
#define AFE_GAIN_REMAP_CLD_R_GAIN_CAP_MASK          (0x1F<<AFE_GAIN_REMAP_CLD_R_GAIN_CAP_POS)
#define AFE_GAIN_REMAP_CLD_AUDZCDHPLGAIN_POS        (16)
#define AFE_GAIN_REMAP_CLD_AUDZCDHPLGAIN_MASK       (0x3F<<AFE_GAIN_REMAP_CLD_AUDZCDHPLGAIN_POS)
#define AFE_GAIN_REMAP_CLD_AUDZCDHPRGAIN_POS        (22)
#define AFE_GAIN_REMAP_CLD_AUDZCDHPRGAIN_MASK       (0x3F<<AFE_GAIN_REMAP_CLD_AUDZCDHPRGAIN_POS)




#define AFE_MAXLENGTH                   (AFE_BASE + 0x1590)
#define AFE_REG_UNDEFINED               (AFE_MAXLENGTH + 0x4)

/*****************************************************************************
 *           I2S DMA      R E G I S T E R      D E F I N I T I O N
 *****************************************************************************/
#define DMA_GLBSTA                      (I2S_DMA_BASE + 0x0000)
#define DMA_GLBSTA_RUN1_POS                         (0)
#define DMA_GLBSTA_RUN1_MASK                        (1<<DMA_GLBSTA_RUN1_POS)
#define DMA_GLBSTA_IT1_POS                          (1)
#define DMA_GLBSTA_IT1_MASK                         (1<<DMA_GLBSTA_IT1_POS)
#define DMA_GLBSTA_RUN2_POS                         (2)
#define DMA_GLBSTA_RUN2_MASK                        (1<<DMA_GLBSTA_RUN2_POS)
#define DMA_GLBSTA_IT2_POS                          (3)
#define DMA_GLBSTA_IT2_MASK                         (1<<DMA_GLBSTA_IT2_POS)
#define DMA_GLBSTA_RUN3_POS                         (4)
#define DMA_GLBSTA_RUN3_MASK                        (1<<DMA_GLBSTA_RUN3_POS)
#define DMA_GLBSTA_IT3_POS                          (5)
#define DMA_GLBSTA_IT3_MASK                         (1<<DMA_GLBSTA_IT3_POS)
#define DMA_GLBSTA_RUN4_POS                         (6)
#define DMA_GLBSTA_RUN4_MASK                        (1<<DMA_GLBSTA_RUN4_POS)
#define DMA_GLBSTA_IT4_POS                          (7)
#define DMA_GLBSTA_IT4_MASK                         (1<<DMA_GLBSTA_IT4_POS)
#define DMA_GLBSTA_RUN5_POS                         (8)
#define DMA_GLBSTA_RUN5_MASK                        (1<<DMA_GLBSTA_RUN5_POS)
#define DMA_GLBSTA_IT5_POS                          (9)
#define DMA_GLBSTA_IT5_MASK                         (1<<DMA_GLBSTA_IT5_POS)
#define DMA_GLBSTA_RUN6_POS                         (10)
#define DMA_GLBSTA_RUN6_MASK                        (1<<DMA_GLBSTA_RUN6_POS)
#define DMA_GLBSTA_IT6_POS                          (11)
#define DMA_GLBSTA_IT6_MASK                         (1<<DMA_GLBSTA_IT6_POS)
#define DMA_GLBSTA_RUN7_POS                         (12)
#define DMA_GLBSTA_RUN7_MASK                        (1<<DMA_GLBSTA_RUN7_POS)
#define DMA_GLBSTA_IT7_POS                          (13)
#define DMA_GLBSTA_IT7_MASK                         (1<<DMA_GLBSTA_IT7_POS)
#define DMA_GLBSTA_RUN8_POS                         (14)
#define DMA_GLBSTA_RUN8_MASK                        (1<<DMA_GLBSTA_RUN8_POS)
#define DMA_GLBSTA_IT8_POS                          (15)
#define DMA_GLBSTA_IT8_MASK                         (1<<DMA_GLBSTA_IT8_POS)
#define DMA_GLBSTA_RUN9_POS                         (16)
#define DMA_GLBSTA_RUN9_MASK                        (1<<DMA_GLBSTA_RUN9_POS)
#define DMA_GLBSTA_IT9_POS                          (17)
#define DMA_GLBSTA_IT9_MASK                         (1<<DMA_GLBSTA_IT9_POS)
#define DMA_GLBSTA_RUN10_POS                        (18)
#define DMA_GLBSTA_RUN10_MASK                       (1<<DMA_GLBSTA_RUN10_POS)
#define DMA_GLBSTA_IT10_POS                         (19)
#define DMA_GLBSTA_IT10_MASK                        (1<<DMA_GLBSTA_IT10_POS)
#define DMA_GLBSTA_RUN11_POS                        (20)
#define DMA_GLBSTA_RUN11_MASK                       (1<<DMA_GLBSTA_RUN11_POS)
#define DMA_GLBSTA_IT11_POS                         (21)
#define DMA_GLBSTA_IT11_MASK                        (1<<DMA_GLBSTA_IT11_POS)
#define DMA_GLBSTA_RUN12_POS                        (22)
#define DMA_GLBSTA_RUN12_MASK                       (1<<DMA_GLBSTA_RUN12_POS)
#define DMA_GLBSTA_IT12_POS                         (23)
#define DMA_GLBSTA_IT12_MASK                        (1<<DMA_GLBSTA_IT12_POS)
#define DMA_GLBSTA_RUN13_POS                        (24)
#define DMA_GLBSTA_RUN13_MASK                       (1<<DMA_GLBSTA_RUN13_POS)
#define DMA_GLBSTA_IT13_POS                         (25)
#define DMA_GLBSTA_IT13_MASK                        (1<<DMA_GLBSTA_IT13_POS)
#define DMA_GLBSTA_RUN14_POS                        (26)
#define DMA_GLBSTA_RUN14_MASK                       (1<<DMA_GLBSTA_RUN14_POS)
#define DMA_GLBSTA_IT14_POS                         (27)
#define DMA_GLBSTA_IT14_MASK                        (1<<DMA_GLBSTA_IT14_POS)
#define DMA_GLBSTA_RUN15_POS                        (28)
#define DMA_GLBSTA_RUN15_MASK                       (1<<DMA_GLBSTA_RUN15_POS)
#define DMA_GLBSTA_IT15_POS                         (29)
#define DMA_GLBSTA_IT15_MASK                        (1<<DMA_GLBSTA_IT15_POS)
#define DMA_GLBSTA_RUN16_POS                        (30)
#define DMA_GLBSTA_RUN16_MASK                       (1<<DMA_GLBSTA_RUN16_POS)
#define DMA_GLBSTA_IT16_POS                         (31)
#define DMA_GLBSTA_IT16_MASK                        (1<<DMA_GLBSTA_IT16_POS)


/*****************************************************************************
 *          A N A L O G      R E G I S T E R      D E F I N I T I O N
 *****************************************************************************/
//#define HAL_AUDIO_LEGACY_ABB_DRIVER
#if 0
#define AUDENC_ANA_CON0                 (ABB_BASE + 0x0100)
#define AUDENC_ANA_CON0_L_AMP_ENABLE_POS            (0)
#define AUDENC_ANA_CON0_L_AMP_ENABLE_MASK           (1<<AUDENC_ANA_CON0_L_AMP_ENABLE_POS)
#define AUDENC_ANA_CON0_L_DC_COUPLE_POS             (1)
#define AUDENC_ANA_CON0_L_DC_COUPLE_MASK            (1<<AUDENC_ANA_CON0_L_DC_COUPLE_POS)
#define AUDENC_ANA_CON0_L_AMP_PRECHARGE_POS         (2)
#define AUDENC_ANA_CON0_L_AMP_PRECHARGE_MASK        (1<<AUDENC_ANA_CON0_L_AMP_PRECHARGE_POS)
#define AUDENC_ANA_CON0_L_PREAMPLIFTER_SEL_POS      (6)
#define AUDENC_ANA_CON0_L_PREAMPLIFTER_SEL_MASK     (3<<AUDENC_ANA_CON0_L_PREAMPLIFTER_SEL_POS)
#define AUDENC_ANA_CON0_L_AMP_GAIN_POS              (8)
#define AUDENC_ANA_CON0_L_AMP_GAIN_MASK             (3<<AUDENC_ANA_CON0_L_AMP_GAIN_POS)
#define AUDENC_ANA_CON0_L_ADC_ON_POS                (12)
#define AUDENC_ANA_CON0_L_ADC_ON_MASK               (1<<AUDENC_ANA_CON0_L_ADC_ON_POS)
#define AUDENC_ANA_CON0_L_ADC_SEL_POS               (13)
#define AUDENC_ANA_CON0_L_ADC_SEL_MASK              (3<<AUDENC_ANA_CON0_L_ADC_SEL_POS)
#define AUDENC_ANA_CON1                 (ABB_BASE + 0x0104)
#define AUDENC_ANA_CON1_R_AMP_ENABLE_POS            (0)
#define AUDENC_ANA_CON1_R_AMP_ENABLE_MASK           (1<<AUDENC_ANA_CON1_R_AMP_ENABLE_POS)
#define AUDENC_ANA_CON1_R_DC_COUPLE_POS             (1)
#define AUDENC_ANA_CON1_R_DC_COUPLE_MASK            (1<<AUDENC_ANA_CON1_R_DC_COUPLE_POS)
#define AUDENC_ANA_CON1_R_AMP_PRECHARGE_POS         (2)
#define AUDENC_ANA_CON1_R_AMP_PRECHARGE_MASK        (1<<AUDENC_ANA_CON1_R_AMP_PRECHARGE_POS)
#define AUDENC_ANA_CON1_R_PREAMPLIFTER_SEL_POS      (6)
#define AUDENC_ANA_CON1_R_PREAMPLIFTER_SEL_MASK     (3<<AUDENC_ANA_CON1_R_PREAMPLIFTER_SEL_POS)
#define AUDENC_ANA_CON1_R_AMP_GAIN_POS              (8)
#define AUDENC_ANA_CON1_R_AMP_GAIN_MASK             (3<<AUDENC_ANA_CON1_R_AMP_GAIN_POS)
#define AUDENC_ANA_CON1_R_ADC_ON_POS                (12)
#define AUDENC_ANA_CON1_R_ADC_ON_MASK               (1<<AUDENC_ANA_CON1_R_ADC_ON_POS)
#define AUDENC_ANA_CON1_R_ADC_SEL_POS               (13)
#define AUDENC_ANA_CON1_R_ADC_SEL_MASK              (3<<AUDENC_ANA_CON1_R_ADC_SEL_POS)
#define AUDENC_ANA_CON2                 (ABB_BASE + 0x0108)
#define AUDDEC_ANA_CON2_HALFBIAS_EN_POS             (0)
#define AUDDEC_ANA_CON2_HALFBIAS_EN_MASK            (1<<AUDDEC_ANA_CON2_HALFBIAS_EN_POS)
#define AUDDEC_ANA_CON2_PGA_LOWPOWER_MODE1_POS      (1)
#define AUDDEC_ANA_CON2_PGA_LOWPOWER_MODE1_MASK     (1<<AUDDEC_ANA_CON2_PGA_LOWPOWER_MODE1_POS)
#define AUDENC_ANA_CON3                 (ABB_BASE + 0x010C)
#define AUDDEC_ANA_CON3_CLOCK_SEL_POS               (0)
#define AUDDEC_ANA_CON3_CLOCK_SEL_MASK              (3<<AUDDEC_ANA_CON3_CLOCK_SEL_POS)
#define AUDDEC_ANA_CON3_CLOCK_SOURCE_POS            (2)
#define AUDDEC_ANA_CON3_CLOCK_SOURCE_MASK           (3<<AUDDEC_ANA_CON3_CLOCK_SOURCE_POS)
#define AUDDEC_ANA_CON3_ENABLE_CLOCK_UL_POS         (4)
#define AUDDEC_ANA_CON3_ENABLE_CLOCK_UL_MASK        (1<<AUDDEC_ANA_CON3_ENABLE_CLOCK_UL_POS)
#define AUDENC_ANA_CON4                 (ABB_BASE + 0x0110)
#define AUDENC_ANA_CON5                 (ABB_BASE + 0x0114)
#define AUDENC_ANA_CON6                 (ABB_BASE + 0x0118)
#define AUDENC_ANA_CON7                 (ABB_BASE + 0x011C)
#define AUDENC_ANA_CON7_LOW_POWER_ENABLE_POS        (0)
#define AUDENC_ANA_CON7_LOW_POWER_ENABLE_MASK       (1<<AUDENC_ANA_CON7_LOW_POWER_ENABLE_POS)
#define AUDENC_ANA_CON7_LOW_POWER_RESET_POS         (1)
#define AUDENC_ANA_CON7_LOW_POWER_RESET_MASK        (1<<AUDENC_ANA_CON7_LOW_POWER_RESET_POS)
#define AUDENC_ANA_CON8                 (ABB_BASE + 0x0120)
#define AUDENC_ANA_CON9                 (ABB_BASE + 0x0124)
#define AUDENC_ANA_CON9_LCLDO_ENC_POS               (0)
#define AUDENC_ANA_CON9_LCLDO_ENC_MASK              (1<<AUDENC_ANA_CON9_LCLDO_ENC_POS)
#define AUDENC_ANA_CON9_LCLDO_DISCHARGE_POS         (1)
#define AUDENC_ANA_CON9_LCLDO_DISCHARGE_MASK        (1<<AUDENC_ANA_CON9_LCLDO_DISCHARGE_POS)
#define AUDENC_ANA_CON9_LCLDO_BOOST_POS             (2)
#define AUDENC_ANA_CON9_LCLDO_BOOST_MASK            (1<<AUDENC_ANA_CON9_LCLDO_BOOST_POS)
#define AUDENC_ANA_CON9_LCLDO_REMOTE_SENSE_POS      (3)
#define AUDENC_ANA_CON9_LCLDO_REMOTE_SENSE_MASK     (1<<AUDENC_ANA_CON9_LCLDO_REMOTE_SENSE_POS)
#define AUDENC_ANA_CON9_LCLDO_DISABLE_TEST_POS      (4)
#define AUDENC_ANA_CON9_LCLDO_DISABLE_TEST_MASK     (1<<AUDENC_ANA_CON9_LCLDO_DISABLE_TEST_POS)
#define AUDENC_ANA_CON10                (ABB_BASE + 0x0128)
#define AUDENC_ANA_CON10_GPIO_DMIC0_ENABLE_POS      (0)
#define AUDENC_ANA_CON10_GPIO_DMIC0_ENABLE_MASK     (1<<AUDENC_ANA_CON10_GPIO_DMIC0_ENABLE_POS)
#define AUDENC_ANA_CON11                (ABB_BASE + 0x012C)
#define AUDENC_ANA_CON11_GPIO_DMIC1_ENABLE_POS      (0)
#define AUDENC_ANA_CON11_GPIO_DMIC1_ENABLE_MASK     (1<<AUDENC_ANA_CON11_GPIO_DMIC1_ENABLE_POS)
#define AUDENC_ANA_CON12                (ABB_BASE + 0x0130)
#define AUDENC_ANA_CON12_POWER_ON_POS               (0)
#define AUDENC_ANA_CON12_POWER_ON_MASK              (1<<AUDENC_ANA_CON12_POWER_ON_POS)
#define AUDENC_ANA_CON12_BYPASS_POS                 (1)
#define AUDENC_ANA_CON12_BYPASS_MASK                (1<<AUDENC_ANA_CON12_BYPASS_POS)
#define AUDENC_ANA_CON12_LOWPOWER_POS               (2)
#define AUDENC_ANA_CON12_LOWPOWER_MASK              (1<<AUDENC_ANA_CON12_LOWPOWER_POS)
#define AUDENC_ANA_CON12_POWER_DOWN3_POS            (3)
#define AUDENC_ANA_CON12_POWER_DOWN3_MASK           (1<<AUDENC_ANA_CON12_POWER_DOWN3_POS)
#define AUDENC_ANA_CON12_VOLTAGE_POS                (4)
#define AUDENC_ANA_CON12_VOLTAGE_MASK               (7<<AUDENC_ANA_CON12_VOLTAGE_POS)
#define AUDENC_ANA_CON12_BIAS_CONNECT_POS           (7)
#define AUDENC_ANA_CON12_BIAS_CONNECT_MASK          (1<<AUDENC_ANA_CON12_BIAS_CONNECT_POS)
#define AUDENC_ANA_CON12_COUPLE_P1_POS              (8)
#define AUDENC_ANA_CON12_COUPLE_P1_MASK             (1<<AUDENC_ANA_CON12_COUPLE_P1_POS)
#define AUDENC_ANA_CON12_COUPLE_P2_POS              (9)
#define AUDENC_ANA_CON12_COUPLE_P2_MASK             (1<<AUDENC_ANA_CON12_COUPLE_P2_POS)
#define AUDENC_ANA_CON12_COUPLE_ON_POS              (10)
#define AUDENC_ANA_CON12_COUPLE_ON_MASK             (1<<AUDENC_ANA_CON12_COUPLE_ON_POS)
#define AUDENC_ANA_CON12_PULL_LOW_POS               (12)
#define AUDENC_ANA_CON12_PULL_LOW_MASK              (1<<AUDENC_ANA_CON12_PULL_LOW_POS)
#define AUDENC_ANA_CON13                (ABB_BASE + 0x0134)
#define AUDENC_ANA_CON14                (ABB_BASE + 0x0138)

#define AUDDEC_ANA_CON0                 (ABB_BASE + 0x0200)
#define AUDDEC_ANA_CON0_LCH_DAC_EN_POS              (0)
#define AUDDEC_ANA_CON0_LCH_DAC_EN_MASK             (1<<AUDDEC_ANA_CON0_LCH_DAC_EN_POS)
#define AUDDEC_ANA_CON0_RCH_DAC_EN_POS              (1)
#define AUDDEC_ANA_CON0_RCH_DAC_EN_MASK             (1<<AUDDEC_ANA_CON0_RCH_DAC_EN_POS)
#define AUDDEC_ANA_CON0_LCH_BIAS_EN_POS             (2)
#define AUDDEC_ANA_CON0_LCH_BIAS_EN_MASK            (1<<AUDDEC_ANA_CON0_LCH_BIAS_EN_POS)
#define AUDDEC_ANA_CON0_RCH_BIAS_EN_POS             (3)
#define AUDDEC_ANA_CON0_RCH_BIAS_EN_MASK            (1<<AUDDEC_ANA_CON0_RCH_BIAS_EN_POS)
#define AUDDEC_ANA_CON0_CHOP_EN_POS                 (4)
#define AUDDEC_ANA_CON0_CHOP_EN_MASK                (1<<AUDDEC_ANA_CON0_CHOP_EN_POS)
#define AUDDEC_ANA_CON0_IBIAS_SEL_POS               (5)
#define AUDDEC_ANA_CON0_IBIAS_SEL_MASK              (1<<AUDDEC_ANA_CON0_IBIAS_SEL_POS)
#define AUDDEC_ANA_CON0_IBIAS_EN_POS                (6)
#define AUDDEC_ANA_CON0_IBIAS_EN_MASK               (1<<AUDDEC_ANA_CON0_IBIAS_EN_POS)
#define AUDDEC_ANA_CON0_LPM_EN_POS                  (8)
#define AUDDEC_ANA_CON0_LPM_EN_MASK                 (1<<AUDDEC_ANA_CON0_LPM_EN_POS)
#define AUDDEC_ANA_CON0_OP_BOOST_EN_POS             (9)
#define AUDDEC_ANA_CON0_OP_BOOST_EN_MASK            (1<<AUDDEC_ANA_CON0_OP_BOOST_EN_POS)
#define AUDDEC_ANA_CON0_FILTER_EN_POS               (12)
#define AUDDEC_ANA_CON0_FILTER_EN_MASK              (1<<AUDDEC_ANA_CON0_FILTER_EN_POS)
#define AUDDEC_ANA_CON0_FILTER_LOW_LEAK_POS         (13)
#define AUDDEC_ANA_CON0_FILTER_LOW_LEAK_MASK        (1<<AUDDEC_ANA_CON0_FILTER_LOW_LEAK_POS)
#define AUDDEC_ANA_CON0_FILTER_TRIG_POS             (14)
#define AUDDEC_ANA_CON0_FILTER_TRIG_MASK            (1<<AUDDEC_ANA_CON0_FILTER_TRIG_POS)
#define AUDDEC_ANA_CON1                 (ABB_BASE + 0x0204)
#define AUDDEC_ANA_CON1_HPL_MAIN_PWRUP_POS          (0)
#define AUDDEC_ANA_CON1_HPL_MAIN_PWRUP_MASK         (1<<AUDDEC_ANA_CON1_HPL_MAIN_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPR_MAIN_PWRUP_POS          (1)
#define AUDDEC_ANA_CON1_HPR_MAIN_PWRUP_MASK         (1<<AUDDEC_ANA_CON1_HPR_MAIN_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPL_AUX_PWRUP_POS           (2)
#define AUDDEC_ANA_CON1_HPL_AUX_PWRUP_MASK          (1<<AUDDEC_ANA_CON1_HPL_AUX_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPR_AUX_PWRUP_POS           (3)
#define AUDDEC_ANA_CON1_HPR_AUX_PWRUP_MASK          (1<<AUDDEC_ANA_CON1_HPR_AUX_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPL_PWRUP_POS               (4)
#define AUDDEC_ANA_CON1_HPL_PWRUP_MASK              (1<<AUDDEC_ANA_CON1_HPL_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPR_PWRUP_POS               (5)
#define AUDDEC_ANA_CON1_HPR_PWRUP_MASK              (1<<AUDDEC_ANA_CON1_HPR_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPL_BIAS_PWRUP_POS          (6)
#define AUDDEC_ANA_CON1_HPL_BIAS_PWRUP_MASK         (1<<AUDDEC_ANA_CON1_HPL_BIAS_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPR_BIAS_PWRUP_POS          (7)
#define AUDDEC_ANA_CON1_HPR_BIAS_PWRUP_MASK         (1<<AUDDEC_ANA_CON1_HPR_BIAS_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPL_IN_SEL_POS              (8)
#define AUDDEC_ANA_CON1_HPL_IN_SEL_MASK             (1<<AUDDEC_ANA_CON1_HPL_IN_SEL_POS)
#define AUDDEC_ANA_CON1_HPR_IN_SEL_POS              (10)
#define AUDDEC_ANA_CON1_HPR_IN_SEL_MASK             (1<<AUDDEC_ANA_CON1_HPR_IN_SEL_POS)
#define AUDDEC_ANA_CON1_LSCP_DISABLE_POS            (12)
#define AUDDEC_ANA_CON1_LSCP_DISABLE_MASK           (1<<AUDDEC_ANA_CON1_LSCP_DISABLE_POS)
#define AUDDEC_ANA_CON1_RSCP_DISABLE_POS            (13)
#define AUDDEC_ANA_CON1_RSCP_DISABLE_MASK           (1<<AUDDEC_ANA_CON1_RSCP_DISABLE_POS)
#define AUDDEC_ANA_CON2                 (ABB_BASE + 0x0208)
#define AUDDEC_ANA_CON2_HPL_STB_RST_POS             (0)
#define AUDDEC_ANA_CON2_HPL_STB_RST_MASK            (1<<AUDDEC_ANA_CON2_HPL_STB_RST_POS)
#define AUDDEC_ANA_CON2_HPL_VCM_BUF_POS             (1)
#define AUDDEC_ANA_CON2_HPL_VCM_BUF_MASK            (1<<AUDDEC_ANA_CON2_HPL_VCM_BUF_POS)
#define AUDDEC_ANA_CON2_HPL_STB_EN_POS              (2)
#define AUDDEC_ANA_CON2_HPL_STB_EN_MASK             (1<<AUDDEC_ANA_CON2_HPL_STB_EN_POS)
#define AUDDEC_ANA_CON2_HPR_STB_RST_POS             (3)
#define AUDDEC_ANA_CON2_HPR_STB_RST_MASK            (1<<AUDDEC_ANA_CON2_HPR_STB_RST_POS)
#define AUDDEC_ANA_CON2_HPR_VCM_BUF_POS             (4)
#define AUDDEC_ANA_CON2_HPR_VCM_BUF_MASK            (1<<AUDDEC_ANA_CON2_HPR_VCM_BUF_POS)
#define AUDDEC_ANA_CON2_HPR_STB_EN_POS              (5)
#define AUDDEC_ANA_CON2_HPR_STB_EN_MASK             (1<<AUDDEC_ANA_CON2_HPR_STB_EN_POS)
#define AUDDEC_ANA_CON2_STARTUP_MODE_POS            (6)
#define AUDDEC_ANA_CON2_STARTUP_MODE_MASK           (1<<AUDDEC_ANA_CON2_STARTUP_MODE_POS)
#define AUDDEC_ANA_CON2_HPL_FB_EN_POS               (7)
#define AUDDEC_ANA_CON2_HPL_FB_EN_MASK              (1<<AUDDEC_ANA_CON2_HPL_FB_EN_POS)
#define AUDDEC_ANA_CON2_HPR_FB_EN_POS               (8)
#define AUDDEC_ANA_CON2_HPR_FB_EN_MASK              (1<<AUDDEC_ANA_CON2_HPR_FB_EN_POS)
#define AUDDEC_ANA_CON2_HPL_SHORT_EN_POS            (10)
#define AUDDEC_ANA_CON2_HPL_SHORT_EN_MASK           (1<<AUDDEC_ANA_CON2_HPL_SHORT_EN_POS)
#define AUDDEC_ANA_CON2_HPR_SHORT_EN_POS            (11)
#define AUDDEC_ANA_CON2_HPR_SHORT_EN_MASK           (1<<AUDDEC_ANA_CON2_HPR_SHORT_EN_POS)
#define AUDDEC_ANA_CON2_HPL_AUXFB_EN_POS            (12)
#define AUDDEC_ANA_CON2_HPL_AUXFB_EN_MASK           (1<<AUDDEC_ANA_CON2_HPL_AUXFB_EN_POS)
#define AUDDEC_ANA_CON2_HPR_AUXFB_EN_POS            (13)
#define AUDDEC_ANA_CON2_HPR_AUXFB_EN_MASK           (1<<AUDDEC_ANA_CON2_HPR_AUXFB_EN_POS)
#define AUDDEC_ANA_CON2_HPL_MAINFB_EN_POS           (14)
#define AUDDEC_ANA_CON2_HPL_MAINFB_EN_MASK          (1<<AUDDEC_ANA_CON2_HPL_MAINFB_EN_POS)
#define AUDDEC_ANA_CON2_HPR_MAINFB_EN_POS           (15)
#define AUDDEC_ANA_CON2_HPR_MAINFB_EN_MASK          (1<<AUDDEC_ANA_CON2_HPR_MAINFB_EN_POS)
#define AUDDEC_ANA_CON3                 (ABB_BASE + 0x020C)
#define AUDDEC_ANA_CON3_HPL_GAIN_POS                (0)
#define AUDDEC_ANA_CON3_HPL_GAIN_MASK               (0xF<<AUDDEC_ANA_CON3_HPL_GAIN_POS)
#define AUDDEC_ANA_CON3_HPR_GAIN_POS                (4)
#define AUDDEC_ANA_CON3_HPR_GAIN_MASK               (0xF<<AUDDEC_ANA_CON3_HPR_GAIN_POS)
#define AUDDEC_ANA_CON3_REFN_EN_POS                 (8)
#define AUDDEC_ANA_CON3_REFN_EN_MASK                (1<<AUDDEC_ANA_CON3_REFN_EN_POS)
#define AUDDEC_ANA_CON3_OUT_STAGE_POS               (9)
#define AUDDEC_ANA_CON3_OUT_STAGE_MASK              (1<<AUDDEC_ANA_CON3_OUT_STAGE_POS)
#define AUDDEC_ANA_CON3_HP_TRIM_EN_POS              (15)
#define AUDDEC_ANA_CON3_HP_TRIM_EN_MASK             (1<<AUDDEC_ANA_CON3_HP_TRIM_EN_POS)

#define AUDDEC_ANA_CON4                 (ABB_BASE + 0x0210)
#define AUDDEC_ANA_CON4_HPL_TRIM_VOL_POS            (0)
#define AUDDEC_ANA_CON4_HPL_TRIM_VOL_MASK           (0x1F<<AUDDEC_ANA_CON4_HPL_TRIM_VOL_POS)
#define AUDDEC_ANA_CON4_HPL_FINE_TRIM_VOL_POS       (5)
#define AUDDEC_ANA_CON4_HPL_FINE_TRIM_VOL_MASK      (7<<AUDDEC_ANA_CON4_HPL_FINE_TRIM_VOL_POS)
#define AUDDEC_ANA_CON4_HPR_TRIM_VOL_POS            (8)
#define AUDDEC_ANA_CON4_HPR_TRIM_VOL_MASK           (0x1F<<AUDDEC_ANA_CON4_HPR_TRIM_VOL_POS)
#define AUDDEC_ANA_CON4_HPR_FINE_TRIM_VOL_POS       (13)
#define AUDDEC_ANA_CON4_HPR_FINE_TRIM_VOL_MASK      (7<<AUDDEC_ANA_CON4_HPR_FINE_TRIM_VOL_POS)
#define AUDDEC_ANA_CON5                 (ABB_BASE + 0x0214)
#define AUDDEC_ANA_CON5_HP_DIF_CURRENT_SEL_POS      (4)
#define AUDDEC_ANA_CON5_HP_DIF_CURRENT_SEL_MASK     (7<<AUDDEC_ANA_CON5_HP_DIF_CURRENT_SEL_POS)


#define AUDDEC_ANA_CON6                 (ABB_BASE + 0x0218)
#define AUDDEC_ANA_CON7                 (ABB_BASE + 0x021C)
#define AUDDEC_ANA_CON7_BIAS_ADJ_0_POS              (0)
#define AUDDEC_ANA_CON7_BIAS_ADJ_0_MASK             (0xFFF<<AUDDEC_ANA_CON7_BIAS_ADJ_0_POS)
#define AUDDEC_ANA_CON8                 (ABB_BASE + 0x0220)
#define AUDDEC_ANA_CON8_BIAS_ADJ_1_POS              (0)
#define AUDDEC_ANA_CON8_BIAS_ADJ_1_MASK             (0x3F<<AUDDEC_ANA_CON8_BIAS_ADJ_1_POS)
#define AUDDEC_ANA_CON8_IBIAS_PDN_POS               (15)
#define AUDDEC_ANA_CON8_IBIAS_PDN_MASK              (1<<AUDDEC_ANA_CON8_IBIAS_PDN_POS)
#define AUDDEC_ANA_CON9                 (ABB_BASE + 0x0224)
#define AUDDEC_ANA_CON9_CLKSQ_ENABLE_POS            (0)
#define AUDDEC_ANA_CON9_CLKSQ_ENABLE_MASK           (1<<AUDDEC_ANA_CON9_CLKSQ_ENABLE_POS)
#define AUDDEC_ANA_CON9_26M_CLKIN_SEL_POS           (2)
#define AUDDEC_ANA_CON9_26M_CLKIN_SEL_MASK          (1<<AUDDEC_ANA_CON9_26M_CLKIN_SEL_POS)
#define AUDDEC_ANA_CON9_GLOBAL_BIAS_POS             (4)
#define AUDDEC_ANA_CON9_GLOBAL_BIAS_MASK            (1<<AUDDEC_ANA_CON9_GLOBAL_BIAS_POS)
#define AUDDEC_ANA_CON9_LDO_1_5V_VOLTAGE_SEL_POS    (5)
#define AUDDEC_ANA_CON9_LDO_1_5V_VOLTAGE_SEL_MASK   (3<<AUDDEC_ANA_CON9_LDO_1_5V_VOLTAGE_SEL_POS)
#define AUDDEC_ANA_CON9_LDO_CORE_EN_POS             (8)
#define AUDDEC_ANA_CON9_LDO_CORE_EN_MASK            (1<<AUDDEC_ANA_CON9_LDO_CORE_EN_POS)
#define AUDDEC_ANA_CON9_LDO_DEC_EN_POS              (9)
#define AUDDEC_ANA_CON9_LDO_DEC_EN_MASK             (1<<AUDDEC_ANA_CON9_LDO_DEC_EN_POS)
#define AUDDEC_ANA_CON9_LDO_DAC_EN_POS              (10)
#define AUDDEC_ANA_CON9_LDO_DAC_EN_MASK             (1<<AUDDEC_ANA_CON9_LDO_DAC_EN_POS)
#define AUDDEC_ANA_CON9_LDO_2_4V_VOLTAGE_SEL_POS    (12)
#define AUDDEC_ANA_CON9_LDO_2_4V_VOLTAGE_SEL_MASK   (7<<AUDDEC_ANA_CON9_LDO_2_4V_VOLTAGE_SEL_POS)
#define AUDDEC_ANA_CON10                (ABB_BASE + 0x0228)
#define AUDDEC_ANA_CON10_HPL_EN_POS                 (0)
#define AUDDEC_ANA_CON10_HPL_EN_MASK                (1<<AUDDEC_ANA_CON10_HPL_EN_POS)
#define AUDDEC_ANA_CON10_HPR_EN_POS                 (1)
#define AUDDEC_ANA_CON10_HPR_EN_MASK                (1<<AUDDEC_ANA_CON10_HPR_EN_POS)
#define AUDDEC_ANA_CON10_GLOBAL_BUF_EN_POS          (2)
#define AUDDEC_ANA_CON10_GLOBAL_BUF_EN_MASK         (1<<AUDDEC_ANA_CON10_GLOBAL_BUF_EN_POS)
#define AUDDEC_ANA_CON10_HP_DUMVCM_BUF_EN_POS       (3)
#define AUDDEC_ANA_CON10_HP_DUMVCM_BUF_EN_MASK      (1<<AUDDEC_ANA_CON10_HP_DUMVCM_BUF_EN_POS)
#define AUDDEC_ANA_CON10_HCLDO_EN_POS               (4)
#define AUDDEC_ANA_CON10_HCLDO_EN_MASK              (1<<AUDDEC_ANA_CON10_HCLDO_EN_POS)
#define AUDDEC_ANA_CON10_LCLDO_EN_POS               (5)
#define AUDDEC_ANA_CON10_LCLDO_EN_MASK              (1<<AUDDEC_ANA_CON10_LCLDO_EN_POS)
#define AUDDEC_ANA_CON10_DAC_EN_POS                 (6)
#define AUDDEC_ANA_CON10_DAC_EN_MASK                (1<<AUDDEC_ANA_CON10_DAC_EN_POS)
#define AUDDEC_ANA_CON10_HCLDO_PDDIS_POS            (7)
#define AUDDEC_ANA_CON10_HCLDO_PDDIS_MASK           (1<<AUDDEC_ANA_CON10_HCLDO_PDDIS_POS)
#define AUDDEC_ANA_CON10_HCLDO_REMOTE_SENSE_POS     (8)
#define AUDDEC_ANA_CON10_HCLDO_REMOTE_SENSE_MASK    (1<<AUDDEC_ANA_CON10_HCLDO_REMOTE_SENSE_POS)
#define AUDDEC_ANA_CON10_HCLDO_TBST_EN_POS          (9)
#define AUDDEC_ANA_CON10_HCLDO_TBST_EN_MASK         (1<<AUDDEC_ANA_CON10_HCLDO_TBST_EN_POS)
#define AUDDEC_ANA_CON10_LCLDO_PDDIS_POS            (10)
#define AUDDEC_ANA_CON10_LCLDO_PDDIS_MASK           (1<<AUDDEC_ANA_CON10_LCLDO_PDDIS_POS)
#define AUDDEC_ANA_CON10_LCLDO_TBST_EN_POS          (11)
#define AUDDEC_ANA_CON10_LCLDO_TBST_EN_MASK         (1<<AUDDEC_ANA_CON10_LCLDO_TBST_EN_POS)
#define AUDDEC_ANA_CON10_LCLDO_REMOTE_SENSE_POS     (12)
#define AUDDEC_ANA_CON10_LCLDO_REMOTE_SENSE_MASK    (1<<AUDDEC_ANA_CON10_LCLDO_REMOTE_SENSE_POS)
#define AUDDEC_ANA_CON10_DAC_PDDIS_POS              (13)
#define AUDDEC_ANA_CON10_DAC_PDDIS_MASK             (1<<AUDDEC_ANA_CON10_DAC_PDDIS_POS)
#define AUDDEC_ANA_CON10_DAC_TBST_EN_POS            (14)
#define AUDDEC_ANA_CON10_DAC_TBST_EN_MASK           (1<<AUDDEC_ANA_CON10_DAC_TBST_EN_POS)
#define AUDDEC_ANA_CON10_DAC_REMOTE_SENSE_POS       (15)
#define AUDDEC_ANA_CON10_DAC_REMOTE_SENSE_MASK      (1<<AUDDEC_ANA_CON10_DAC_REMOTE_SENSE_POS)
#define AUDDEC_ANA_CON11                (ABB_BASE + 0x022C)
#define AUDDEC_ANA_CON11_VCMT_OP_EN_POS             (0)
#define AUDDEC_ANA_CON11_VCMT_OP_EN_MASK            (1<<AUDDEC_ANA_CON11_VCMT_OP_EN_POS)
#define AUDDEC_ANA_CON11_VCMT_RDIV_EN_POS           (1)
#define AUDDEC_ANA_CON11_VCMT_RDIV_EN_MASK          (1<<AUDDEC_ANA_CON11_VCMT_RDIV_EN_POS)
#define AUDDEC_ANA_CON11_VCMT_PAD_PULL_LOW_POS      (2)
#define AUDDEC_ANA_CON11_VCMT_PAD_PULL_LOW_MASK     (1<<AUDDEC_ANA_CON11_VCMT_PAD_PULL_LOW_POS)
#define AUDDEC_ANA_CON11_VCMT_CURRENT_SEL_POS       (3)
#define AUDDEC_ANA_CON11_VCMT_CURRENT_SEL_MASK      (3<<AUDDEC_ANA_CON11_VCMT_CURRENT_SEL_POS)
#define AUDDEC_ANA_CON11_VCMT_CMPTHRS_SEL_POS       (5)
#define AUDDEC_ANA_CON11_VCMT_CMPTHRS_SEL_MASK      (3<<AUDDEC_ANA_CON11_VCMT_CMPTHRS_SEL_POS)
#define AUDDEC_ANA_CON11_VCMT_TRACK_EN_POS          (7)
#define AUDDEC_ANA_CON11_VCMT_TRACK_EN_MASK         (1<<AUDDEC_ANA_CON11_VCMT_TRACK_EN_POS)
#define AUDDEC_ANA_CON11_CLASSG_EN_POS              (8)
#define AUDDEC_ANA_CON11_CLASSG_EN_MASK             (1<<AUDDEC_ANA_CON11_CLASSG_EN_POS)



#define AUDDEC_ANA_CON12                (ABB_BASE + 0x0230)
#define AUDDEC_ANA_CON12_RSVD0_POS                  (0)
#define AUDDEC_ANA_CON12_RSVD0_MASK                 (0xFF<<AUDDEC_ANA_CON12_RSVD0_POS)
#define AUDDEC_ANA_CON12_RSVD1_POS                  (8)
#define AUDDEC_ANA_CON12_RSVD1_MASK                 (0xFF<<AUDDEC_ANA_CON12_RSVD1_POS)
#define AUDDEC_ANA_CON13                (ABB_BASE + 0x0234)
#define AUDDEC_ANA_CON14                (ABB_BASE + 0x0238)

#define PMU2_ANA_CON0                   (ABB_BASE + 0x0500)
#define PMU2_ANA_RO                     (ABB_BASE + 0x0510)
#else
#if 0 //2822 part
#define ABB_ANA_CON0                    (ABB_BASE + 0x0000)
#define ABB_ANA_CON0_SEL_ATST0_POS                  (0)
#define ABB_ANA_CON0_SEL_ATST0_MASK                 (0xFF<<ABB_ANA_CON0_SEL_ATST0_POS)
#define ABB_ANA_CON0_SEL_ATST1_POS                  (8)
#define ABB_ANA_CON0_SEL_ATST1_MASK                 (0xFF<<ABB_ANA_CON0_SEL_ATST1_POS)
#define AUDDEC_ANA_CON0                 (ABB_BASE + 0x0100)
#define AUDDEC_ANA_CON0_DAC_L_PWRUP_POS             (0)
#define AUDDEC_ANA_CON0_DAC_L_PWRUP_MASK            (1<<AUDDEC_ANA_CON0_DAC_L_PWRUP_POS)
#define AUDDEC_ANA_CON0_DAC_R_PWRUP_POS             (1)
#define AUDDEC_ANA_CON0_DAC_R_PWRUP_MASK            (1<<AUDDEC_ANA_CON0_DAC_R_PWRUP_POS)
#define AUDDEC_ANA_CON0_DAC_L_BIAS_EN_POS           (2)
#define AUDDEC_ANA_CON0_DAC_L_BIAS_EN_MASK          (1<<AUDDEC_ANA_CON0_DAC_L_BIAS_EN_POS)
#define AUDDEC_ANA_CON0_DAC_R_BIAS_EN_POS           (3)
#define AUDDEC_ANA_CON0_DAC_R_BIAS_EN_MASK          (1<<AUDDEC_ANA_CON0_DAC_R_BIAS_EN_POS)
#define AUDDEC_ANA_CON0_DAC_CHOP_EN_POS             (4)
#define AUDDEC_ANA_CON0_DAC_CHOP_EN_MASK            (1<<AUDDEC_ANA_CON0_DAC_CHOP_EN_POS)
#define AUDDEC_ANA_CON0_DAC_IBIAS_BST_SEL_POS       (5)
#define AUDDEC_ANA_CON0_DAC_IBIAS_BST_SEL_MASK      (1<<AUDDEC_ANA_CON0_DAC_IBIAS_BST_SEL_POS)
#define AUDDEC_ANA_CON0_DAC_IBIAS_BST_EN_POS        (6)
#define AUDDEC_ANA_CON0_DAC_IBIAS_BST_EN_MASK       (1<<AUDDEC_ANA_CON0_DAC_IBIAS_BST_EN_POS)
#define AUDDEC_ANA_CON0_DAC_LPM_EN_POS              (8)
#define AUDDEC_ANA_CON0_DAC_LPM_EN_MASK             (1<<AUDDEC_ANA_CON0_DAC_LPM_EN_POS)
#define AUDDEC_ANA_CON0_RG_AUDDAC_CLD_I_VA33_POS    (10)
#define AUDDEC_ANA_CON0_RG_AUDDAC_CLD_I_VA33_MASK   (0x3<<AUDDEC_ANA_CON0_RG_AUDDAC_CLD_I_VA33_POS)


#define AUDDEC_ANA_CON1                 (ABB_BASE + 0x0104)
#define AUDDEC_ANA_CON1_HPL_MAIN_STAGE_EN_POS       (0)
#define AUDDEC_ANA_CON1_HPL_MAIN_STAGE_EN_MASK      (1<<AUDDEC_ANA_CON1_HPL_MAIN_STAGE_EN_POS)
#define AUDDEC_ANA_CON1_HPR_MAIN_STAGE_EN_POS       (1)
#define AUDDEC_ANA_CON1_HPR_MAIN_STAGE_EN_MASK      (1<<AUDDEC_ANA_CON1_HPR_MAIN_STAGE_EN_POS)
#define AUDDEC_ANA_CON1_HPL_AUX_STAGE_EN_POS        (2)
#define AUDDEC_ANA_CON1_HPL_AUX_STAGE_EN_MASK       (1<<AUDDEC_ANA_CON1_HPL_AUX_STAGE_EN_POS)
#define AUDDEC_ANA_CON1_HPR_AUX_STAGE_EN_POS        (3)
#define AUDDEC_ANA_CON1_HPR_AUX_STAGE_EN_MASK       (1<<AUDDEC_ANA_CON1_HPR_AUX_STAGE_EN_POS)
#define AUDDEC_ANA_CON1_HPL_PWRUP_EN_POS            (4)
#define AUDDEC_ANA_CON1_HPL_PWRUP_EN_MASK           (1<<AUDDEC_ANA_CON1_HPL_PWRUP_EN_POS)
#define AUDDEC_ANA_CON1_HPR_PWRUP_EN_POS            (5)
#define AUDDEC_ANA_CON1_HPR_PWRUP_EN_MASK           (1<<AUDDEC_ANA_CON1_HPR_PWRUP_EN_POS)
#define AUDDEC_ANA_CON1_HPL_BIAS_EN_POS             (6)
#define AUDDEC_ANA_CON1_HPL_BIAS_EN_MASK            (1<<AUDDEC_ANA_CON1_HPL_BIAS_EN_POS)
#define AUDDEC_ANA_CON1_HPR_BIAS_EN_POS             (7)
#define AUDDEC_ANA_CON1_HPR_BIAS_EN_MASK            (1<<AUDDEC_ANA_CON1_HPR_BIAS_EN_POS)
#define AUDDEC_ANA_CON1_HPL_INPUT_SEL_POS           (8)
#define AUDDEC_ANA_CON1_HPL_INPUT_SEL_MASK          (3<<AUDDEC_ANA_CON1_HPL_INPUT_SEL_POS)
#define AUDDEC_ANA_CON1_HPR_INPUT_SEL_POS           (10)
#define AUDDEC_ANA_CON1_HPR_INPUT_SEL_MASK          (3<<AUDDEC_ANA_CON1_HPR_INPUT_SEL_POS)
#define AUDDEC_ANA_CON1_HPL_SCP_DISEN_POS           (12)
#define AUDDEC_ANA_CON1_HPL_SCP_DISEN_MASK          (1<<AUDDEC_ANA_CON1_HPL_SCP_DISEN_POS)
#define AUDDEC_ANA_CON1_HPR_SCP_DISEN_POS           (13)
#define AUDDEC_ANA_CON1_HPR_SCP_DISEN_MASK          (1<<AUDDEC_ANA_CON1_HPR_SCP_DISEN_POS)
#define AUDDEC_ANA_CON2                 (ABB_BASE + 0x0108)
#define AUDDEC_ANA_CON2_HPL_STB_RST_POS             (0)
#define AUDDEC_ANA_CON2_HPL_STB_RST_MASK            (1<<AUDDEC_ANA_CON2_HPL_STB_RST_POS)
#define AUDDEC_ANA_CON2_HPL_BUFSW_EN_POS            (1)
#define AUDDEC_ANA_CON2_HPL_BUFSW_EN_MASK           (1<<AUDDEC_ANA_CON2_HPL_BUFSW_EN_POS)
#define AUDDEC_ANA_CON2_HPL_STB_EN_POS              (2)
#define AUDDEC_ANA_CON2_HPL_STB_EN_MASK             (1<<AUDDEC_ANA_CON2_HPL_STB_EN_POS)
#define AUDDEC_ANA_CON2_HPR_STB_RST_POS             (3)
#define AUDDEC_ANA_CON2_HPR_STB_RST_MASK            (1<<AUDDEC_ANA_CON2_HPR_STB_RST_POS)
#define AUDDEC_ANA_CON2_HPR_BUFSW_EN_POS            (4)
#define AUDDEC_ANA_CON2_HPR_BUFSW_EN_MASK           (1<<AUDDEC_ANA_CON2_HPR_BUFSW_EN_POS)
#define AUDDEC_ANA_CON2_HPR_STB_EN_POS              (5)
#define AUDDEC_ANA_CON2_HPR_STB_EN_MASK             (1<<AUDDEC_ANA_CON2_HPR_STB_EN_POS)
#define AUDDEC_ANA_CON2_HP_STARTUP_POS              (6)
#define AUDDEC_ANA_CON2_HP_STARTUP_MASK             (1<<AUDDEC_ANA_CON2_HP_STARTUP_POS)
#define AUDDEC_ANA_CON2_HPL_AUX_FB_POS              (7)
#define AUDDEC_ANA_CON2_HPL_AUX_FB_MASK             (1<<AUDDEC_ANA_CON2_HPL_AUX_FB_POS)
#define AUDDEC_ANA_CON2_HPR_AUX_FB_POS              (8)
#define AUDDEC_ANA_CON2_HPR_AUX_FB_MASK             (1<<AUDDEC_ANA_CON2_HPR_AUX_FB_POS)
#define AUDDEC_ANA_CON2_HPL_SHORT_EN_POS            (10)
#define AUDDEC_ANA_CON2_HPL_SHORT_EN_MASK           (1<<AUDDEC_ANA_CON2_HPL_SHORT_EN_POS)
#define AUDDEC_ANA_CON2_HPR_SHORT_EN_POS            (11)
#define AUDDEC_ANA_CON2_HPR_SHORT_EN_MASK           (1<<AUDDEC_ANA_CON2_HPR_SHORT_EN_POS)
#define AUDDEC_ANA_CON2_HPL_AUX_CMFB_EN_POS         (12)
#define AUDDEC_ANA_CON2_HPL_AUX_CMFB_EN_MASK        (1<<AUDDEC_ANA_CON2_HPL_AUX_CMFB_EN_POS)
#define AUDDEC_ANA_CON2_HPR_AUX_CMFB_EN_POS         (13)
#define AUDDEC_ANA_CON2_HPR_AUX_CMFB_EN_MASK        (1<<AUDDEC_ANA_CON2_HPR_AUX_CMFB_EN_POS)
#define AUDDEC_ANA_CON2_HPL_MAIN_CMFB_EN_POS        (14)
#define AUDDEC_ANA_CON2_HPL_MAIN_CMFB_EN_MASK       (1<<AUDDEC_ANA_CON2_HPL_MAIN_CMFB_EN_POS)
#define AUDDEC_ANA_CON2_HPR_MAIN_CMFB_EN_POS        (15)
#define AUDDEC_ANA_CON2_HPR_MAIN_CMFB_EN_MASK       (1<<AUDDEC_ANA_CON2_HPR_MAIN_CMFB_EN_POS)

#define AUDDEC_ANA_CON3                 (ABB_BASE + 0x010C)
#define AUDDEC_ANA_CON3_HPL_AUX_GAIN_POS            (0)
#define AUDDEC_ANA_CON3_HPL_AUX_GAIN_MASK           (0xF<<AUDDEC_ANA_CON3_HPL_AUX_GAIN_POS)
#define AUDDEC_ANA_CON3_HPR_AUX_GAIN_POS            (4)
#define AUDDEC_ANA_CON3_HPR_AUX_GAIN_MASK           (0xF<<AUDDEC_ANA_CON3_HPR_AUX_GAIN_POS)
#define AUDDEC_ANA_CON3_HP_ESD_RES_EN_POS           (8)
#define AUDDEC_ANA_CON3_HP_ESD_RES_EN_MASK          (1<<AUDDEC_ANA_CON3_HP_ESD_RES_EN_POS)
#define AUDDEC_ANA_CON3_HP_OUT_SHORT_EN_POS         (9)
#define AUDDEC_ANA_CON3_HP_OUT_SHORT_EN_MASK        (1<<AUDDEC_ANA_CON3_HP_OUT_SHORT_EN_POS)
#define AUDDEC_ANA_CON3_HPL_RPWSEL_POS              (10)
#define AUDDEC_ANA_CON3_HPL_RPWSEL_MASK             (1<<AUDDEC_ANA_CON3_HPL_RPWSEL_POS)
#define AUDDEC_ANA_CON3_HPR_RPWSEL_POS              (11)
#define AUDDEC_ANA_CON3_HPR_RPWSEL_MASK             (1<<AUDDEC_ANA_CON3_HPR_RPWSEL_POS)
#define AUDDEC_ANA_CON3_HP_TRIM_EN_POS              (15)
#define AUDDEC_ANA_CON3_HP_TRIM_EN_MASK             (1<<AUDDEC_ANA_CON3_HP_TRIM_EN_POS)
#define AUDDEC_ANA_CON4                 (ABB_BASE + 0x0110)
#define AUDDEC_ANA_CON4_HPL_TRIM_VOL_SEL_POS        (0)
#define AUDDEC_ANA_CON4_HPL_TRIM_VOL_SEL_MASK       (0x1F<<AUDDEC_ANA_CON4_HPL_TRIM_VOL_SEL_POS)
#define AUDDEC_ANA_CON4_HPL_FINE_VOL_SEL_POS        (5)
#define AUDDEC_ANA_CON4_HPL_FINE_VOL_SEL_MASK       (7<<AUDDEC_ANA_CON4_HPL_FINE_VOL_SEL_POS)
#define AUDDEC_ANA_CON4_HPR_TRIM_VOL_SEL_POS        (8)
#define AUDDEC_ANA_CON4_HPR_TRIM_VOL_SEL_MASK       (0x1F<<AUDDEC_ANA_CON4_HPR_TRIM_VOL_SEL_POS)
#define AUDDEC_ANA_CON4_HPR_FINE_VOL_SEL_POS        (13)
#define AUDDEC_ANA_CON4_HPR_FINE_VOL_SEL_MASK       (7<<AUDDEC_ANA_CON4_HPR_FINE_VOL_SEL_POS)


#define AUDDEC_ANA_CON5                 (ABB_BASE + 0x0114)
#define AUDDEC_ANA_CON5_RG_AUDHPCM3_EN_VA33_POS     (3)
#define AUDDEC_ANA_CON5_RG_AUDHPCM3_EN_VA33_MASK    (1<<AUDDEC_ANA_CON5_RG_AUDHPCM3_EN_VA33_POS)
#define AUDDEC_ANA_CON5_HP_BIAS_SEL_POS             (4)
#define AUDDEC_ANA_CON5_HP_BIAS_SEL_MASK            (7<<AUDDEC_ANA_CON5_HP_BIAS_SEL_POS)
#define AUDDEC_ANA_CON5_HP_TRACK_EN_POS             (7)
#define AUDDEC_ANA_CON5_HP_TRACK_EN_MASK            (1<<AUDDEC_ANA_CON5_HP_TRACK_EN_POS)
#define AUDDEC_ANA_CON5_HP_TRACK_LEVEL_POS          (8)
#define AUDDEC_ANA_CON5_HP_TRACK_LEVEL_MASK         (7<<AUDDEC_ANA_CON5_HP_TRACK_LEVEL_POS)
#define AUDDEC_ANA_CON5_HP_1ST_CAP_SEL_POS          (11)
#define AUDDEC_ANA_CON5_HP_1ST_CAP_SEL_MASK         (3<<AUDDEC_ANA_CON5_HP_1ST_CAP_SEL_POS)

#define AUDDEC_ANA_CON6                 (ABB_BASE + 0x0118)
#define AUDDEC_ANA_CON6_TRIMBUF_GAIN_SEL_POS        (4)
#define AUDDEC_ANA_CON6_TRIMBUF_GAIN_SEL_MASK       (3<<AUDDEC_ANA_CON6_TRIMBUF_GAIN_SEL_POS)
#define AUDDEC_ANA_CON6_TRIMBUF_EN_POS              (6)
#define AUDDEC_ANA_CON6_TRIMBUF_EN_MASK             (1<<AUDDEC_ANA_CON6_TRIMBUF_EN_POS)
#define AUDDEC_ANA_CON7                 (ABB_BASE + 0x011C)
#define AUDDEC_ANA_CON7_BIAS_ADJ_POS                (0)
#define AUDDEC_ANA_CON7_BIAS_ADJ_MASK               (0xFFF<<AUDDEC_ANA_CON7_BIAS_ADJ_POS)
#define AUDDEC_ANA_CON8                 (ABB_BASE + 0x0120)
#define AUDDEC_ANA_CON8_BIAS_ADJ_POS                (0)
#define AUDDEC_ANA_CON8_BIAS_ADJ_MASK               (0x3F<<AUDDEC_ANA_CON8_BIAS_ADJ_POS)
#define AUDDEC_ANA_CON8_IBIAS_PDN_POS               (15)
#define AUDDEC_ANA_CON8_IBIAS_PDN_MASK              (1<<AUDDEC_ANA_CON8_IBIAS_PDN_POS)
#define AUDDEC_ANA_CON9                 (ABB_BASE + 0x0124)
#define AUDDEC_ANA_CON9_DECODER_RST_POS             (0)
#define AUDDEC_ANA_CON9_DECODER_RST_MASK            (1<<AUDDEC_ANA_CON9_DECODER_RST_POS)
#define AUDDEC_ANA_CON9_26M_CLK_SRC_POS             (1)
#define AUDDEC_ANA_CON9_26M_CLK_SRC_MASK            (1<<AUDDEC_ANA_CON9_26M_CLK_SRC_POS)
#define AUDDEC_ANA_CON9_13M_D5NS_DELAY_POS          (2)
#define AUDDEC_ANA_CON9_13M_D5NS_DELAY_MASK         (7<<AUDDEC_ANA_CON9_13M_D5NS_DELAY_POS)
#define AUDDEC_ANA_CON9_13M_D5NS_INV_POS            (5)
#define AUDDEC_ANA_CON9_13M_D5NS_INV_MASK           (1<<AUDDEC_ANA_CON9_13M_D5NS_INV_POS)
#define AUDDEC_ANA_CON9_DAC_13M_EN_POS              (6)
#define AUDDEC_ANA_CON9_DAC_13M_EN_MASK             (1<<AUDDEC_ANA_CON9_DAC_13M_EN_POS)
#define AUDDEC_ANA_CON9_13M_DUTY_POS                (7)
#define AUDDEC_ANA_CON9_13M_DUTY_MASK               (1<<AUDDEC_ANA_CON9_13M_DUTY_POS)
#define AUDDEC_ANA_CON9_DAC_26M_EN_POS              (8)
#define AUDDEC_ANA_CON9_DAC_26M_EN_MASK             (1<<AUDDEC_ANA_CON9_DAC_26M_EN_POS)
#define AUDDEC_ANA_CON9_CLD_26M_EN_POS              (9)
#define AUDDEC_ANA_CON9_CLD_26M_EN_MASK             (1<<AUDDEC_ANA_CON9_CLD_26M_EN_POS)
#define AUDDEC_ANA_CON9_13M_DIV_POS                 (10)
#define AUDDEC_ANA_CON9_13M_DIV_MASK                (3<<AUDDEC_ANA_CON9_13M_DIV_POS)
#define AUDDEC_ANA_CON9_CLKGEN_RSV_POS              (12)
#define AUDDEC_ANA_CON9_CLKGEN_RSV_MASK             (1<<AUDDEC_ANA_CON9_CLKGEN_RSV_POS)
#define AUDDEC_ANA_CON10                (ABB_BASE + 0x0128)
#define AUDDEC_ANA_CON10_PWR_VA33_POS               (2)
#define AUDDEC_ANA_CON10_PWR_VA33_MASK              (1<<AUDDEC_ANA_CON10_PWR_VA33_POS)
#define AUDDEC_ANA_CON10_1P5V_LDO_SEL_POS           (3)
#define AUDDEC_ANA_CON10_1P5V_LDO_SEL_MASK          (3<<AUDDEC_ANA_CON10_1P5V_LDO_SEL_POS)
#define AUDDEC_ANA_CON10_0P8V_LDO_SEL_POS           (5)
#define AUDDEC_ANA_CON10_0P8V_LDO_SEL_MASK          (3<<AUDDEC_ANA_CON10_0P8V_LDO_SEL_POS)
#define AUDDEC_ANA_CON10_GLB_RSV_POS                (7)
#define AUDDEC_ANA_CON10_GLB_RSV_MASK               (3<<AUDDEC_ANA_CON10_GLB_RSV_POS)
#define AUDDEC_ANA_CON10_2P4VLDO_CORE_EN_POS        (9)
#define AUDDEC_ANA_CON10_2P4VLDO_CORE_EN_MASK       (1<<AUDDEC_ANA_CON10_2P4VLDO_CORE_EN_POS)
#define AUDDEC_ANA_CON10_2P4VLDO_DEC_EN_POS         (10)
#define AUDDEC_ANA_CON10_2P4VLDO_DEC_EN_MASK        (1<<AUDDEC_ANA_CON10_2P4VLDO_DEC_EN_POS)
#define AUDDEC_ANA_CON10_2P4VLDO_DAC_EN_POS         (11)
#define AUDDEC_ANA_CON10_2P4VLDO_DAC_EN_MASK        (1<<AUDDEC_ANA_CON10_2P4VLDO_DAC_EN_POS)
#define AUDDEC_ANA_CON10_2P4V_LDO_VOL_SEL_POS       (12)
#define AUDDEC_ANA_CON10_2P4V_LDO_VOL_SEL_MASK      (7<<AUDDEC_ANA_CON10_2P4V_LDO_VOL_SEL_POS)
#define AUDDEC_ANA_CON11                (ABB_BASE + 0x012C)
#define AUDDEC_ANA_CON11_HPL_STBEHN_EN_POS          (0)
#define AUDDEC_ANA_CON11_HPL_STBEHN_EN_MASK         (1<<AUDDEC_ANA_CON11_HPL_STBEHN_EN_POS)
#define AUDDEC_ANA_CON11_HPR_STBEHN_EN_POS          (1)
#define AUDDEC_ANA_CON11_HPR_STBEHN_EN_MASK         (1<<AUDDEC_ANA_CON11_HPR_STBEHN_EN_POS)
#define AUDDEC_ANA_CON11_GLB_VCM_EN_POS             (2)
#define AUDDEC_ANA_CON11_GLB_VCM_EN_MASK            (1<<AUDDEC_ANA_CON11_GLB_VCM_EN_POS)
#define AUDDEC_ANA_CON11_HCLDO_EN_POS               (4)
#define AUDDEC_ANA_CON11_HCLDO_EN_MASK              (1<<AUDDEC_ANA_CON11_HCLDO_EN_POS)
#define AUDDEC_ANA_CON11_LCLDO_EN_POS               (5)
#define AUDDEC_ANA_CON11_LCLDO_EN_MASK              (1<<AUDDEC_ANA_CON11_LCLDO_EN_POS)
#define AUDDEC_ANA_CON11_LCLDO_DAC_EN_POS           (6)
#define AUDDEC_ANA_CON11_LCLDO_DAC_EN_MASK          (1<<AUDDEC_ANA_CON11_LCLDO_DAC_EN_POS)
#define AUDDEC_ANA_CON11_HCLDO_PDDIS_EN_POS         (7)
#define AUDDEC_ANA_CON11_HCLDO_PDDIS_EN_MASK        (1<<AUDDEC_ANA_CON11_HCLDO_PDDIS_EN_POS)
#define AUDDEC_ANA_CON11_HCLDO_REMOTE_POS           (8)
#define AUDDEC_ANA_CON11_HCLDO_REMOTE_MASK          (1<<AUDDEC_ANA_CON11_HCLDO_REMOTE_POS)
#define AUDDEC_ANA_CON11_HCLDO_TBST_EN_POS          (9)
#define AUDDEC_ANA_CON11_HCLDO_TBST_EN_MASK         (1<<AUDDEC_ANA_CON11_HCLDO_TBST_EN_POS)
#define AUDDEC_ANA_CON11_LCLDO_PDDIS_EN_POS         (10)
#define AUDDEC_ANA_CON11_LCLDO_PDDIS_EN_MASK        (1<<AUDDEC_ANA_CON11_LCLDO_PDDIS_EN_POS)
#define AUDDEC_ANA_CON11_LCLDO_TBST_EN_POS          (11)
#define AUDDEC_ANA_CON11_LCLDO_TBST_EN_MASK         (1<<AUDDEC_ANA_CON11_LCLDO_TBST_EN_POS)
#define AUDDEC_ANA_CON11_LCLDO_REMOTE_POS           (12)
#define AUDDEC_ANA_CON11_LCLDO_REMOTE_MASK          (1<<AUDDEC_ANA_CON11_LCLDO_REMOTE_POS)
#define AUDDEC_ANA_CON11_LCLDO_DAC_PDDIS_EN_POS     (13)
#define AUDDEC_ANA_CON11_LCLDO_DAC_PDDIS_EN_MASK    (1<<AUDDEC_ANA_CON11_LCLDO_DAC_PDDIS_EN_POS)
#define AUDDEC_ANA_CON11_LCLDO_DAC_TBST_EN_POS      (14)
#define AUDDEC_ANA_CON11_LCLDO_DAC_TBST_EN_MASK     (1<<AUDDEC_ANA_CON11_LCLDO_DAC_TBST_EN_POS)
#define AUDDEC_ANA_CON11_LCLDO_DAC_REMOTE_POS       (15)
#define AUDDEC_ANA_CON11_LCLDO_DAC_REMOTE_MASK      (1<<AUDDEC_ANA_CON11_LCLDO_DAC_REMOTE_POS)
#define AUDDEC_ANA_CON12                (ABB_BASE + 0x0130)
#define AUDDEC_ANA_CON12_VCMT_OP_EN_POS             (0)
#define AUDDEC_ANA_CON12_VCMT_OP_EN_MASK            (1<<AUDDEC_ANA_CON12_VCMT_OP_EN_POS)
#define AUDDEC_ANA_CON12_VCMT_RDIV_EN_POS           (1)
#define AUDDEC_ANA_CON12_VCMT_RDIV_EN_MASK          (1<<AUDDEC_ANA_CON12_VCMT_RDIV_EN_POS)
#define AUDDEC_ANA_CON12_VCMT_PAD_PULLLOW_POS       (2)
#define AUDDEC_ANA_CON12_VCMT_PAD_PULLLOW_MASK      (1<<AUDDEC_ANA_CON12_VCMT_PAD_PULLLOW_POS)
#define AUDDEC_ANA_CON12_VCMT_CHARGE_SEL_POS        (3)
#define AUDDEC_ANA_CON12_VCMT_CHARGE_SEL_MASK       (3<<AUDDEC_ANA_CON12_VCMT_CHARGE_SEL_POS)
#define AUDDEC_ANA_CON12_VCMT_CMP_THRE_SEL_POS      (5)
#define AUDDEC_ANA_CON12_VCMT_CMP_THRE_SEL_MASK     (3<<AUDDEC_ANA_CON12_VCMT_CMP_THRE_SEL_POS)
#define AUDDEC_ANA_CON12_VCMT_CLAMPING_POS          (7)
#define AUDDEC_ANA_CON12_VCMT_CLAMPING_MASK         (1<<AUDDEC_ANA_CON12_VCMT_CLAMPING_POS)
#define AUDDEC_ANA_CON12_VCMT_COMPARATOR_POS        (8)
#define AUDDEC_ANA_CON12_VCMT_COMPARATOR_MASK       (1<<AUDDEC_ANA_CON12_VCMT_COMPARATOR_POS)
#define AUDDEC_ANA_CON12_VCMT_FROM_PAD_POS          (9)
#define AUDDEC_ANA_CON12_VCMT_FROM_PAD_MASK         (1<<AUDDEC_ANA_CON12_VCMT_FROM_PAD_POS)
#define AUDDEC_ANA_CON12_VCMT_CMPOUT_POLARITY_POS   (13)
#define AUDDEC_ANA_CON12_VCMT_CMPOUT_POLARITY_MASK  (1<<AUDDEC_ANA_CON12_VCMT_CMPOUT_POLARITY_POS)
#define AUDDEC_ANA_CON12_VCMT_RSV0_POS              (9)
#define AUDDEC_ANA_CON12_VCMT_RSV0_MASK             (0X7F<<AUDDEC_ANA_CON12_VCMT_RSV0_POS)
#define AUDDEC_ANA_CON12_VCMT_RSV0_R_IDAC_POS       (10)
#define AUDDEC_ANA_CON12_VCMT_RSV0_R_IDAC_MASK      (1<<AUDDEC_ANA_CON12_VCMT_RSV0_R_IDAC_POS)
#define AUDDEC_ANA_CON12_VCMT_RSV0_L_IDAC_POS       (11)
#define AUDDEC_ANA_CON12_VCMT_RSV0_L_IDAC_MASK      (1<<AUDDEC_ANA_CON12_VCMT_RSV0_L_IDAC_POS)
#define AUDDEC_ANA_CON12_VCMT_RSV0_VCMT_CMPOUT_VA18_POS       (13)
#define AUDDEC_ANA_CON12_VCMT_RSV0_VCMT_CMPOUT_VA18_MASK      (1<<AUDDEC_ANA_CON12_VCMT_RSV0_VCMT_CMPOUT_VA18_POS)


#define AUDDEC_ANA_CON13                (ABB_BASE + 0x0134)
#define AUDDEC_ANA_CON13_PAD_AU_HPRP_EN_POS         (0)
#define AUDDEC_ANA_CON13_PAD_AU_HPRP_EN_MASK        (1<<AUDDEC_ANA_CON13_PAD_AU_HPRP_EN_POS)
#define AUDDEC_ANA_CON13_DAC_POS_OFFSET_EN_POS      (1)
#define AUDDEC_ANA_CON13_DAC_POS_OFFSET_EN_MASK     (1<<AUDDEC_ANA_CON13_DAC_POS_OFFSET_EN_POS)
#define AUDDEC_ANA_CON13_DAC_NEG_OFFSET_EN_POS      (2)
#define AUDDEC_ANA_CON13_DAC_NEG_OFFSET_EN_MASK     (1<<AUDDEC_ANA_CON13_DAC_NEG_OFFSET_EN_POS)
#define AUDDEC_ANA_CON13_CMFB_LNW_POS               (3)
#define AUDDEC_ANA_CON13_CMFB_LNW_MASK              (1<<AUDDEC_ANA_CON13_CMFB_LNW_POS)
#define AUDDEC_ANA_CON13_CMFB_RNW_POS               (4)
#define AUDDEC_ANA_CON13_CMFB_RNW_MASK              (1<<AUDDEC_ANA_CON13_CMFB_RNW_POS)
#define AUDDEC_ANA_CON13_GM2_BOOST_PDN_POS          (5)
#define AUDDEC_ANA_CON13_GM2_BOOST_PDN_MASK         (1<<AUDDEC_ANA_CON13_GM2_BOOST_PDN_POS)
#define AUDDEC_ANA_CON13_PULL_HIGH_POS              (6)
#define AUDDEC_ANA_CON13_PULL_HIGH_MASK             (1<<AUDDEC_ANA_CON13_PULL_HIGH_POS)
#define AUDDEC_ANA_CON13_PULL_LOW_POS               (7)
#define AUDDEC_ANA_CON13_PULL_LOW_MASK              (1<<AUDDEC_ANA_CON13_PULL_LOW_POS)
#define AUDDEC_ANA_CON13_HP_COM_CAP_SHORT_POS       (8)
#define AUDDEC_ANA_CON13_HP_COM_CAP_SHORT_MASK      (1<<AUDDEC_ANA_CON13_HP_COM_CAP_SHORT_POS)
#define AUDDEC_ANA_CON13_CLASSG_GAIN_MAX_POS        (9)
#define AUDDEC_ANA_CON13_CLASSG_GAIN_MAX_MASK       (1<<AUDDEC_ANA_CON13_CLASSG_GAIN_MAX_POS)
#define AUDDEC_ANA_CON13_LCLDO_DAC_EN_POS           (10)
#define AUDDEC_ANA_CON13_LCLDO_DAC_EN_MASK          (1<<AUDDEC_ANA_CON13_LCLDO_DAC_EN_POS)
#define AUDDEC_ANA_CON13_LCLDO_IBAS_EN_POS          (11)
#define AUDDEC_ANA_CON13_LCLDO_IBAS_EN_MASK         (1<<AUDDEC_ANA_CON13_LCLDO_IBAS_EN_POS)
#define AUDDEC_ANA_CON13_HCLDO_IBAS_EN_POS          (12)
#define AUDDEC_ANA_CON13_HCLDO_IBAS_EN_MASK         (1<<AUDDEC_ANA_CON13_HCLDO_IBAS_EN_POS)
#define AUDDEC_ANA_CON13_LDO_REF_IBAS_EN_POS        (13)
#define AUDDEC_ANA_CON13_LDO_REF_IBAS_EN_MASK       (1<<AUDDEC_ANA_CON13_LDO_REF_IBAS_EN_POS)
#define AUDDEC_ANA_CON13_OFF_COMPARATOR_POS         (14)
#define AUDDEC_ANA_CON13_OFF_COMPARATOR_MASK        (1<<AUDDEC_ANA_CON13_OFF_COMPARATOR_POS)
#define AUDDEC_ANA_CON13_HP_VCM_FROM_GLB_POS        (15)
#define AUDDEC_ANA_CON13_HP_VCM_FROM_GLB_MASK       (1<<AUDDEC_ANA_CON13_HP_VCM_FROM_GLB_POS)
#define AUDDEC_ANA_CON14                (ABB_BASE + 0x0138)
#define AUDDEC_ANA_CON15                (ABB_BASE + 0x013C)
#define AUDDEC_ANA_CON15_DAC_CURRENT_2X_POS         (8)
#define AUDDEC_ANA_CON15_DAC_CURRENT_2X_MASK        (1<<AUDDEC_ANA_CON15_DAC_CURRENT_2X_POS)
#define AUDDEC_ANA_CON15_DAC_CURRENT_4X_POS         (9)
#define AUDDEC_ANA_CON15_DAC_CURRENT_4X_MASK        (1<<AUDDEC_ANA_CON15_DAC_CURRENT_4X_POS)
#define AUDDEC_ANA_CON15_DAC_FILTER_SEL_POS         (10)
#define AUDDEC_ANA_CON15_DAC_FILTER_SEL_MASK        (1<<AUDDEC_ANA_CON15_DAC_FILTER_SEL_POS)
#define AUDDEC_ANA_CON16                (ABB_BASE + 0x0140)
#define AUDDEC_ANA_CON16_CLD_HPL_MAIN_STAGE_EN_POS  (0)
#define AUDDEC_ANA_CON16_CLD_HPL_MAIN_STAGE_EN_MASK (1<<AUDDEC_ANA_CON16_CLD_HPL_MAIN_STAGE_EN_POS)
#define AUDDEC_ANA_CON16_CLD_HPR_MAIN_STAGE_EN_POS  (1)
#define AUDDEC_ANA_CON16_CLD_HPR_MAIN_STAGE_EN_MASK (1<<AUDDEC_ANA_CON16_CLD_HPR_MAIN_STAGE_EN_POS)
#define AUDDEC_ANA_CON16_CLD_HPL_AUX_STAGE_EN_POS   (2)
#define AUDDEC_ANA_CON16_CLD_HPL_AUX_STAGE_EN_MASK  (1<<AUDDEC_ANA_CON16_CLD_HPL_AUX_STAGE_EN_POS)
#define AUDDEC_ANA_CON16_CLD_HPR_AUX_STAGE_EN_POS   (3)
#define AUDDEC_ANA_CON16_CLD_HPR_AUX_STAGE_EN_MASK  (1<<AUDDEC_ANA_CON16_CLD_HPR_AUX_STAGE_EN_POS)
#define AUDDEC_ANA_CON16_CLD_HPL_PWRUP_POS          (4)
#define AUDDEC_ANA_CON16_CLD_HPL_PWRUP_MASK         (1<<AUDDEC_ANA_CON16_CLD_HPL_PWRUP_POS)
#define AUDDEC_ANA_CON16_CLD_HPR_PWRUP_POS          (5)
#define AUDDEC_ANA_CON16_CLD_HPR_PWRUP_MASK         (1<<AUDDEC_ANA_CON16_CLD_HPR_PWRUP_POS)
#define AUDDEC_ANA_CON16_CLD_HPL_IBAS_POS           (6)
#define AUDDEC_ANA_CON16_CLD_HPL_IBAS_MASK          (1<<AUDDEC_ANA_CON16_CLD_HPL_IBAS_POS)
#define AUDDEC_ANA_CON16_CLD_HPR_IBAS_POS           (7)
#define AUDDEC_ANA_CON16_CLD_HPR_IBAS_MASK          (1<<AUDDEC_ANA_CON16_CLD_HPR_IBAS_POS)
#define AUDDEC_ANA_CON16_CLD_HPL_MUXIN_SEL_POS      (8)
#define AUDDEC_ANA_CON16_CLD_HPL_MUXIN_SEL_MASK     (3<<AUDDEC_ANA_CON16_CLD_HPL_MUXIN_SEL_POS)
#define AUDDEC_ANA_CON16_CLD_HPR_MUXIN_SEL_POS      (10)
#define AUDDEC_ANA_CON16_CLD_HPR_MUXIN_SEL_MASK     (3<<AUDDEC_ANA_CON16_CLD_HPR_MUXIN_SEL_POS)

#define AUDDEC_ANA_CON17                (ABB_BASE + 0x0144)
#define AUDDEC_ANA_CON17_CLD_HPL_FB_RSW_EN_POS      (7)
#define AUDDEC_ANA_CON17_CLD_HPL_FB_RSW_EN_MASK     (1<<AUDDEC_ANA_CON17_CLD_HPL_FB_RSW_EN_POS)
#define AUDDEC_ANA_CON17_CLD_HPR_FB_RSW_EN_POS      (8)
#define AUDDEC_ANA_CON17_CLD_HPR_FB_RSW_EN_MASK     (1<<AUDDEC_ANA_CON17_CLD_HPR_FB_RSW_EN_POS)
#define AUDDEC_ANA_CON18                (ABB_BASE + 0x0148)
#define AUDDEC_ANA_CON18_HPL_AUX_GAIN_POS           (0)
#define AUDDEC_ANA_CON18_HPL_AUX_GAIN_MASK          (0xF<<AUDDEC_ANA_CON18_HPL_AUX_GAIN_POS)
#define AUDDEC_ANA_CON18_HPR_AUX_GAIN_POS           (4)
#define AUDDEC_ANA_CON18_HPR_AUX_GAIN_MASK          (0xF<<AUDDEC_ANA_CON18_HPR_AUX_GAIN_POS)
#define AUDDEC_ANA_CON19                (ABB_BASE + 0x014C)
#define AUDDEC_ANA_CON20                (ABB_BASE + 0x0150)
#define AUDDEC_ANA_CON20_HP_DIFF_IBIAS_POS          (4)
#define AUDDEC_ANA_CON20_HP_DIFF_IBIAS_MASK         (7<<AUDDEC_ANA_CON20_HP_DIFF_IBIAS_POS)
#define AUDDEC_ANA_CON21                (ABB_BASE + 0x0154)
#define AUDDEC_ANA_CON21_CLD_BIAS_EN_POS            (2-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_BIAS_EN_MASK           (1<<AUDDEC_ANA_CON21_CLD_BIAS_EN_POS)
#define AUDDEC_ANA_CON21_CLD_CMP_EN_POS             (3-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_CMP_EN_MASK            (1<<AUDDEC_ANA_CON21_CLD_CMP_EN_POS)
#define AUDDEC_ANA_CON21_CLD_LF_EN_POS              (4-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_LF_EN_MASK             (1<<AUDDEC_ANA_CON21_CLD_LF_EN_POS)
#define AUDDEC_ANA_CON21_CLD_NONOVER_EN_POS         (5-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_NONOVER_EN_MASK        (1<<AUDDEC_ANA_CON21_CLD_NONOVER_EN_POS)
#define AUDDEC_ANA_CON21_CLD_SAWGEN_EN_POS          (6-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_SAWGEN_EN_MASK         (1<<AUDDEC_ANA_CON21_CLD_SAWGEN_EN_POS)
#define AUDDEC_ANA_CON21_CLD_VCMINT_EN_POS          (7-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_VCMINT_EN_MASK         (1<<AUDDEC_ANA_CON21_CLD_VCMINT_EN_POS)
#define AUDDEC_ANA_CON21_CLD_VCMRED_EN_POS          (8-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_VCMRED_EN_MASK         (1<<AUDDEC_ANA_CON21_CLD_VCMRED_EN_POS)
#define AUDDEC_ANA_CON21_CLD_VCM_SEL_POS            (9-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_VCM_SEL_MASK           (1<<AUDDEC_ANA_CON21_CLD_VCM_SEL_POS)
#define AUDDEC_ANA_CON21_CLD_DZ_SEL_POS             (10-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_DZ_SEL_MASK            (3<<AUDDEC_ANA_CON21_CLD_DZ_SEL_POS)
#define AUDDEC_ANA_CON21_CLD_SR_SEL_POS             (12-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_SR_SEL_MASK            (3<<AUDDEC_ANA_CON21_CLD_SR_SEL_POS)
#define AUDDEC_ANA_CON21_CLD_CMP_BIAS_SEL_POS       (14-1)//ab1568 needs to shift by 1 bit
#define AUDDEC_ANA_CON21_CLD_CMP_BIAS_SEL_MASK      (3<<AUDDEC_ANA_CON21_CLD_CMP_BIAS_SEL_POS)
#define AUDDEC_ANA_CON22                (ABB_BASE + 0x0158)
#define AUDDEC_ANA_CON22_CLD_LF1_SEL_POS            (0)
#define AUDDEC_ANA_CON22_CLD_LF1_SEL_MASK           (3<<AUDDEC_ANA_CON22_CLD_LF1_SEL_POS)
#define AUDDEC_ANA_CON22_CLD_LF2_SEL_POS            (2)
#define AUDDEC_ANA_CON22_CLD_LF2_SEL_MASK           (3<<AUDDEC_ANA_CON22_CLD_LF2_SEL_POS)
#define AUDDEC_ANA_CON22_CLD_SAWOP_SEL_POS          (4)
#define AUDDEC_ANA_CON22_CLD_SAWOP_SEL_MASK         (3<<AUDDEC_ANA_CON22_CLD_SAWOP_SEL_POS)
#define AUDDEC_ANA_CON22_CLD_VCMINT_SEL_POS         (6)
#define AUDDEC_ANA_CON22_CLD_VCMINT_SEL_MASK        (3<<AUDDEC_ANA_CON22_CLD_VCMINT_SEL_POS)
#define AUDDEC_ANA_CON22_CLD_VCMRED_SEL_POS         (8)
#define AUDDEC_ANA_CON22_CLD_VCMRED_SEL_MASK        (3<<AUDDEC_ANA_CON22_CLD_VCMRED_SEL_POS)
#define AUDDEC_ANA_CON22_CLD_CMP_BIAS_SEL_POS       (10)
#define AUDDEC_ANA_CON22_CLD_CMP_BIAS_SEL_MASK      (3<<AUDDEC_ANA_CON22_CLD_CMP_BIAS_SEL_POS)
#define AUDDEC_ANA_CON22_CLD_CHOP_FREQ_SEL_POS      (13)
#define AUDDEC_ANA_CON22_CLD_CHOP_FREQ_SEL_MASK     (7<<AUDDEC_ANA_CON22_CLD_CHOP_FREQ_SEL_POS)
#define AUDDEC_ANA_CON23                (ABB_BASE + 0x015C)
#define AUDDEC_ANA_CON23_CLD_SAW_FREQ_SEL_POS       (1)
#define AUDDEC_ANA_CON23_CLD_SAW_FREQ_SEL_MASK      (7<<AUDDEC_ANA_CON23_CLD_SAW_FREQ_SEL_POS)
#define AUDDEC_ANA_CON23_CLD_CHOP_EN_POS            (4)
#define AUDDEC_ANA_CON23_CLD_CHOP_EN_MASK           (1<<AUDDEC_ANA_CON23_CLD_CHOP_EN_POS)
#define AUDDEC_ANA_CON23_CLD_CAPTRIM_SEL_POS        (5)
#define AUDDEC_ANA_CON23_CLD_CAPTRIM_SEL_MASK       (1<<AUDDEC_ANA_CON23_CLD_CAPTRIM_SEL_POS)
#define AUDDEC_ANA_CON23_CLD_CAPTRIM_POS            (6)
#define AUDDEC_ANA_CON23_CLD_CAPTRIM_MASK           (0x1F<<AUDDEC_ANA_CON23_CLD_CAPTRIM_POS)
#define AUDDEC_ANA_CON23_CLD_CAP_GAIN_POS           (11)
#define AUDDEC_ANA_CON23_CLD_CAP_GAIN_MASK          (0x1F<<AUDDEC_ANA_CON23_CLD_CAP_GAIN_POS)
#define AUDDEC_ANA_CON24                (ABB_BASE + 0x0160)
#define AUDDEC_ANA_CON24_CLD_SAW_GAIN_POS           (3)
#define AUDDEC_ANA_CON24_CLD_SAW_GAIN_MASK          (0x1F<<AUDDEC_ANA_CON24_CLD_SAW_GAIN_POS)
#define AUDDEC_ANA_CON24_CLD_PWM_SEL_POS            (8)
#define AUDDEC_ANA_CON24_CLD_PWM_SEL_MASK           (3<<AUDDEC_ANA_CON24_CLD_PWM_SEL_POS)
#define AUDDEC_ANA_CON24_CLD_CHOP_MPH_EN_POS        (10)
#define AUDDEC_ANA_CON24_CLD_CHOP_MPH_EN_MASK       (1<<AUDDEC_ANA_CON24_CLD_CHOP_MPH_EN_POS)
#define AUDDEC_ANA_CON24_CLD_RST_POS                (11)
#define AUDDEC_ANA_CON24_CLD_RST_MASK               (1<<AUDDEC_ANA_CON24_CLD_RST_POS)
#define AUDDEC_ANA_CON24_CLD_OUTPUT_STAGE_EN_POS    (12)
#define AUDDEC_ANA_CON24_CLD_OUTPUT_STAGE_EN_MASK   (1<<AUDDEC_ANA_CON24_CLD_OUTPUT_STAGE_EN_POS)
#define AUDDEC_ANA_CON24_CLD_LOOP_EN_POS            (13)
#define AUDDEC_ANA_CON24_CLD_LOOP_EN_MASK           (1<<AUDDEC_ANA_CON24_CLD_LOOP_EN_POS)
#define AUDDEC_ANA_CON24_CLD_ZCD_SEL_POS            (14)
#define AUDDEC_ANA_CON24_CLD_ZCD_SEL_MASK           (1<<AUDDEC_ANA_CON24_CLD_ZCD_SEL_POS)
#define AUDDEC_ANA_CON24_CLD_RCH_TRIM_POS           (15)
#define AUDDEC_ANA_CON24_CLD_RCH_TRIM_MASK          (0x1F<<AUDDEC_ANA_CON24_CLD_RCH_TRIM_POS)
#define AUDDEC_ANA_CON24_CLD_DEPOP_SYNC_CLK_POS     (20)
#define AUDDEC_ANA_CON24_CLD_DEPOP_SYNC_CLK_MASK    (1<<AUDDEC_ANA_CON24_CLD_DEPOP_SYNC_CLK_POS)

#define AUDDEC_ANA_CON25                (ABB_BASE + 0x0164)
#define AUDDEC_ANA_CON25_CLD_VCMRED_EN_POS          (0)
#define AUDDEC_ANA_CON25_CLD_VCMRED_EN_MASK         (1<<AUDDEC_ANA_CON25_CLD_VCMRED_EN_POS)
#define AUDDEC_ANA_CON25_CLD_HPL_GAIN_POS           (2)
#define AUDDEC_ANA_CON25_CLD_HPL_GAIN_MASK          (0x3F<<AUDDEC_ANA_CON25_CLD_HPL_GAIN_POS)
#define AUDDEC_ANA_CON25_CLD_HPR_GAIN_POS           (10)
#define AUDDEC_ANA_CON25_CLD_HPR_GAIN_MASK          (0x3F<<AUDDEC_ANA_CON25_CLD_HPR_GAIN_POS)
#define AUDDEC_ANA_CON26                (ABB_BASE + 0x0168)
#define AUDDEC_ANA_CON26_HPL_OUT_STAG_POS           (1)
#define AUDDEC_ANA_CON26_HPL_OUT_STAG_MASK          (7<<AUDDEC_ANA_CON26_HPL_OUT_STAG_POS)
#define AUDDEC_ANA_CON26_HPR_OUT_STAG_POS           (5)
#define AUDDEC_ANA_CON26_HPR_OUT_STAG_MASK          (7<<AUDDEC_ANA_CON26_HPR_OUT_STAG_POS)
#define AUDENC_ANA_CON0                 (ABB_BASE + 0x0200)
#define AUDENC_ANA_CON0_L_PREAMP_EN_POS             (0)
#define AUDENC_ANA_CON0_L_PREAMP_EN_MASK            (1<<AUDENC_ANA_CON0_L_PREAMP_EN_POS)
#define AUDENC_ANA_CON0_L_DCC_MODE_POS              (1)
#define AUDENC_ANA_CON0_L_DCC_MODE_MASK             (1<<AUDENC_ANA_CON0_L_DCC_MODE_POS)
#define AUDENC_ANA_CON0_L_PREAMP_PRECHARGE_DCC_POS  (2)
#define AUDENC_ANA_CON0_L_PREAMP_PRECHARGE_DCC_MASK (1<<AUDENC_ANA_CON0_L_PREAMP_PRECHARGE_DCC_POS)
#define AUDENC_ANA_CON0_L_PREAMP_INPUT_SEL_POS      (6)
#define AUDENC_ANA_CON0_L_PREAMP_INPUT_SEL_MASK     (3<<AUDENC_ANA_CON0_L_PREAMP_INPUT_SEL_POS)
#define AUDENC_ANA_CON0_L_PREAMP_GAIN_POS           (8)
#define AUDENC_ANA_CON0_L_PREAMP_GAIN_MASK          (0xF<<AUDENC_ANA_CON0_L_PREAMP_GAIN_POS)
#define AUDENC_ANA_CON0_L_ADC_EN_POS                (12)
#define AUDENC_ANA_CON0_L_ADC_EN_MASK               (1<<AUDENC_ANA_CON0_L_ADC_EN_POS)
#define AUDENC_ANA_CON0_L_INPUT_SEL_POS             (13)
#define AUDENC_ANA_CON0_L_INPUT_SEL_MASK            (3<<AUDENC_ANA_CON0_L_INPUT_SEL_POS)
#define AUDENC_ANA_CON0_L_ACC20K_EN_POS             (15)
#define AUDENC_ANA_CON0_L_ACC20K_EN_MASK            (1<<AUDENC_ANA_CON0_L_ACC20K_EN_POS)
#define AUDENC_ANA_CON1                 (ABB_BASE + 0x0204)
#define AUDENC_ANA_CON1_R_PREAMP_EN_POS             (0)
#define AUDENC_ANA_CON1_R_PREAMP_EN_MASK            (1<<AUDENC_ANA_CON1_R_PREAMP_EN_POS)
#define AUDENC_ANA_CON1_R_DCC_MODE_POS              (1)
#define AUDENC_ANA_CON1_R_DCC_MODE_MASK             (1<<AUDENC_ANA_CON1_R_DCC_MODE_POS)
#define AUDENC_ANA_CON1_R_PREAMP_PRECHARGE_DCC_POS  (2)
#define AUDENC_ANA_CON1_R_PREAMP_PRECHARGE_DCC_MASK (1<<AUDENC_ANA_CON1_R_PREAMP_PRECHARGE_DCC_POS)
#define AUDENC_ANA_CON1_R_PREAMP_INPUT_SEL_POS      (6)
#define AUDENC_ANA_CON1_R_PREAMP_INPUT_SEL_MASK     (3<<AUDENC_ANA_CON1_R_PREAMP_INPUT_SEL_POS)
#define AUDENC_ANA_CON1_R_PREAMP_GAIN_POS           (8)
#define AUDENC_ANA_CON1_R_PREAMP_GAIN_MASK          (0xF<<AUDENC_ANA_CON1_R_PREAMP_GAIN_POS)
#define AUDENC_ANA_CON1_R_ADC_EN_POS                (12)
#define AUDENC_ANA_CON1_R_ADC_EN_MASK               (1<<AUDENC_ANA_CON1_R_ADC_EN_POS)
#define AUDENC_ANA_CON1_R_INPUT_SEL_POS             (13)
#define AUDENC_ANA_CON1_R_INPUT_SEL_MASK            (3<<AUDENC_ANA_CON1_R_INPUT_SEL_POS)
#define AUDENC_ANA_CON1_R_ACC20K_EN_POS             (15)
#define AUDENC_ANA_CON1_R_ACC20K_EN_MASK            (1<<AUDENC_ANA_CON1_R_ACC20K_EN_POS)
#define AUDENC_ANA_CON2                 (ABB_BASE + 0x0208)
#define AUDENC_ANA_CON2_HALF_BIAS_EN_POS            (0)
#define AUDENC_ANA_CON2_HALF_BIAS_EN_MASK           (1<<AUDENC_ANA_CON2_HALF_BIAS_EN_POS)
#define AUDENC_ANA_CON2_PREAMP_LP_MODE1_EN_POS      (1)
#define AUDENC_ANA_CON2_PREAMP_LP_MODE1_EN_MASK     (1<<AUDENC_ANA_CON2_PREAMP_LP_MODE1_EN_POS)
#define AUDENC_ANA_CON2_PREAMP_LP_MODE2_EN_POS      (2)
#define AUDENC_ANA_CON2_PREAMP_LP_MODE2_EN_MASK     (1<<AUDENC_ANA_CON2_PREAMP_LP_MODE2_EN_POS)
#define AUDENC_ANA_CON2_LP_1ST_STAGE_EN_POS         (3)
#define AUDENC_ANA_CON2_LP_1ST_STAGE_EN_MASK        (1<<AUDENC_ANA_CON2_LP_1ST_STAGE_EN_POS)
#define AUDENC_ANA_CON2_LP_2ND_STAGE_EN_POS         (4)
#define AUDENC_ANA_CON2_LP_2ND_STAGE_EN_MASK        (1<<AUDENC_ANA_CON2_LP_2ND_STAGE_EN_POS)
#define AUDENC_ANA_CON2_FLASH_LP_EN_POS             (5)
#define AUDENC_ANA_CON2_FLASH_LP_EN_MASK            (1<<AUDENC_ANA_CON2_FLASH_LP_EN_POS)
#define AUDENC_ANA_CON3                 (ABB_BASE + 0x020C)
#define AUDENC_ANA_CON3_ADC_CLK_SEL_POS             (0)
#define AUDENC_ANA_CON3_ADC_CLK_SEL_MASK            (3<<AUDENC_ANA_CON3_ADC_CLK_SEL_POS)
#define AUDENC_ANA_CON3_ADC_CLK_SRC_POS             (2)
#define AUDENC_ANA_CON3_ADC_CLK_SRC_MASK            (3<<AUDENC_ANA_CON3_ADC_CLK_SRC_POS)
#define AUDENC_ANA_CON3_UL_CLK_FROM_CLKSQ_POS       (4)
#define AUDENC_ANA_CON3_UL_CLK_FROM_CLKSQ_MASK      (1<<AUDENC_ANA_CON3_UL_CLK_FROM_CLKSQ_POS)
#define AUDENC_ANA_CON3_0P25FS_EN_POS               (5)
#define AUDENC_ANA_CON3_0P25FS_EN_MASK              (1<<AUDENC_ANA_CON3_0P25FS_EN_POS)
#define AUDENC_ANA_CON3_L_PGA_ACCFS_POS             (6)
#define AUDENC_ANA_CON3_L_PGA_ACCFS_MASK            (1<<AUDENC_ANA_CON3_L_PGA_ACCFS_POS)
#define AUDENC_ANA_CON3_R_PGA_ACCFS_POS             (7)
#define AUDENC_ANA_CON3_R_PGA_ACCFS_MASK            (1<<AUDENC_ANA_CON3_R_PGA_ACCFS_POS)
#define AUDENC_ANA_CON3_PREAMP_LOWPEN1_POS          (8)
#define AUDENC_ANA_CON3_PREAMP_LOWPEN1_MASK         (1<<AUDENC_ANA_CON3_PREAMP_LOWPEN1_POS)
#define AUDENC_ANA_CON3_PREAMP_LOWPEN2_POS          (9)
#define AUDENC_ANA_CON3_PREAMP_LOWPEN2_MASK         (1<<AUDENC_ANA_CON3_PREAMP_LOWPEN2_POS)
#define AUDENC_ANA_CON3_RG_AUD01PREAMP_LOWPEN_ORIGIN_POS          (10)
#define AUDENC_ANA_CON3_RG_AUD01PREAMP_LOWPEN_ORIGIN_MASK         (1<<AUDENC_ANA_CON3_RG_AUD01PREAMP_LOWPEN_ORIGIN_POS)

#define AUDENC_ANA_CON4                 (ABB_BASE + 0x0210)
#define AUDENC_ANA_CON5                 (ABB_BASE + 0x0214)
#define AUDENC_ANA_CON6                 (ABB_BASE + 0x0218)
#define AUDENC_ANA_CON6_ADC_CLK_RST_POS             (15)
#define AUDENC_ANA_CON6_ADC_CLK_RST_MASK            (1<<AUDENC_ANA_CON6_ADC_CLK_RST_POS)
#define AUDENC_ANA_CON7                 (ABB_BASE + 0x021C)
#define AUDENC_ANA_CON7_ADC_LP_EN_POS               (0)
#define AUDENC_ANA_CON7_ADC_LP_EN_MASK              (1<<AUDENC_ANA_CON7_ADC_LP_EN_POS)
#define AUDENC_ANA_CON7_ADC_RST_FOR_LP_POS          (1)
#define AUDENC_ANA_CON7_ADC_RST_FOR_LP_MASK         (1<<AUDENC_ANA_CON7_ADC_RST_FOR_LP_POS)
#define AUDENC_ANA_CON8                 (ABB_BASE + 0x0220)
#define AUDENC_ANA_CON9                 (ABB_BASE + 0x0224)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLON_POS             (0)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLON_MASK            (1<<AUDENC_ANA_CON9_RG_AUD23PREAMPLON_POS)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLDCCEN_POS          (1)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLDCCEN_MASK         (1<<AUDENC_ANA_CON9_RG_AUD23PREAMPLDCCEN_POS)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLDCPRECHARGE_POS    (2)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLDCPRECHARGE_MASK   (1<<AUDENC_ANA_CON9_RG_AUD23PREAMPLDCPRECHARGE_POS)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLPGATEST_POS        (3)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLPGATEST_MASK       (1<<AUDENC_ANA_CON9_RG_AUD23PREAMPLPGATEST_POS)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLVSCALE_POS         (4)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLVSCALE_MASK        (3<<AUDENC_ANA_CON9_RG_AUD23PREAMPLVSCALE_POS)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLINPUTSEL_POS       (6)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLINPUTSEL_MASK      (3<<AUDENC_ANA_CON9_RG_AUD23PREAMPLINPUTSEL_POS)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLGAIN_POS           (8)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPLGAIN_MASK          (0xF<<AUDENC_ANA_CON9_RG_AUD23PREAMPLGAIN_POS)
#define AUDENC_ANA_CON9_RG_AUD23ADCLPWRUP_POS             (12)
#define AUDENC_ANA_CON9_RG_AUD23ADCLPWRUP_MASK            (1<<AUDENC_ANA_CON9_RG_AUD23ADCLPWRUP_POS)
#define AUDENC_ANA_CON9_RG_AUD23ADCLINPUTSEL_POS          (13)
#define AUDENC_ANA_CON9_RG_AUD23ADCLINPUTSEL_MASK         (3<<AUDENC_ANA_CON9_RG_AUD23ADCLINPUTSEL_POS)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPL_ACC20K_EN_POS     (15)
#define AUDENC_ANA_CON9_RG_AUD23PREAMPL_ACC20K_EN_MASK    (1<<AUDENC_ANA_CON9_RG_AUD23PREAMPL_ACC20K_EN_POS)
#define AUDENC_ANA_CON10                 (ABB_BASE + 0x0228)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRON_POS             (0)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRON_MASK            (1<<AUDENC_ANA_CON10_RG_AUD23PREAMPRON_POS)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRDCCEN_POS          (1)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRDCCEN_MASK         (1<<AUDENC_ANA_CON10_RG_AUD23PREAMPRDCCEN_POS)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRDCPRECHARGE_POS    (2)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRDCPRECHARGE_MASK   (1<<AUDENC_ANA_CON10_RG_AUD23PREAMPRDCPRECHARGE_POS)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRPGATEST_POS        (3)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRPGATEST_MASK       (1<<AUDENC_ANA_CON10_RG_AUD23PREAMPRPGATEST_POS)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRVSCALE_POS         (4)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRVSCALE_MASK        (3<<AUDENC_ANA_CON10_RG_AUD23PREAMPRVSCALE_POS)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRINPUTSEL_POS       (6)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRINPUTSEL_MASK      (3<<AUDENC_ANA_CON10_RG_AUD23PREAMPRINPUTSEL_POS)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRGAIN_POS           (8)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPRGAIN_MASK          (0xF<<AUDENC_ANA_CON10_RG_AUD23PREAMPRGAIN_POS)
#define AUDENC_ANA_CON10_RG_AUD23ADCRPWRUP_POS             (12)
#define AUDENC_ANA_CON10_RG_AUD23ADCRPWRUP_MASK            (1<<AUDENC_ANA_CON10_RG_AUD23ADCRPWRUP_POS)
#define AUDENC_ANA_CON10_RG_AUD23ADCRINPUTSEL_POS          (13)
#define AUDENC_ANA_CON10_RG_AUD23ADCRINPUTSEL_MASK         (3<<AUDENC_ANA_CON10_RG_AUD23ADCRINPUTSEL_POS)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPR_ACC20K_EN_POS     (15)
#define AUDENC_ANA_CON10_RG_AUD23PREAMPR_ACC20K_EN_MASK    (1<<AUDENC_ANA_CON10_RG_AUD23PREAMPR_ACC20K_EN_POS)

#define AUDENC_ANA_CON11                (ABB_BASE + 0x022C)
#define AUDENC_ANA_CON11_RG_AUD23ULHALFBIAS_POS          (0)
#define AUDENC_ANA_CON11_RG_AUD23ULHALFBIAS_MASK         (1<<AUDENC_ANA_CON11_RG_AUD23ULHALFBIAS_POS)
#define AUDENC_ANA_CON11_RG_AUD23PREAMPLP1EN_POS         (1)
#define AUDENC_ANA_CON11_RG_AUD23PREAMPLP1EN_MASK        (1<<AUDENC_ANA_CON11_RG_AUD23PREAMPLP1EN_POS)
#define AUDENC_ANA_CON11_RG_AUD23PREAMPLP2EN_POS         (2)
#define AUDENC_ANA_CON11_RG_AUD23PREAMPLP2EN_MASK        (1<<AUDENC_ANA_CON11_RG_AUD23PREAMPLP2EN_POS)
#define AUDENC_ANA_CON11_RG_AUD23ADC1STSTAGELPEN_POS     (3)
#define AUDENC_ANA_CON11_RG_AUD23ADC1STSTAGELPEN_MASK    (1<<AUDENC_ANA_CON11_RG_AUD23ADC1STSTAGELPEN_POS)
#define AUDENC_ANA_CON11_RG_AUD23ADC2NDSTAGELPEN_POS     (4)
#define AUDENC_ANA_CON11_RG_AUD23ADC2NDSTAGELPEN_MASK    (1<<AUDENC_ANA_CON11_RG_AUD23ADC2NDSTAGELPEN_POS)
#define AUDENC_ANA_CON11_RG_AUD23ADCFLASHLPEN_POS        (5)
#define AUDENC_ANA_CON11_RG_AUD23ADCFLASHLPEN_MASK       (1<<AUDENC_ANA_CON11_RG_AUD23ADCFLASHLPEN_POS)
#define AUDENC_ANA_CON11_RG_AUD23PREAMPIDDTEST_POS       (6)
#define AUDENC_ANA_CON11_RG_AUD23PREAMPIDDTEST_MASK      (3<<AUDENC_ANA_CON11_RG_AUD23PREAMPIDDTEST_POS)
#define AUDENC_ANA_CON11_RG_AUD23ADC1STSTAGEIDDTEST_POS  (8)
#define AUDENC_ANA_CON11_RG_AUD23ADC1STSTAGEIDDTEST_MASK (3<<AUDENC_ANA_CON11_RG_AUD23ADC1STSTAGEIDDTEST_POS)
#define AUDENC_ANA_CON11_RG_AUD23ADC2NDSTAGEIDDTEST_POS  (10)
#define AUDENC_ANA_CON11_RG_AUD23ADC2NDSTAGEIDDTEST_MASK (3<<AUDENC_ANA_CON11_RG_AUD23ADC2NDSTAGEIDDTEST_POS)
#define AUDENC_ANA_CON11_RG_AUD23ADCREFBUFIDDTEST_POS    (12)
#define AUDENC_ANA_CON11_RG_AUD23ADCREFBUFIDDTEST_MASK   (1<<AUDENC_ANA_CON11_RG_AUD23ADCREFBUFIDDTEST_POS)
#define AUDENC_ANA_CON11_RG_AUD23ADCFLASHIDDTEST_POS     (14)
#define AUDENC_ANA_CON11_RG_AUD23ADCFLASHIDDTEST_MASK    (3<<AUDENC_ANA_CON11_RG_AUD23ADCFLASHIDDTEST_POS)
#define AUDENC_ANA_CON12                (ABB_BASE + 0x0230)
#define AUDENC_ANA_CON12_RG_AUD23ADCCLKSEL_POS      (0)
#define AUDENC_ANA_CON12_RG_AUD23ADCCLKSEL_MASK     (3<<AUDENC_ANA_CON12_RG_AUD23ADCCLKSEL_POS)
#define AUDENC_ANA_CON12_RG_AUD23ADCCLKSOURCE_POS   (2)
#define AUDENC_ANA_CON12_RG_AUD23ADCCLKSOURCE_MASK  (3<<AUDENC_ANA_CON12_RG_AUD23ADCCLKSOURCE_POS)
#define AUDENC_ANA_CON12_RG_AUD23ADC_13MCK_EN_POS   (4)
#define AUDENC_ANA_CON12_RG_AUD23ADC_13MCK_EN_MASK  (1<<AUDENC_ANA_CON12_RG_AUD23ADC_13MCK_EN_POS)
#define AUDENC_ANA_CON12_RG_AUD23ADCDAC0P25FS_POS   (5)
#define AUDENC_ANA_CON12_RG_AUD23ADCDAC0P25FS_MASK  (1<<AUDENC_ANA_CON12_RG_AUD23ADCDAC0P25FS_POS)
#define AUDENC_ANA_CON12_RG_AUD23PGAL_ACCFS_POS     (6)
#define AUDENC_ANA_CON12_RG_AUD23PGAL_ACCFS_MASK    (1<<AUDENC_ANA_CON12_RG_AUD23PGAL_ACCFS_POS)
#define AUDENC_ANA_CON12_RG_AUD23PGAR_ACCFS_POS     (7)
#define AUDENC_ANA_CON12_RG_AUD23PGAR_ACCFS_MASK    (1<<AUDENC_ANA_CON12_RG_AUD23PGAR_ACCFS_POS)
#define AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN1_POS (8)
#define AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN1_MASK (1<<AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN1_POS)
#define AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN2_POS (9)
#define AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN2_MASK (1<<AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN2_POS)
#define AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN_ORIGIN_POS (10)
#define AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN_ORIGIN_MASK (1<<AUDENC_ANA_CON12_RG_AUD23PREAMP_LOWPEN_ORIGIN_POS)
#define AUDENC_ANA_CON15                (ABB_BASE + 0x023C)
#define AUDENC_ANA_CON15_RG_AUD23RCTUNEL_POS            (0)
#define AUDENC_ANA_CON15_RG_AUD23RCTUNEL_MASK           (0x1F<<AUDENC_ANA_CON15_RG_AUD23RCTUNEL_POS)
#define AUDENC_ANA_CON15_RG_AUD23RCTUNELSEL_POS         (5)
#define AUDENC_ANA_CON15_RG_AUD23RCTUNELSEL_MASK        (1<<AUDENC_ANA_CON15_RG_AUD23RCTUNELSEL_POS)
#define AUDENC_ANA_CON15_RG_AUD23RCTUNER_POS            (8)
#define AUDENC_ANA_CON15_RG_AUD23RCTUNER_MASK           (0x1F<<AUDENC_ANA_CON15_RG_AUD23RCTUNER_POS)
#define AUDENC_ANA_CON15_RG_AUD23RCTUNERSEL_POS         (13)
#define AUDENC_ANA_CON15_RG_AUD23RCTUNERSEL_MASK        (1<<AUDENC_ANA_CON15_RG_AUD23RCTUNERSEL_POS)
#define AUDENC_ANA_CON15_RG_AUD23ADCSYNCCLK_INV_POS     (14)
#define AUDENC_ANA_CON15_RG_AUD23ADCSYNCCLK_INV_MASK    (1<<AUDENC_ANA_CON15_RG_AUD23ADCSYNCCLK_INV_POS)
#define AUDENC_ANA_CON15_RG_AUD23ADCCLKRATEQUARTER_POS  (15)
#define AUDENC_ANA_CON15_RG_AUD23ADCCLKRATEQUARTER_MASK (1<<AUDENC_ANA_CON15_RG_AUD23ADCCLKRATEQUARTER_POS)
#define AUDENC_ANA_CON16                (ABB_BASE + 0x0240)
#define AUDENC_ANA_CON16_RG_AUD23ADCCLKHALFRST_POS   (0)
#define AUDENC_ANA_CON16_RG_AUD23ADCCLKHALFRST_MASK  (1<<AUDENC_ANA_CON16_RG_AUD23ADCCLKHALFRST_POS)
#define AUDENC_ANA_CON16_RG_AUD23ADCCLKRATEHALF_POS  (1)
#define AUDENC_ANA_CON16_RG_AUD23ADCCLKRATEHALF_MASK (1<<AUDENC_ANA_CON16_RG_AUD23ADCCLKRATEHALF_POS)
#define AUDENC_ANA_CON16_RG_AUD23IO_VOWCLK_EN_POS    (2)
#define AUDENC_ANA_CON16_RG_AUD23IO_VOWCLK_EN_MASK   (1<<AUDENC_ANA_CON16_RG_AUD23IO_VOWCLK_EN_POS)
#define AUDENC_ANA_CON16_RG_CM23_REFGENSEL_POS       (3)
#define AUDENC_ANA_CON16_RG_CM23_REFGENSEL_MASK      (1<<AUDENC_ANA_CON16_RG_CM23_REFGENSEL_POS)
#define AUDENC_ANA_CON16_RG_AUD23PREAMPAAFEN_POS     (4)
#define AUDENC_ANA_CON16_RG_AUD23PREAMPAAFEN_MASK    (1<<AUDENC_ANA_CON16_RG_AUD23PREAMPAAFEN_POS)
#define AUDENC_ANA_CON16_RG_DCC23VCMBUFLPMODSEL_POS  (5)
#define AUDENC_ANA_CON16_RG_DCC23VCMBUFLPMODSEL_MASK (1<<AUDENC_ANA_CON16_RG_DCC23VCMBUFLPMODSEL_POS)
#define AUDENC_ANA_CON16_RG_DCC23VCMBUFLPSWEN_POS    (6)
#define AUDENC_ANA_CON16_RG_DCC23VCMBUFLPSWEN_MASK   (1<<AUDENC_ANA_CON16_RG_DCC23VCMBUFLPSWEN_POS)
#define AUDENC_ANA_CON16_RG_CM23STBENH_POS           (7)
#define AUDENC_ANA_CON16_RG_CM23STBENH_MASK          (1<<AUDENC_ANA_CON16_RG_CM23STBENH_POS)
#define AUDENC_ANA_CON16_RG_AUD23SPAREVA25_POS       (8)
#define AUDENC_ANA_CON16_RG_AUD23SPAREVA25_MASK      (0xF<<AUDENC_ANA_CON16_RG_AUD23SPAREVA25_POS)
#define AUDENC_ANA_CON16_RG_AUD23SPAREVA25_LDO_TO_AUD23_L_POS       (8)
#define AUDENC_ANA_CON16_RG_AUD23SPAREVA25_LDO_TO_AUD23_L_MASK      (1<<AUDENC_ANA_CON16_RG_AUD23SPAREVA25_LDO_TO_AUD23_L_POS)
#define AUDENC_ANA_CON16_RG_AUD23SPAREVA25_LDO_TO_AUD23_R_POS       (9)
#define AUDENC_ANA_CON16_RG_AUD23SPAREVA25_LDO_TO_AUD23_R_MASK      (1<<AUDENC_ANA_CON16_RG_AUD23SPAREVA25_LDO_TO_AUD23_R_POS)


#define AUDENC_ANA_CON16_RG_AUD23SPAREVA12_POS       (12)
#define AUDENC_ANA_CON16_RG_AUD23SPAREVA12_MASK      (0xF<<AUDENC_ANA_CON16_RG_AUD23SPAREVA12_POS)
#define AUDENC_ANA_CON18                (ABB_BASE + 0x0248)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLON_POS            (0)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLON_MASK           (1<<AUDENC_ANA_CON18_RG_AUD45PREAMPLON_POS)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLDCCEN_POS         (1)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLDCCEN_MASK        (1<<AUDENC_ANA_CON18_RG_AUD45PREAMPLDCCEN_POS)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLDCPRECHARGE_POS   (2)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLDCPRECHARGE_MASK  (1<<AUDENC_ANA_CON18_RG_AUD45PREAMPLDCPRECHARGE_POS)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLPGATEST_POS       (3)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLPGATEST_MASK      (1<<AUDENC_ANA_CON18_RG_AUD45PREAMPLPGATEST_POS)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLVSCALE_POS        (4)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLVSCALE_MASK       (3<<AUDENC_ANA_CON18_RG_AUD45PREAMPLVSCALE_POS)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLINPUTSEL_POS      (6)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLINPUTSEL_MASK     (3<<AUDENC_ANA_CON18_RG_AUD45PREAMPLINPUTSEL_POS)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLGAIN_POS          (8)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPLGAIN_MASK         (0xF<<AUDENC_ANA_CON18_RG_AUD45PREAMPLGAIN_POS)
#define AUDENC_ANA_CON18_RG_AUD45ADCLPWRUP_POS            (12)
#define AUDENC_ANA_CON18_RG_AUD45ADCLPWRUP_MASK           (1<<AUDENC_ANA_CON18_RG_AUD45ADCLPWRUP_POS)
#define AUDENC_ANA_CON18_RG_AUD45ADCLINPUTSEL_POS         (13)
#define AUDENC_ANA_CON18_RG_AUD45ADCLINPUTSEL_MASK        (3<<AUDENC_ANA_CON18_RG_AUD45ADCLINPUTSEL_POS)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPL_ACC20K_EN_POS    (15)
#define AUDENC_ANA_CON18_RG_AUD45PREAMPL_ACC20K_EN_MASK   (1<<AUDENC_ANA_CON18_RG_AUD45PREAMPL_ACC20K_EN_POS)
#define AUDENC_ANA_CON19                (ABB_BASE + 0x024C)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRON_POS             (0)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRON_MASK            (1<<AUDENC_ANA_CON19_RG_AUD45PREAMPRON_POS)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRDCCEN_POS          (1)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRDCCEN_MASK         (1<<AUDENC_ANA_CON19_RG_AUD45PREAMPRDCCEN_POS)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRDCPRECHARGE_POS    (2)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRDCPRECHARGE_MASK   (1<<AUDENC_ANA_CON19_RG_AUD45PREAMPRDCPRECHARGE_POS)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRPGATEST_POS        (3)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRPGATEST_MASK       (1<<AUDENC_ANA_CON19_RG_AUD45PREAMPRPGATEST_POS)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRVSCALE_POS         (4)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRVSCALE_MASK        (3<<AUDENC_ANA_CON19_RG_AUD45PREAMPRVSCALE_POS)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRINPUTSEL_POS       (6)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRINPUTSEL_MASK      (3<<AUDENC_ANA_CON19_RG_AUD45PREAMPRINPUTSEL_POS)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRGAIN_POS           (8)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPRGAIN_MASK          (0xF<<AUDENC_ANA_CON19_RG_AUD45PREAMPRGAIN_POS)
#define AUDENC_ANA_CON19_RG_AUD45ADCRPWRUP_POS             (12)
#define AUDENC_ANA_CON19_RG_AUD45ADCRPWRUP_MASK            (1<<AUDENC_ANA_CON19_RG_AUD45ADCRPWRUP_POS)
#define AUDENC_ANA_CON19_RG_AUD45ADCRINPUTSEL_POS          (13)
#define AUDENC_ANA_CON19_RG_AUD45ADCRINPUTSEL_MASK         (3<<AUDENC_ANA_CON19_RG_AUD45ADCRINPUTSEL_POS)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPR_ACC20K_EN_POS     (15)
#define AUDENC_ANA_CON19_RG_AUD45PREAMPR_ACC20K_EN_MASK    (1<<AUDENC_ANA_CON19_RG_AUD45PREAMPR_ACC20K_EN_POS)
#define AUDENC_ANA_CON20                (ABB_BASE + 0x0250)
#define AUDENC_ANA_CON20_RG_AUD45ULHALFBIAS_POS           (0)
#define AUDENC_ANA_CON20_RG_AUD45ULHALFBIAS_MASK          (1<<AUDENC_ANA_CON20_RG_AUD45ULHALFBIAS_POS)
#define AUDENC_ANA_CON20_RG_AUD45PREAMPLP1EN_POS          (1)
#define AUDENC_ANA_CON20_RG_AUD45PREAMPLP1EN_MASK         (1<<AUDENC_ANA_CON20_RG_AUD45PREAMPLP1EN_POS)
#define AUDENC_ANA_CON20_RG_AUD45PREAMPLP2EN_POS          (2)
#define AUDENC_ANA_CON20_RG_AUD45PREAMPLP2EN_MASK         (1<<AUDENC_ANA_CON20_RG_AUD45PREAMPLP2EN_POS)
#define AUDENC_ANA_CON20_RG_AUD45ADC1STSTAGELPEN_POS      (3)
#define AUDENC_ANA_CON20_RG_AUD45ADC1STSTAGELPEN_MASK     (1<<AUDENC_ANA_CON20_RG_AUD45ADC1STSTAGELPEN_POS)
#define AUDENC_ANA_CON20_RG_AUD45ADC2NDSTAGELPEN_POS      (4)
#define AUDENC_ANA_CON20_RG_AUD45ADC2NDSTAGELPEN_MASK     (1<<AUDENC_ANA_CON20_RG_AUD45ADC2NDSTAGELPEN_POS)
#define AUDENC_ANA_CON20_RG_AUD45ADCFLASHLPEN_POS         (5)
#define AUDENC_ANA_CON20_RG_AUD45ADCFLASHLPEN_MASK        (1<<AUDENC_ANA_CON20_RG_AUD45ADCFLASHLPEN_POS)
#define AUDENC_ANA_CON20_RG_AUD45PREAMPIDDTEST_POS        (6)
#define AUDENC_ANA_CON20_RG_AUD45PREAMPIDDTEST_MASK       (3<<AUDENC_ANA_CON20_RG_AUD45PREAMPIDDTEST_POS)
#define AUDENC_ANA_CON20_RG_AUD45ADC1STSTAGEIDDTEST_POS   (8)
#define AUDENC_ANA_CON20_RG_AUD45ADC1STSTAGEIDDTEST_MASK  (3<<AUDENC_ANA_CON20_RG_AUD45ADC1STSTAGEIDDTEST_POS)
#define AUDENC_ANA_CON20_RG_AUD45ADC2NDSTAGEIDDTEST_POS   (10)
#define AUDENC_ANA_CON20_RG_AUD45ADC2NDSTAGEIDDTEST_MASK  (3<<AUDENC_ANA_CON20_RG_AUD45ADC2NDSTAGEIDDTEST_POS)
#define AUDENC_ANA_CON20_RG_AUD45ADCREFBUFIDDTEST_POS     (12)
#define AUDENC_ANA_CON20_RG_AUD45ADCREFBUFIDDTEST_MASK    (3<<AUDENC_ANA_CON20_RG_AUD45ADCREFBUFIDDTEST_POS)
#define AUDENC_ANA_CON20_RG_AUD45ADCFLASHIDDTEST_POS      (14)
#define AUDENC_ANA_CON20_RG_AUD45ADCFLASHIDDTEST_MASK     (3<<AUDENC_ANA_CON20_RG_AUD45ADCFLASHIDDTEST_POS)
#define AUDENC_ANA_CON21                (ABB_BASE + 0x0254)
#define AUDENC_ANA_CON21_RG_AUD45ADCCLKSEL_POS             (0)
#define AUDENC_ANA_CON21_RG_AUD45ADCCLKSEL_MASK            (3<<AUDENC_ANA_CON21_RG_AUD45ADCCLKSEL_POS)
#define AUDENC_ANA_CON21_RG_AUD45ADCCLKSOURCE_POS          (2)
#define AUDENC_ANA_CON21_RG_AUD45ADCCLKSOURCE_MASK         (3<<AUDENC_ANA_CON21_RG_AUD45ADCCLKSOURCE_POS)
#define AUDENC_ANA_CON21_RG_AUD45ADC_13MCK_EN_POS          (4)
#define AUDENC_ANA_CON21_RG_AUD45ADC_13MCK_EN_MASK         (1<<AUDENC_ANA_CON21_RG_AUD45ADC_13MCK_EN_POS)
#define AUDENC_ANA_CON21_RG_AUD45ADCDAC0P25FS_POS          (5)
#define AUDENC_ANA_CON21_RG_AUD45ADCDAC0P25FS_MASK         (1<<AUDENC_ANA_CON21_RG_AUD45ADCDAC0P25FS_POS)
#define AUDENC_ANA_CON21_RG_AUD45PGAL_ACCFS_POS            (6)
#define AUDENC_ANA_CON21_RG_AUD45PGAL_ACCFS_MASK           (1<<AUDENC_ANA_CON21_RG_AUD45PGAL_ACCFS_POS)
#define AUDENC_ANA_CON21_RG_AUD45PGAR_ACCFS_POS            (7)
#define AUDENC_ANA_CON21_RG_AUD45PGAR_ACCFS_MASK           (1<<AUDENC_ANA_CON21_RG_AUD45PGAR_ACCFS_POS)
#define AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN1_POS        (8)
#define AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN1_MASK       (1<<AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN1_POS)
#define AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN2_POS        (9)
#define AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN2_MASK       (1<<AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN2_POS)
#define AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN_ORIGIN_POS  (10)
#define AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN_ORIGIN_MASK (1<<AUDENC_ANA_CON21_RG_AUD45PREAMP_LOWPEN_ORIGIN_POS)
#define AUDENC_ANA_CON24                (ABB_BASE + 0x0260)
#define AUDENC_ANA_CON24_RG_AUD45RCTUNEL_POS               (0)
#define AUDENC_ANA_CON24_RG_AUD45RCTUNEL_MASK              (0x1F<<AUDENC_ANA_CON24_RG_AUD45RCTUNEL_POS)
#define AUDENC_ANA_CON24_RG_AUD45RCTUNELSEL_POS            (5)
#define AUDENC_ANA_CON24_RG_AUD45RCTUNELSEL_MASK           (1<<AUDENC_ANA_CON24_RG_AUD45RCTUNELSEL_POS)
#define AUDENC_ANA_CON24_RG_AUD45RCTUNER_POS               (8)
#define AUDENC_ANA_CON24_RG_AUD45RCTUNER_MASK              (0x1F<<AUDENC_ANA_CON24_RG_AUD45RCTUNER_POS)
#define AUDENC_ANA_CON24_RG_AUD45RCTUNERSEL_POS            (13)
#define AUDENC_ANA_CON24_RG_AUD45RCTUNERSEL_MASK           (1<<AUDENC_ANA_CON24_RG_AUD45RCTUNERSEL_POS)
#define AUDENC_ANA_CON24_RG_AUD45ADCSYNCCLK_INV_POS        (14)
#define AUDENC_ANA_CON24_RG_AUD45ADCSYNCCLK_INV_MASK       (1<<AUDENC_ANA_CON24_RG_AUD45ADCSYNCCLK_INV_POS)
#define AUDENC_ANA_CON24_RG_AUD45ADCCLKRATEQUARTER_POS     (15)
#define AUDENC_ANA_CON24_RG_AUD45ADCCLKRATEQUARTER_MASK    (1<<AUDENC_ANA_CON24_RG_AUD45ADCCLKRATEQUARTER_POS)
#define AUDENC_ANA_CON25                (ABB_BASE + 0x0264)
#define AUDENC_ANA_CON25_RG_AUD45ADCCLKHALFRST_POS    (0)
#define AUDENC_ANA_CON25_RG_AUD45ADCCLKHALFRST_MASK   (1<<AUDENC_ANA_CON25_RG_AUD45ADCCLKHALFRST_POS)
#define AUDENC_ANA_CON25_RG_AUD45ADCCLKRATEHALF_POS   (1)
#define AUDENC_ANA_CON25_RG_AUD45ADCCLKRATEHALF_MASK  (1<<AUDENC_ANA_CON25_RG_AUD45ADCCLKRATEHALF_POS)
#define AUDENC_ANA_CON25_RG_AUD45IO_VOWCLK_EN_POS     (2)
#define AUDENC_ANA_CON25_RG_AUD45IO_VOWCLK_EN_MASK    (1<<AUDENC_ANA_CON25_RG_AUD45IO_VOWCLK_EN_POS)
#define AUDENC_ANA_CON25_RG_CM45_REFGENSEL_POS        (3)
#define AUDENC_ANA_CON25_RG_CM45_REFGENSEL_MASK       (1<<AUDENC_ANA_CON25_RG_CM45_REFGENSEL_POS)
#define AUDENC_ANA_CON25_RG_AUD45PREAMPAAFEN_POS      (4)
#define AUDENC_ANA_CON25_RG_AUD45PREAMPAAFEN_MASK     (1<<AUDENC_ANA_CON25_RG_AUD45PREAMPAAFEN_POS)
#define AUDENC_ANA_CON25_RG_DCC45VCMBUFLPMODSEL_POS   (5)
#define AUDENC_ANA_CON25_RG_DCC45VCMBUFLPMODSEL_MASK  (1<<AUDENC_ANA_CON25_RG_DCC45VCMBUFLPMODSEL_POS)
#define AUDENC_ANA_CON25_RG_DCC45VCMBUFLPSWEN_POS     (6)
#define AUDENC_ANA_CON25_RG_DCC45VCMBUFLPSWEN_MASK    (1<<AUDENC_ANA_CON25_RG_DCC45VCMBUFLPSWEN_POS)
#define AUDENC_ANA_CON25_RG_CM45STBENH_POS            (7)
#define AUDENC_ANA_CON25_RG_CM45STBENH_MASK           (1<<AUDENC_ANA_CON25_RG_CM45STBENH_POS)
#define AUDENC_ANA_CON25_RG_AUD45SPAREVA25_POS        (8)
#define AUDENC_ANA_CON25_RG_AUD45SPAREVA25_MASK       (0xF<<AUDENC_ANA_CON25_RG_AUD45SPAREVA25_POS)
#define AUDENC_ANA_CON25_RG_AUD45SPAREVA25_LDO_TO_AUD45_L_POS        (8)
#define AUDENC_ANA_CON25_RG_AUD45SPAREVA25_LDO_TO_AUD45_L_MASK       (1<<AUDENC_ANA_CON25_RG_AUD45SPAREVA25_LDO_TO_AUD45_L_POS)
#define AUDENC_ANA_CON25_RG_AUD45SPAREVA25_LDO_TO_AUD45_R_POS        (9)
#define AUDENC_ANA_CON25_RG_AUD45SPAREVA25_LDO_TO_AUD45_R_MASK       (1<<AUDENC_ANA_CON25_RG_AUD45SPAREVA25_LDO_TO_AUD45_R_POS)
#define AUDENC_ANA_CON25_RG_AUD45SPAREVA12_POS        (12)
#define AUDENC_ANA_CON25_RG_AUD45SPAREVA12_MASK       (0xF<<AUDENC_ANA_CON25_RG_AUD45SPAREVA12_POS)
#define AUDENC_ANA_CON27                (ABB_BASE + 0x026C)
#define AUDENC_ANA_CON27_LCLDO_ENC_EN_POS           (0)
#define AUDENC_ANA_CON27_LCLDO_ENC_EN_MASK          (1<<AUDENC_ANA_CON27_LCLDO_ENC_EN_POS)
#define AUDENC_ANA_CON27_DISCHARGE_PDN_POS          (1)
#define AUDENC_ANA_CON27_DISCHARGE_PDN_MASK         (1<<AUDENC_ANA_CON27_DISCHARGE_PDN_POS)
#define AUDENC_ANA_CON27_TBST_EN_POS                (2)
#define AUDENC_ANA_CON27_TBST_EN_MASK               (1<<AUDENC_ANA_CON27_TBST_EN_POS)
#define AUDENC_ANA_CON27_REMOTE_SENSE_EN_POS        (3)
#define AUDENC_ANA_CON27_REMOTE_SENSE_EN_MASK       (1<<AUDENC_ANA_CON27_REMOTE_SENSE_EN_POS)
#define AUDENC_ANA_CON27_PDD_TEST_POS               (4)
#define AUDENC_ANA_CON27_PDD_TEST_MASK              (1<<AUDENC_ANA_CON27_PDD_TEST_POS)
#define AUDENC_ANA_CON28                (ABB_BASE + 0x0270)
#define AUDENC_ANA_CON28_DMIC0_EN_POS               (0)
#define AUDENC_ANA_CON28_DMIC0_EN_MASK              (1<<AUDENC_ANA_CON28_DMIC0_EN_POS)
#define AUDENC_ANA_CON28_CLOCK_SOURCE_POS           (3)
#define AUDENC_ANA_CON28_CLOCK_SOURCE_MASK          (1<<AUDENC_ANA_CON28_CLOCK_SOURCE_POS)
#define AUDENC_ANA_CON28_POS_DUTY_POS               (4)
#define AUDENC_ANA_CON28_POS_DUTY_MASK              (3<<AUDENC_ANA_CON28_POS_DUTY_POS)
#define AUDENC_ANA_CON28_NEG_DUTY_POS               (6)
#define AUDENC_ANA_CON28_NEG_DUTY_MASK              (3<<AUDENC_ANA_CON28_NEG_DUTY_POS)
#define AUDENC_ANA_CON28_DMIC0_DATA_EN_POS          (13)
#define AUDENC_ANA_CON28_DMIC0_DATA_EN_MASK         (1<<AUDENC_ANA_CON28_DMIC0_DATA_EN_POS)
#define AUDENC_ANA_CON29                (ABB_BASE + 0x0274)
#define AUDENC_ANA_CON29_DMIC1_EN_POS               (0)
#define AUDENC_ANA_CON29_DMIC1_EN_MASK              (1<<AUDENC_ANA_CON29_DMIC1_EN_POS)
#define AUDENC_ANA_CON29_CLOCK_SOURCE_POS           (3)
#define AUDENC_ANA_CON29_CLOCK_SOURCE_MASK          (1<<AUDENC_ANA_CON29_CLOCK_SOURCE_POS)
#define AUDENC_ANA_CON29_POS_DUTY_POS               (4)
#define AUDENC_ANA_CON29_POS_DUTY_MASK              (3<<AUDENC_ANA_CON29_POS_DUTY_POS)
#define AUDENC_ANA_CON29_NEG_DUTY_POS               (6)
#define AUDENC_ANA_CON29_NEG_DUTY_MASK              (3<<AUDENC_ANA_CON29_NEG_DUTY_POS)
#define AUDENC_ANA_CON29_DMIC1_DATA_EN_POS          (13)
#define AUDENC_ANA_CON29_DMIC1_DATA_EN_MASK         (1<<AUDENC_ANA_CON29_DMIC1_DATA_EN_POS)
#define AUDENC_ANA_CON30                (ABB_BASE + 0x0278)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2EN_POS        (0)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2EN_MASK       (1<<AUDENC_ANA_CON30_RG_AUDDIGMIC2EN_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMICBIAS2_POS      (1)
#define AUDENC_ANA_CON30_RG_AUDDIGMICBIAS2_MASK     (3<<AUDENC_ANA_CON30_RG_AUDDIGMICBIAS2_POS)
#define AUDENC_ANA_CON30_RG_DMIC2HPCLKEN_POS        (3)
#define AUDENC_ANA_CON30_RG_DMIC2HPCLKEN_MASK       (1<<AUDENC_ANA_CON30_RG_DMIC2HPCLKEN_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2PDUTY_POS     (4)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2PDUTY_MASK    (3<<AUDENC_ANA_CON30_RG_AUDDIGMIC2PDUTY_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2NDUTY_POS     (6)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2NDUTY_MASK    (3<<AUDENC_ANA_CON30_RG_AUDDIGMIC2NDUTY_POS)
#define AUDENC_ANA_CON30_RG_DMIC2MONEN_POS          (8)
#define AUDENC_ANA_CON30_RG_DMIC2MONEN_MASK         (1<<AUDENC_ANA_CON30_RG_DMIC2MONEN_POS)
#define AUDENC_ANA_CON30_RG_DMIC2MONSEL_POS         (9)
#define AUDENC_ANA_CON30_RG_DMIC2MONSEL_MASK        (7<<AUDENC_ANA_CON30_RG_DMIC2MONSEL_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2CLK_SHARE_EN_POS (12)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2CLK_SHARE_EN_MASK (1<<AUDENC_ANA_CON30_RG_AUDDIGMIC2CLK_SHARE_EN_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2DATA_EN_POS (13)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2DATA_EN_MASK (1<<AUDENC_ANA_CON30_RG_AUDDIGMIC2DATA_EN_POS)
#define AUDENC_ANA_CON30_RG_AUDSPAREVMIC2_POS  (14)
#define AUDENC_ANA_CON30_RG_AUDSPAREVMIC2_MASK (3<<AUDENC_ANA_CON30_RG_AUDSPAREVMIC2_POS)
#define AUDENC_ANA_CON31                (ABB_BASE + 0x027C)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3EN_POS (0)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3EN_MASK (1<<AUDENC_ANA_CON31_RG_AUDDIGMIC3EN_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMICBIAS3_POS (1)
#define AUDENC_ANA_CON31_RG_AUDDIGMICBIAS3_MASK (3<<AUDENC_ANA_CON31_RG_AUDDIGMICBIAS3_POS)
#define AUDENC_ANA_CON31_RG_DMIC3HPCLKEN_POS (3)
#define AUDENC_ANA_CON31_RG_DMIC3HPCLKEN_MASK (1<<AUDENC_ANA_CON31_RG_DMIC3HPCLKEN_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3PDUTY_POS (4)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3PDUTY_MASK (3<<AUDENC_ANA_CON31_RG_AUDDIGMIC3PDUTY_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3NDUTY_POS (6)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3NDUTY_MASK (3<<AUDENC_ANA_CON31_RG_AUDDIGMIC3NDUTY_POS)
#define AUDENC_ANA_CON31_RG_DMIC3MONEN_POS (8)
#define AUDENC_ANA_CON31_RG_DMIC3MONEN_MASK (1<<AUDENC_ANA_CON31_RG_DMIC3MONEN_POS)
#define AUDENC_ANA_CON31_RG_DMIC3MONSEL_POS (9)
#define AUDENC_ANA_CON31_RG_DMIC3MONSEL_MASK (7<<AUDENC_ANA_CON31_RG_DMIC3MONSEL_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3CLK_SHARE_EN_POS (12)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3CLK_SHARE_EN_MASK (1<<AUDENC_ANA_CON31_RG_AUDDIGMIC3CLK_SHARE_EN_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3DATA_EN_POS (13)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3DATA_EN_MASK (1<<AUDENC_ANA_CON31_RG_AUDDIGMIC3DATA_EN_POS)
#define AUDENC_ANA_CON31_RG_AUDSPAREVMIC3_POS (14)
#define AUDENC_ANA_CON31_RG_AUDSPAREVMIC3_MASK (3<<AUDENC_ANA_CON31_RG_AUDSPAREVMIC3_POS)
#define AUDENC_ANA_CON32                (ABB_BASE + 0x0280)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4EN_POS (0)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4EN_MASK (1<<AUDENC_ANA_CON32_RG_AUDDIGMIC4EN_POS)
#define AUDENC_ANA_CON32_RG_AUDDIGMICBIAS4_POS (1)
#define AUDENC_ANA_CON32_RG_AUDDIGMICBIAS4_MASK (3<<AUDENC_ANA_CON32_RG_AUDDIGMICBIAS4_POS)
#define AUDENC_ANA_CON32_RG_DMIC4HPCLKEN_POS (3)
#define AUDENC_ANA_CON32_RG_DMIC4HPCLKEN_MASK (1<<AUDENC_ANA_CON32_RG_DMIC4HPCLKEN_POS)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4PDUTY_POS (4)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4PDUTY_MASK (3<<AUDENC_ANA_CON32_RG_AUDDIGMIC4PDUTY_POS)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4NDUTY_POS (6)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4NDUTY_MASK (3<<AUDENC_ANA_CON32_RG_AUDDIGMIC4NDUTY_POS)
#define AUDENC_ANA_CON32_RG_DMIC4MONEN_POS (8)
#define AUDENC_ANA_CON32_RG_DMIC4MONEN_MASK (1<<AUDENC_ANA_CON32_RG_DMIC4MONEN_POS)
#define AUDENC_ANA_CON32_RG_DMIC4MONSEL_POS (9)
#define AUDENC_ANA_CON32_RG_DMIC4MONSEL_MASK (7<<AUDENC_ANA_CON32_RG_DMIC4MONSEL_POS)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4CLK_SHARE_EN_POS (12)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4CLK_SHARE_EN_MASK (1<<AUDENC_ANA_CON32_RG_AUDDIGMIC4CLK_SHARE_EN_POS)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4DATA_EN_POS (13)
#define AUDENC_ANA_CON32_RG_AUDDIGMIC4DATA_EN_MASK (1<<AUDENC_ANA_CON32_RG_AUDDIGMIC4DATA_EN_POS)
#define AUDENC_ANA_CON32_RG_AUDSPAREVMIC4_POS (14)
#define AUDENC_ANA_CON32_RG_AUDSPAREVMIC4_MASK (3<<AUDENC_ANA_CON32_RG_AUDSPAREVMIC4_POS)
#define AUDENC_ANA_CON33                (ABB_BASE + 0x0284)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5EN_POS (0)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5EN_MASK (1<<AUDENC_ANA_CON33_RG_AUDDIGMIC5EN_POS)
#define AUDENC_ANA_CON33_RG_AUDDIGMICBIAS5_POS (1)
#define AUDENC_ANA_CON33_RG_AUDDIGMICBIAS5_MASK (3<<AUDENC_ANA_CON33_RG_AUDDIGMICBIAS5_POS)
#define AUDENC_ANA_CON33_RG_DMIC5HPCLKEN_POS (3)
#define AUDENC_ANA_CON33_RG_DMIC5HPCLKEN_MASK (1<<AUDENC_ANA_CON33_RG_DMIC5HPCLKEN_POS)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5PDUTY_POS (4)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5PDUTY_MASK (3<<AUDENC_ANA_CON33_RG_AUDDIGMIC5PDUTY_POS)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5NDUTY_POS (6)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5NDUTY_MASK (3<<AUDENC_ANA_CON33_RG_AUDDIGMIC5NDUTY_POS)
#define AUDENC_ANA_CON33_RG_DMIC5MONEN_POS (8)
#define AUDENC_ANA_CON33_RG_DMIC5MONEN_MASK (1<<AUDENC_ANA_CON33_RG_DMIC5MONEN_POS)
#define AUDENC_ANA_CON33_RG_DMIC5MONSEL_POS (9)
#define AUDENC_ANA_CON33_RG_DMIC5MONSEL_MASK (7<<AUDENC_ANA_CON33_RG_DMIC5MONSEL_POS)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5CLK_SHARE_EN_POS (12)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5CLK_SHARE_EN_MASK (1<<AUDENC_ANA_CON33_RG_AUDDIGMIC5CLK_SHARE_EN_POS)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5DATA_EN_POS (13)
#define AUDENC_ANA_CON33_RG_AUDDIGMIC5DATA_EN_MASK (3<<AUDENC_ANA_CON33_RG_AUDDIGMIC5DATA_EN_POS)
#define AUDENC_ANA_CON33_RG_AUDSPAREVMIC5_POS (14)
#define AUDENC_ANA_CON33_RG_AUDSPAREVMIC5_MASK (3<<AUDENC_ANA_CON33_RG_AUDSPAREVMIC5_POS)
#define AUDENC_ANA_CON34                (ABB_BASE + 0x0288)
#define AUDENC_ANA_CON34_BIAS0_EN_POS               (0)
#define AUDENC_ANA_CON34_BIAS0_EN_MASK              (1<<AUDENC_ANA_CON34_BIAS0_EN_POS)
#define AUDENC_ANA_CON34_BIAS0_BYPASS_POS           (1)
#define AUDENC_ANA_CON34_BIAS0_BYPASS_MASK          (1<<AUDENC_ANA_CON34_BIAS0_BYPASS_POS)
#define AUDENC_ANA_CON34_BIAS0_LOWPOWER_EN_POS      (2)
#define AUDENC_ANA_CON34_BIAS0_LOWPOWER_EN_MASK     (1<<AUDENC_ANA_CON34_BIAS0_LOWPOWER_EN_POS)
#define AUDENC_ANA_CON34_BIAS0_DRIVER_EN_POS        (3)
#define AUDENC_ANA_CON34_BIAS0_DRIVER_EN_MASK       (1<<AUDENC_ANA_CON34_BIAS0_DRIVER_EN_POS)
#define AUDENC_ANA_CON34_BIAS0_VOLTAGE_SEL_POS      (4)
#define AUDENC_ANA_CON34_BIAS0_VOLTAGE_SEL_MASK     (7<<AUDENC_ANA_CON34_BIAS0_VOLTAGE_SEL_POS)
#define AUDENC_ANA_CON34_BIAS0_PULLLOW_POS          (15)
#define AUDENC_ANA_CON34_BIAS0_PULLLOW_MASK         (1<<AUDENC_ANA_CON34_BIAS0_PULLLOW_POS)
#define AUDENC_ANA_CON35                (ABB_BASE + 0x028C)
#define AUDENC_ANA_CON35_BIAS1_EN_POS               (0)
#define AUDENC_ANA_CON35_BIAS1_EN_MASK              (1<<AUDENC_ANA_CON35_BIAS1_EN_POS)
#define AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VBYPASSEN_POS (1)
#define AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VBYPASSEN_MASK (1<<AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VBYPASSEN_POS)
#define AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VLOWPEN_POS (2)
#define AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VLOWPEN_MASK (1<<AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VLOWPEN_POS)
#define AUDENC_ANA_CON35_RG_AUDPWDBMICBIAS1_3VEN_POS (3)
#define AUDENC_ANA_CON35_RG_AUDPWDBMICBIAS1_3VEN_MASK (1<<AUDENC_ANA_CON35_RG_AUDPWDBMICBIAS1_3VEN_POS)
#define AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VVREF_POS (4)
#define AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VVREF_MASK (7<<AUDENC_ANA_CON35_RG_AUDMICBIAS1_3VVREF_POS)
#define AUDENC_ANA_CON35_BIAS1_PULLLOW_POS          (15)
#define AUDENC_ANA_CON35_BIAS1_PULLLOW_MASK         (1<<AUDENC_ANA_CON35_BIAS1_PULLLOW_POS)
#define AUDENC_ANA_CON36                (ABB_BASE + 0x0290)
#define AUDENC_ANA_CON36_BIAS2_EN_POS               (0)
#define AUDENC_ANA_CON36_BIAS2_EN_MASK              (1<<AUDENC_ANA_CON36_BIAS2_EN_POS)
#define AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VBYPASSEN_POS (1)
#define AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VBYPASSEN_MASK (1<<AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VBYPASSEN_POS)
#define AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VLOWPEN_POS (2)
#define AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VLOWPEN_MASK (1<<AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VLOWPEN_POS)
#define AUDENC_ANA_CON36_RG_AUDPWDBMICBIAS2_3VEN_POS (3)
#define AUDENC_ANA_CON36_RG_AUDPWDBMICBIAS2_3VEN_MASK (1<<AUDENC_ANA_CON36_RG_AUDPWDBMICBIAS2_3VEN_POS)
#define AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VVREF_POS  (4)
#define AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VVREF_MASK (7<<AUDENC_ANA_CON36_RG_AUDMICBIAS2_3VVREF_POS)
#define AUDENC_ANA_CON36_BIAS2_PULLLOW_POS          (15)
#define AUDENC_ANA_CON36_BIAS2_PULLLOW_MASK         (1<<AUDENC_ANA_CON36_BIAS2_PULLLOW_POS)
//only for ab1568
#define AUDENC_ANA_CON37                (ABB_BASE + 0x0294)
#define AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_POS            (0)
#define AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_MASK           (1<<AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_POS)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VBYPASSEN_POS     (1)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VBYPASSEN_MASK    (1<<AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VBYPASSEN_POS)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VLOWPEN_POS       (2)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VLOWPEN_MASK      (1<<AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VBYPASSEN_POS)
#define AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_3VEN_POS       (3)
#define AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_3VEN_MASK      (1<<AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_3VEN_POS)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VVREF_POS         (4)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VVREF_MASK        (7<<AUDENC_ANA_CON37_RG_AUDMICBIAS3_3VVREF_POS)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VBYPASSEN_POS     (8)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VBYPASSEN_MASK    (1<<AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VBYPASSEN_POS)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VLOWPEN_POS       (9)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VLOWPEN_MASK      (1<<AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VLOWPEN_POS)
#define AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_2VEN_POS       (10)
#define AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_2VEN_MASK      (1<<AUDENC_ANA_CON37_RG_AUDPWDBMICBIAS3_2VEN_POS)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VVREF_POS         (11)
#define AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VVREF_MASK        (1<<AUDENC_ANA_CON37_RG_AUDMICBIAS3_2VVREF_POS)
#define AUDENC_ANA_CON37_RG_AUDACCDETMICBIAS3_PULLLOW_POS  (15)
#define AUDENC_ANA_CON37_RG_AUDACCDETMICBIAS3_PULLLOW_MASK (1<<AUDENC_ANA_CON37_RG_AUDACCDETMICBIAS3_PULLLOW_POS)
#define AUDENC_ANA_CON38                (ABB_BASE + 0x0298)
#define AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_POS            (0)
#define AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_MASK           (1<<AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_POS)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VBYPASSEN_POS     (1)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VBYPASSEN_MASK    (1<<AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VBYPASSEN_POS)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VLOWPEN_POS       (2)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VLOWPEN_MASK      (1<<AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VLOWPEN_POS)
#define AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_3VEN_POS       (3)
#define AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_3VEN_MASK      (1<<AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_3VEN_POS)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VVREF_POS         (4)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VVREF_MASK        (7<<AUDENC_ANA_CON38_RG_AUDMICBIAS4_3VVREF_POS)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VBYPASSEN_POS     (8)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VBYPASSEN_MASK    (1<<AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VBYPASSEN_POS)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VLOWPEN_POS       (9)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VLOWPEN_MASK      (1<<AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VLOWPEN_POS)
#define AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_2VEN_POS       (10)
#define AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_2VEN_MASK      (1<<AUDENC_ANA_CON38_RG_AUDPWDBMICBIAS4_2VEN_POS)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VVREF_POS         (11)
#define AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VVREF_MASK        (1<<AUDENC_ANA_CON38_RG_AUDMICBIAS4_2VVREF_POS)
#define AUDENC_ANA_CON38_RG_AUDACCDETMICBIAS4_PULLLOW_POS  (15)
#define AUDENC_ANA_CON38_RG_AUDACCDETMICBIAS4_PULLLOW_MASK (1<<AUDENC_ANA_CON38_RG_AUDACCDETMICBIAS4_PULLLOW_POS)

#define AUDENC_ANA_CON39                (ABB_BASE + 0x029C)
#define AUDENC_ANA_CON39_BIAS0_DCC_P1_EN_POS        (1)
#define AUDENC_ANA_CON39_BIAS0_DCC_P1_EN_MASK       (1<<AUDENC_ANA_CON39_BIAS0_DCC_P1_EN_POS)
#define AUDENC_ANA_CON39_BIAS0_DCC_P2_EN_POS        (2)
#define AUDENC_ANA_CON39_BIAS0_DCC_P2_EN_MASK       (1<<AUDENC_ANA_CON39_BIAS0_DCC_P2_EN_POS)
#define AUDENC_ANA_CON39_BIAS0_DCC_N_EN_POS         (3)
#define AUDENC_ANA_CON39_BIAS0_DCC_N_EN_MASK        (1<<AUDENC_ANA_CON39_BIAS0_DCC_N_EN_POS)
#define AUDENC_ANA_CON39_BIAS1_DCC_P1_EN_POS        (5)
#define AUDENC_ANA_CON39_BIAS1_DCC_P1_EN_MASK       (1<<AUDENC_ANA_CON39_BIAS1_DCC_P1_EN_POS)
#define AUDENC_ANA_CON39_BIAS1_DCC_P2_EN_POS        (6)
#define AUDENC_ANA_CON39_BIAS1_DCC_P2_EN_MASK       (1<<AUDENC_ANA_CON39_BIAS1_DCC_P2_EN_POS)
#define AUDENC_ANA_CON39_BIAS1_DCC_N_EN_POS         (7)
#define AUDENC_ANA_CON39_BIAS1_DCC_N_EN_MASK        (1<<AUDENC_ANA_CON39_BIAS1_DCC_N_EN_POS)
#define AUDENC_ANA_CON39_BIAS2_DCC_P1_EN_POS        (9)
#define AUDENC_ANA_CON39_BIAS2_DCC_P1_EN_MASK       (1<<AUDENC_ANA_CON39_BIAS0_DCC_P1_EN_POS)
#define AUDENC_ANA_CON39_BIAS2_DCC_P2_EN_POS        (10)
#define AUDENC_ANA_CON39_BIAS2_DCC_P2_EN_MASK       (1<<AUDENC_ANA_CON39_BIAS0_DCC_P2_EN_POS)
#define AUDENC_ANA_CON39_BIAS2_DCC_N_EN_POS         (11)
#define AUDENC_ANA_CON39_BIAS2_DCC_N_EN_MASK        (1<<AUDENC_ANA_CON39_BIAS0_DCC_N_EN_POS)
#define AUDENC_ANA_CON39_BIAS3_DCC_P1_EN_POS        (13)
#define AUDENC_ANA_CON39_BIAS3_DCC_P1_EN_MASK       (1<<AUDENC_ANA_CON39_BIAS1_DCC_P1_EN_POS)
#define AUDENC_ANA_CON39_BIAS3_DCC_P2_EN_POS        (14)
#define AUDENC_ANA_CON39_BIAS3_DCC_P2_EN_MASK       (1<<AUDENC_ANA_CON39_BIAS1_DCC_P2_EN_POS)
#define AUDENC_ANA_CON39_BIAS3_DCC_N_EN_POS         (15)
#define AUDENC_ANA_CON39_BIAS3_DCC_N_EN_MASK        (1<<AUDENC_ANA_CON39_BIAS1_DCC_N_EN_POS)

#define AUDENC_ANA_CON40                (ABB_BASE + 0x02A0)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0P1EN_POS  (1)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0P1EN_MASK (1<<AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0P1EN_POS)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0P2EN_POS  (2)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0P2EN_MASK (1<<AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0P2EN_POS)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0NEN_POS   (3)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0NEN_MASK  (1<<AUDENC_ANA_CON40_RG_AUDMICBIAS4_DCSW0NEN_POS)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0P1EN_POS  (5)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0P1EN_MASK (1<<AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0P1EN_POS)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0P2EN_POS  (6)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0P2EN_MASK (1<<AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0P2EN_POS)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0NEN_POS   (7)
#define AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0NEN_MASK  (1<<AUDENC_ANA_CON40_RG_AUDMICBIAS5_DCSW0NEN_POS)
#define AUDENC_ANA_CON40_BIAS1_EN_POS               (8)
#define AUDENC_ANA_CON40_BIAS1_EN_MASK              (1<<AUDENC_ANA_CON40_BIAS1_EN_POS)
#define AUDENC_ANA_CON40_BIAS2_EN_POS               (9)
#define AUDENC_ANA_CON40_BIAS2_EN_MASK              (1<<AUDENC_ANA_CON40_BIAS2_EN_POS)
#define AUDENC_ANA_CON40_DMIC_BIAS_EN_POS           (10)
#define AUDENC_ANA_CON40_DMIC_BIAS_EN_MASK          (1<<AUDENC_ANA_CON40_DMIC_BIAS_EN_POS)
#define AUDENC_ANA_CON40_BIAS_REV_POS               (12)
#define AUDENC_ANA_CON40_BIAS_REV_MASK              (0xF<<AUDENC_ANA_CON40_BIAS_REV_POS)
#define AUDENC_ANA_CON40_BIAS_REV_SW0_POS           (13)
#define AUDENC_ANA_CON40_BIAS_REV_SW0_MASK          (1<<AUDENC_ANA_CON40_BIAS_REV_SW0_POS)
#define AUDENC_ANA_CON40_BIAS_REV_SW0_1_POS         (14)
#define AUDENC_ANA_CON40_BIAS_REV_SW0_1_MASK        (1<<AUDENC_ANA_CON40_BIAS_REV_SW0_1_POS)
#define AUDENC_ANA_CON40_BIAS_REV_SW0_2_POS         (15)
#define AUDENC_ANA_CON40_BIAS_REV_SW0_2_MASK        (1<<AUDENC_ANA_CON40_BIAS_REV_SW0_2_POS)
#define AUDENC_ANA_CON40_BIAS0_PARALLEL_POS         (13)
#define AUDENC_ANA_CON40_BIAS0_PARALLEL_MASK        (1<<AUDENC_ANA_CON40_BIAS0_PARALLEL_POS)
#define AUDENC_ANA_CON40_BIAS1_PARALLEL_POS         (14)
#define AUDENC_ANA_CON40_BIAS1_PARALLEL_MASK        (1<<AUDENC_ANA_CON40_BIAS1_PARALLEL_POS)
#define AUDENC_ANA_CON40_BIAS2_PARALLEL_POS         (15)
#define AUDENC_ANA_CON40_BIAS2_PARALLEL_MASK        (1<<AUDENC_ANA_CON40_BIAS2_PARALLEL_POS)

#define AUDENC_ANA_CON41                (ABB_BASE + 0x02A4)
#define AUDENC_ANA_CON42                (ABB_BASE + 0x02A8)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_POS      (0)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_MASK     (0xFF<<AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_POS)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW1_POS  (0)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW1_MASK (1<<AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW1_POS)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW2_POS  (1)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW2_MASK (1<<AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW2_POS)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW3_POS  (2)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW3_MASK (1<<AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW3_POS)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW4_POS  (3)
#define AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW4_MASK (1<<AUDENC_ANA_CON42_RG_AUDUL_MICBIAS_REV0_SW4_POS)



#define AUDENC_ANA_CON42_RG_AUDUL_DMIC_REV0_POS     (8)
#define AUDENC_ANA_CON42_RG_AUDUL_DMIC_REV0_MASK    (1<<AUDENC_ANA_CON42_RG_AUDUL_DMIC_REV0_POS)



#define AUDENC_ANA_CON43                (ABB_BASE + 0x02AC)
#define AUDENC_ANA_CON43_AUD01_RC_READ_POS      (0)
#define AUDENC_ANA_CON43_AUD01_RC_READ_MASK     (0x1F<<AUDENC_ANA_CON43_AUD01_RC_READ_POS)
#define VAD_ANA_CON0                    (ABB_BASE + 0x0300)
#define VAD_ANA_CON0_EN_ATST_LV_POS             (5)
#define VAD_ANA_CON0_EN_ATST_LV_MASK            (1<<VAD_ANA_CON0_EN_ATST_LV_POS)
#define VAD_ANA_CON0_CIRCUIT_EN_POS             (7)
#define VAD_ANA_CON0_CIRCUIT_EN_MASK            (1<<VAD_ANA_CON0_CIRCUIT_EN_POS)
#define VAD_ANA_CON0_IBAS_DAC_LV_POS            (12)
#define VAD_ANA_CON0_IBAS_DAC_LV_MASK           (0xF<<VAD_ANA_CON0_IBAS_DAC_LV_POS)
#define VAD_ANA_CON1                    (ABB_BASE + 0x0304)
#define VAD_ANA_CON1_MIC_GAIN_LV_POS            (4)
#define VAD_ANA_CON1_MIC_GAIN_LV_MASK           (0x1F<<VAD_ANA_CON1_MIC_GAIN_LV_POS)

#define VAD_ANA_CON2                    (ABB_BASE + 0x0308)
#define VAD_ANA_CON3                    (ABB_BASE + 0x030C)
#define VAD_ANA_CON3_VAD_MIC_SEL_LV_POS         (0)
#define VAD_ANA_CON3_VAD_MIC_SEL_LV_MASK        (7<<VAD_ANA_CON3_VAD_MIC_SEL_LV_POS)
#define VAD_ANA_CON4                    (ABB_BASE + 0x0310)
#define VAD_ANA_CON5                    (ABB_BASE + 0x0314)
#define VAD_ANA_CON5_OP1_IDAC_LV_POS            (0)
#define VAD_ANA_CON5_OP1_IDAC_LV_MASK           (0xF<<VAD_ANA_CON5_OP1_IDAC_LV_POS)
#define VAD_ANA_CON5_OP2_IDAC_LV_POS            (5)
#define VAD_ANA_CON5_OP2_IDAC_LV_MASK           (0xF<<VAD_ANA_CON5_OP2_IDAC_LV_POS)
#define VAD_ANA_CON5_VIN0P_PRCH_POS             (10)
#define VAD_ANA_CON5_VIN0P_PRCH_MASK            (1<<VAD_ANA_CON5_VIN0P_PRCH_POS)
#define VAD_ANA_CON5_VIN0N_PRCH_POS             (11)
#define VAD_ANA_CON5_VIN0N_PRCH_MASK            (1<<VAD_ANA_CON5_VIN0N_PRCH_POS)
#define VAD_ANA_CON5_VIN1P_PRCH_POS             (12)
#define VAD_ANA_CON5_VIN1P_PRCH_MASK            (1<<VAD_ANA_CON5_VIN1P_PRCH_POS)
#define VAD_ANA_CON5_VIN1N_PRCH_POS             (13)
#define VAD_ANA_CON5_VIN1N_PRCH_MASK            (1<<VAD_ANA_CON5_VIN1N_PRCH_POS)
#define VAD_ANA_CON5_PRCH_MASK                  (0xF<<VAD_ANA_CON5_VIN0P_PRCH_POS)
#define AUXADC_ANA_CON0                 (ABB_BASE + 0x0400)
#endif
#endif

#define AUDDEC_ANA_CON0                 (ABB_BASE + 0x100)
#define AUDDEC_ANA_CON0_LCH_PWRUP_POS               (0)
#define AUDDEC_ANA_CON0_LCH_PWRUP_MASK              (1<<AUDDEC_ANA_CON0_LCH_PWRUP_POS)
#define AUDDEC_ANA_CON0_RCH_PWRUP_POS               (1)
#define AUDDEC_ANA_CON0_RCH_PWRUP_MASK              (1<<AUDDEC_ANA_CON0_RCH_PWRUP_POS)
#define AUDDEC_ANA_CON0_BIAS_L_EN_POS               (2)
#define AUDDEC_ANA_CON0_BIAS_L_EN_MASK              (1<<AUDDEC_ANA_CON0_BIAS_L_EN_POS)
#define AUDDEC_ANA_CON0_BIAS_R_EN_POS               (3)
#define AUDDEC_ANA_CON0_BIAS_R_EN_MASK              (1<<AUDDEC_ANA_CON0_BIAS_R_EN_POS)

#define AUDDEC_ANA_CON1                 (ABB_BASE + 0x104)
#define AUDDEC_ANA_CON1_HPL_OUT_PWRUP_POS           (0)
#define AUDDEC_ANA_CON1_HPL_OUT_PWRUP_MASK          (1<<AUDDEC_ANA_CON1_HPL_OUT_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPR_OUT_PWRUP_POS           (1)
#define AUDDEC_ANA_CON1_HPR_OUT_PWRUP_MASK          (1<<AUDDEC_ANA_CON1_HPR_OUT_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPL_OUT_AUX_PWRUP_POS       (2)
#define AUDDEC_ANA_CON1_HPL_OUT_AUX_PWRUP_MASK      (1<<AUDDEC_ANA_CON1_HPL_OUT_AUX_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPR_OUT_AUX_PWRUP_POS       (3)
#define AUDDEC_ANA_CON1_HPR_OUT_AUX_PWRUP_MASK      (1<<AUDDEC_ANA_CON1_HPR_OUT_AUX_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPL_PWRUP_POS               (4)
#define AUDDEC_ANA_CON1_HPL_PWRUP_MASK              (1<<AUDDEC_ANA_CON1_HPL_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPR_PWRUP_POS               (5)
#define AUDDEC_ANA_CON1_HPR_PWRUP_MASK              (1<<AUDDEC_ANA_CON1_HPR_PWRUP_POS)
#define AUDDEC_ANA_CON1_HPL_PWRUP_IBIAS_POS         (6)
#define AUDDEC_ANA_CON1_HPL_PWRUP_IBIAS_MASK        (1<<AUDDEC_ANA_CON1_HPL_PWRUP_IBIAS_POS)
#define AUDDEC_ANA_CON1_HPR_PWRUP_IBIAS_POS         (7)
#define AUDDEC_ANA_CON1_HPR_PWRUP_IBIAS_MASK        (1<<AUDDEC_ANA_CON1_HPR_PWRUP_IBIAS_POS)
#define AUDDEC_ANA_CON1_HPL_MUXINPUTSEL_POS         (8)
#define AUDDEC_ANA_CON1_HPL_MUXINPUTSEL_MASK        (3<<AUDDEC_ANA_CON1_HPL_MUXINPUTSEL_POS)
#define AUDDEC_ANA_CON1_HPR_MUXINPUTSEL_POS         (10)
#define AUDDEC_ANA_CON1_HPR_MUXINPUTSEL_MASK        (3<<AUDDEC_ANA_CON1_HPR_MUXINPUTSEL_POS)

#define AUDDEC_ANA_CON2                 (ABB_BASE + 0x108)
#define AUDDEC_ANA_CON2_HPL_OUT_STBENH_RST_POS      (0)
#define AUDDEC_ANA_CON2_HPL_OUT_STBENH_RST_MASK     (1<<AUDDEC_ANA_CON2_HPL_OUT_STBENH_RST_POS)
#define AUDDEC_ANA_CON2_HPL_OUT_STBENH_BUFSW_POS    (1)
#define AUDDEC_ANA_CON2_HPL_OUT_STBENH_BUFSW_MASK   (1<<AUDDEC_ANA_CON2_HPL_OUT_STBENH_BUFSW_POS)
#define AUDDEC_ANA_CON2_HPL_OUT_STBENH_REN_POS      (2)
#define AUDDEC_ANA_CON2_HPL_OUT_STBENH_REN_MASK     (1<<AUDDEC_ANA_CON2_HPL_OUT_STBENH_REN_POS)
#define AUDDEC_ANA_CON2_HPR_OUT_STBENH_RST_POS      (3)
#define AUDDEC_ANA_CON2_HPR_OUT_STBENH_RST_MASK     (1<<AUDDEC_ANA_CON2_HPR_OUT_STBENH_RST_POS)
#define AUDDEC_ANA_CON2_HPR_OUT_STBENH_BUFSW_POS    (4)
#define AUDDEC_ANA_CON2_HPR_OUT_STBENH_BUFSW_MASK   (1<<AUDDEC_ANA_CON2_HPR_OUT_STBENH_BUFSW_POS)
#define AUDDEC_ANA_CON2_HPR_OUT_STBENH_REN_POS      (5)
#define AUDDEC_ANA_CON2_HPR_OUT_STBENH_REN_MASK     (1<<AUDDEC_ANA_CON2_HPR_OUT_STBENH_REN_POS)
#define AUDDEC_ANA_CON2_AUD_HPSTARTUP_POS           (6)
#define AUDDEC_ANA_CON2_STARTUP_MODE_MASK           (1<<AUDDEC_ANA_CON2_AUD_HPSTARTUP_POS)
#define AUDDEC_ANA_CON2_HPL_AUXFBRSW_EN_POS         (7)
#define AUDDEC_ANA_CON2_HPL_AUXFBRSW_EN_MASK        (1<<AUDDEC_ANA_CON2_HPL_AUXFBRSW_EN_POS)
#define AUDDEC_ANA_CON2_HPR_AUXFBRSW_EN_POS         (8)
#define AUDDEC_ANA_CON2_HPR_AUXFBRSW_EN_MASK        (1<<AUDDEC_ANA_CON2_HPR_AUXFBRSW_EN_POS)
#define AUDDEC_ANA_CON2_HPL_SHORT_2HPLAUX_EN_POS    (10)
#define AUDDEC_ANA_CON2_HPL_SHORT_2HPLAUX_EN_MASK   (1<<AUDDEC_ANA_CON2_HPL_SHORT_2HPLAUX_EN_POS)
#define AUDDEC_ANA_CON2_HPR_SHORT_2HPLAUX_EN_POS    (11)
#define AUDDEC_ANA_CON2_HPR_SHORT_2HPLAUX_EN_MASK   (1<<AUDDEC_ANA_CON2_HPR_SHORT_2HPLAUX_EN_POS)
#define AUDDEC_ANA_CON2_HPL_OUT_AUXCM_EN_POS        (12)
#define AUDDEC_ANA_CON2_HPL_OUT_AUXCM_EN_MASK       (1<<AUDDEC_ANA_CON2_HPL_OUT_AUXCM_EN_POS)
#define AUDDEC_ANA_CON2_HPR_OUT_AUXCM_EN_POS        (13)
#define AUDDEC_ANA_CON2_HPR_OUT_AUXCM_EN_MASK       (1<<AUDDEC_ANA_CON2_HPR_OUT_AUXCM_EN_POS)
#define AUDDEC_ANA_CON2_HPL_OUTCM_EN_POS            (14)
#define AUDDEC_ANA_CON2_HPL_OUTCM_EN_MASK           (1<<AUDDEC_ANA_CON2_HPL_OUTCM_EN_POS)
#define AUDDEC_ANA_CON2_HPR_OUTCM_EN_POS            (15)
#define AUDDEC_ANA_CON2_HPR_OUTCM_EN_MASK           (1<<AUDDEC_ANA_CON2_HPR_OUTCM_EN_POS)

#define AUDDEC_ANA_CON3                 (ABB_BASE + 0x10C)
#define AUDDEC_ANA_CON3_HPL_AUX_GAIN_POS            (0)
#define AUDDEC_ANA_CON3_HPL_AUX_GAIN_MASK           (0xF<<AUDDEC_ANA_CON3_HPL_AUX_GAIN_POS)
#define AUDDEC_ANA_CON3_HPR_AUX_GAIN_POS            (4)
#define AUDDEC_ANA_CON3_HPR_AUX_GAIN_MASK           (0xF<<AUDDEC_ANA_CON3_HPR_AUX_GAIN_POS)
#define AUDDEC_ANA_CON3_HP_ESD_RES_EN_POS           (8)
#define AUDDEC_ANA_CON3_HP_ESD_RES_EN_MASK          (1<<AUDDEC_ANA_CON3_HP_ESD_RES_EN_POS)
#define AUDDEC_ANA_CON3_HP_OUT_SHORT_EN_POS         (9)
#define AUDDEC_ANA_CON3_HP_OUT_SHORT_EN_MASK        (1<<AUDDEC_ANA_CON3_HP_OUT_SHORT_EN_POS)
#define AUDDEC_ANA_CON3_HPL_CMFB_RNWSEL_POS         (10)
#define AUDDEC_ANA_CON3_HPL_CMFB_RNWSEL_MASK        (1<<AUDDEC_ANA_CON3_HPL_CMFB_RNWSEL_POS)
#define AUDDEC_ANA_CON3_HPR_CMFB_RNWSEL_POS         (11)
#define AUDDEC_ANA_CON3_HPR_CMFB_RNWSEL_MASK        (1<<AUDDEC_ANA_CON3_HPR_CMFB_RNWSEL_POS)

#define AUDDEC_ANA_CON4                 (ABB_BASE + 0x110)
#define AUDDEC_ANA_CON4_HPL_TRIM_POS                (0)
#define AUDDEC_ANA_CON4_HPL_TRIM_MASK               (0x1F<<AUDDEC_ANA_CON4_HPL_TRIM_POS)
#define AUDDEC_ANA_CON4_HPL_FINE_TRIM_POS           (5)
#define AUDDEC_ANA_CON4_HPL_FINE_MASK               (7<<AUDDEC_ANA_CON4_HPL_FINE_TRIM_POS)
#define AUDDEC_ANA_CON4_HPR_TRIM_POS                (8)
#define AUDDEC_ANA_CON4_HPR_TRIM_MASK               (0x1F<<AUDDEC_ANA_CON4_HPR_TRIM_POS)
#define AUDDEC_ANA_CON4_HPR_FINE_TRIM_POS           (13)
#define AUDDEC_ANA_CON4_HPR_FINE_MASK               (7<<AUDDEC_ANA_CON4_HPR_FINE_TRIM_POS)

#define AUDDEC_ANA_CON5                 (ABB_BASE + 0x114)
#define AUDDEC_ANA_CON6                 (ABB_BASE + 0x118)
#define AUDDEC_ANA_CON7                 (ABB_BASE + 0x11C)
#define AUDDEC_ANA_CON8                 (ABB_BASE + 0x120)
#define AUDDEC_ANA_CON9                 (ABB_BASE + 0x124)
#define AUDDEC_ANA_CON9_DECODER_RST_POS             (0)
#define AUDDEC_ANA_CON9_DECODER_RST_MASK            (1<<AUDDEC_ANA_CON9_DECODER_RST_POS)
#define AUDDEC_ANA_CON9_AUDDAC_13MCK_EN_POS         (6)
#define AUDDEC_ANA_CON9_AUDDAC_13MCK_EN_MASK        (1<<AUDDEC_ANA_CON9_AUDDAC_13MCK_EN_POS)
#define AUDDEC_ANA_CON9_AUDCLD_26MCK_EN_POS         (9)
#define AUDDEC_ANA_CON9_AUDCLD_26MCK_EN_MASK        (1<<AUDDEC_ANA_CON9_AUDCLD_26MCK_EN_POS)
#define AUDDEC_ANA_CON9_POWERDOWN_POS               (14)
#define AUDDEC_ANA_CON9_POWERDOWN_MASK              (1<<AUDDEC_ANA_CON9_POWERDOWN_POS)

#define AUDDEC_ANA_CON10                (ABB_BASE + 0x128)
#define AUDDEC_ANA_CON10_HPL_TBENHVCM_BUF_EN_EN_POS                 (0)
#define AUDDEC_ANA_CON10_HPL_TBENHVCM_BUF_EN_EN_MASK                (1<<AUDDEC_ANA_CON10_HPL_TBENHVCM_BUF_EN_EN_POS)
#define AUDDEC_ANA_CON10_HPR_TBENHVCM_BUF_EN_EN_POS                 (1)
#define AUDDEC_ANA_CON10_HPR_TBENHVCM_BUF_EN_EN_MASK                (1<<AUDDEC_ANA_CON10_HPR_TBENHVCM_BUF_EN_EN_POS)

#define AUDDEC_ANA_CON11                (ABB_BASE + 0x12C)
#define AUDDEC_ANA_CON12                (ABB_BASE + 0x130)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_OUT_AUXPWRUP_POS            (2)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_OUT_AUXPWRUP_MASK           (1<<AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_OUT_AUXPWRUP_POS)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_OUT_AUXPWRUP_POS            (3)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_OUT_AUXPWRUP_MASK           (1<<AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_OUT_AUXPWRUP_POS)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_PWRUP_POS                   (4)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_PWRUP_MASK                  (1<<AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_PWRUP_POS)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_PWRUP_POS                   (5)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_PWRUP_MASK                  (1<<AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_PWRUP_POS)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_PWRUP_IBIAS_POS             (6)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_PWRUP_IBIAS_MASK            (1<<AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_PWRUP_IBIAS_POS)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_PWRUP_IBIAS_POS             (7)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_PWRUP_IBIAS_MASK            (1<<AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_PWRUP_IBIAS_POS)

#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_MUXINPUTSEL_POS             (8)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_MUXINPUTSEL_MASK            (3<<AUDDEC_ANA_CON12_RG_CLD_AUD_HPL_MUXINPUTSEL_POS)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_MUXINPUTSEL_POS             (10)
#define AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_MUXINPUTSEL_MASK            (3<<AUDDEC_ANA_CON12_RG_CLD_AUD_HPR_MUXINPUTSEL_POS)

#define AUDDEC_ANA_CON13                (ABB_BASE + 0x134)
#define AUDDEC_ANA_CON14                (ABB_BASE + 0x138)
#define AUDDEC_ANA_CON15                (ABB_BASE + 0x13C)
#define AUDDEC_ANA_CON16                (ABB_BASE + 0x140)
#define AUDDEC_ANA_CON17                (ABB_BASE + 0x144)
#define AUDDEC_ANA_CON18                (ABB_BASE + 0x148)
#define AUDDEC_ANA_CON19                (ABB_BASE + 0x14C)
#define AUDDEC_ANA_CON20                (ABB_BASE + 0x150)
#define AUDDEC_ANA_CON21                (ABB_BASE + 0x154)
#define AUDDEC_ANA_CON22                (ABB_BASE + 0x158)
#define AUDDEC_ANA_CON23                (ABB_BASE + 0x15C)
#define AUDDEC_ANA_CON24                (ABB_BASE + 0x160)
#define AUDDEC_ANA_CON25                (ABB_BASE + 0x164)
#define AUDDEC_ANA_CON25_CLD_RSV1_POS               (8)
#define AUDDEC_ANA_CON25_CLD_RSV1_MASK              (0xFF<<AUDDEC_ANA_CON25_CLD_RSV1_POS)

#define AUDDEC_ANA_CON26                (ABB_BASE + 0x168)
#define AUDDEC_ANA_CON26_HPL_OUT_STAG_POS           (1)
#define AUDDEC_ANA_CON26_HPL_OUT_STAG_MASK          (7<<AUDDEC_ANA_CON26_HPL_OUT_STAG_POS)
#define AUDDEC_ANA_CON26_HPR_OUT_STAG_POS           (5)
#define AUDDEC_ANA_CON26_HPR_OUT_STAG_MASK          (7<<AUDDEC_ANA_CON26_HPR_OUT_STAG_POS)



#define AUDDEC_ANA_CON27                (ABB_BASE + 0x16C)
#define AUDDEC_ANA_CON28                (ABB_BASE + 0x170)
#define AUDDEC_ANA_CON29                (ABB_BASE + 0x174)
#define AUDDEC_ANA_CON30                (ABB_BASE + 0x178)
#define AUDDEC_ANA_CON31                (ABB_BASE + 0x17C)
#define AUDDEC_ANA_CON31_AUDCLD_26M_D5NS_DELAY_POS      (2)
#define AUDDEC_ANA_CON31_AUDCLD_26M_D5NS_DELAY_MASK     (7<<AUDDEC_ANA_CON31_AUDCLD_26M_D5NS_DELAY_POS)
#define AUDDEC_ANA_CON31_IREF_SW_EN_POS                 (10)
#define AUDDEC_ANA_CON31_IREF_SW_EN_MASK                (1<<AUDDEC_ANA_CON31_IREF_SW_EN_POS)

#define AUDDEC_ANA_CON32                (ABB_BASE + 0x180) //bit2,8,10,12
#define AUDDEC_ANA_CON32_0P8VLDO_POS     (0) //bit 2:0
#define AUDDEC_ANA_CON32_0P8VLDO_MASK    (7<<AUDDEC_ANA_CON32_0P8VLDO_POS)
#define AUDDEC_ANA_CON32_AUDREFSEL_VCM_POS     (4) //bit 6:4
#define AUDDEC_ANA_CON32_AUDREFSEL_VCM_MASK    (7<<AUDDEC_ANA_CON32_AUDREFSEL_VCM_POS)
#define AUDDEC_ANA_CON32_AUDGLB_DAC_VREF_POS   (8) //9:8
#define AUDDEC_ANA_CON32_AUDGLB_DAC_VREF_MASK  (3<<AUDDEC_ANA_CON32_AUDGLB_DAC_VREF_POS)
#define AUDDEC_ANA_CON32_AUDGLB_VREFGEN_POS   (10) //11:10
#define AUDDEC_ANA_CON32_AUDGLB_VREFGEN_MASK  (3<<AUDDEC_ANA_CON32_AUDGLB_VREFGEN_POS)
#define AUDDEC_ANA_CON32_AUDBIAS_POS           (12) //13:12
#define AUDDEC_ANA_CON32_AUDBIAS_MASK          (3<<AUDDEC_ANA_CON32_AUDBIAS_POS)

#define AUDDEC_ANA_CON33                (ABB_BASE + 0x184)
#define AUDDEC_ANA_CON33_CLG_R_IBIAS_EN_POS         (0)
#define AUDDEC_ANA_CON33_CLG_R_IBIAS_EN_MASK        (1<<AUDDEC_ANA_CON33_CLG_R_IBIAS_EN_POS)
#define AUDDEC_ANA_CON33_CLG_L_IBIAS_EN_POS         (1)
#define AUDDEC_ANA_CON33_CLG_L_IBIAS_EN_MASK        (1<<AUDDEC_ANA_CON33_CLG_L_IBIAS_EN_POS)


#define AUDDEC_ANA_CON34                (ABB_BASE + 0x188)
#define AUDDEC_ANA_CON35                (ABB_BASE + 0x18C)
#define AUDDEC_ANA_CON36                (ABB_BASE + 0x190)
#define AUDDEC_ANA_CON37                (ABB_BASE + 0x194)
#define AUDDEC_ANA_CON38                (ABB_BASE + 0x198)
#define AUDDEC_ANA_CON39                (ABB_BASE + 0x19C)
#define AUDDEC_ANA_CON40                (ABB_BASE + 0x1A0)
#define AUDDEC_ANA_CON41                (ABB_BASE + 0x1A4)
#define AUDDEC_ANA_CON42                (ABB_BASE + 0x1A8)
#define AUDDEC_ANA_CON43                (ABB_BASE + 0x1AC)
#define AUDDEC_ANA_CON44                (ABB_BASE + 0x1B0)
#define AUDDEC_ANA_CON45                (ABB_BASE + 0x1B4)

/*UL*/
#define AUDENC_ANA_CON0                     (ABB_BASE + 0x200) //ADC01 L
#define AUDENC_ANA_CON0_L_20K_EN_POS                (15) //20K = 1, 10K = 0
#define AUDENC_ANA_CON0_L_20K_EN_MASK               (1<<AUDENC_ANA_CON0_L_20K_EN_POS)
#define AUDENC_ANA_CON0_L_INPUT_SELECT_POS          (13) //14:13, all=10(L AMP)
#define AUDENC_ANA_CON0_L_INPUT_SELECT_MASK         (3<<AUDENC_ANA_CON0_L_INPUT_SELECT_POS)
#define AUDENC_ANA_CON0_L_POWER_UP_POS              (12)
#define AUDENC_ANA_CON0_L_POWER_UP_MASK             (1<<AUDENC_ANA_CON0_L_POWER_UP_POS)
#define AUDENC_ANA_CON0_L_PREAMP_GAIN_POS           (8) //11:8 4bit
#define AUDENC_ANA_CON0_L_PREAMP_GAIN_MASK          (0xF<<AUDENC_ANA_CON0_L_PREAMP_GAIN_POS)
#define AUDENC_ANA_CON0_L_PREAMP_INPUT_SELECT_POS   (6) //7:6 2bit, DIFF = 01(AIN0), SE = 00(None)
#define AUDENC_ANA_CON0_L_PREAMP_INPUT_SELECT_MASK  (3<<AUDENC_ANA_CON0_L_PREAMP_INPUT_SELECT_POS)
#define AUDENC_ANA_CON0_L_PREAMP_POWER_POS          (0) //L PREAMP POWER, all 1
#define AUDENC_ANA_CON0_L_PREAMP_POWER_MASK         (1<<AUDENC_ANA_CON0_L_PREAMP_POWER_POS)

#define AUDENC_ANA_CON1                 (ABB_BASE + 0x204) //ADC01 R
#define AUDENC_ANA_CON1_R_20K_EN_POS                (15) //20K = 1, 10K = 0
#define AUDENC_ANA_CON1_R_20K_EN_MASK               (1<<AUDENC_ANA_CON1_R_20K_EN_POS)
#define AUDENC_ANA_CON1_R_INPUT_SELECT_POS          (13) //14:13, all=10(R AMP)
#define AUDENC_ANA_CON1_R_INPUT_SELECT_MASK         (3<<AUDENC_ANA_CON1_R_INPUT_SELECT_POS)
#define AUDENC_ANA_CON1_R_POWER_UP_POS              (12)
#define AUDENC_ANA_CON1_R_POWER_UP_MASK             (1<<AUDENC_ANA_CON1_R_POWER_UP_POS)
#define AUDENC_ANA_CON1_R_PREAMP_GAIN_POS           (8) //11:8 4bit
#define AUDENC_ANA_CON1_R_PREAMP_GAIN_MASK          (0xF<<AUDENC_ANA_CON1_R_PREAMP_GAIN_POS)
#define AUDENC_ANA_CON1_R_PREAMP_INPUT_SELECT_POS   (6) //7:6 2bit, DIFF = 01(AIN0), SE = 00(None)
#define AUDENC_ANA_CON1_R_PREAMP_INPUT_SELECT_MASK  (3<<AUDENC_ANA_CON1_R_PREAMP_INPUT_SELECT_POS)
#define AUDENC_ANA_CON1_R_PREAMP_POWER_POS          (0) //L PREAMP POWER, all 1
#define AUDENC_ANA_CON1_R_PREAMP_POWER_MASK         (1<<AUDENC_ANA_CON1_R_PREAMP_POWER_POS)


#define AUDENC_ANA_CON2                 (ABB_BASE + 0x208)
//HP:000000 = 0, NM: 011000 = 24, LP: 010111 = 23, ULP: 100111 = 39
#define AUDENC_ANA_CON2_PERFORMANCE_POS         (0) //6bit
#define AUDENC_ANA_CON2_PERFORMANCE_MASK        (0x3F<<AUDENC_ANA_CON2_PERFORMANCE_POS)

#define AUDENC_ANA_CON3                 (ABB_BASE + 0x20C)
#define AUDENC_ANA_CON3_ADC01_CLK_SOURCE_POS   (2) //3:2
#define AUDENC_ANA_CON3_ADC01_CLK_SOURCE_MASK  (0x3<<AUDENC_ANA_CON3_ADC01_CLK_SOURCE_POS)
#define AUDENC_ANA_CON3_CLK_FROM_DL_TO_UL_POS   (4) //13MCK, all are 1
#define AUDENC_ANA_CON3_CLK_FROM_DL_TO_UL_MASK  (1<<AUDENC_ANA_CON3_CLK_FROM_DL_TO_UL_POS)
#define AUDENC_ANA_CON3_ADCDAC25FS_POS          (5) //ULP = 1, other = 0
#define AUDENC_ANA_CON3_ADCDAC25FS_MASK         (1<<AUDENC_ANA_CON3_ADCDAC25FS_POS)
#define AUDENC_ANA_CON3_PGAL_ACCFS_POS          (6) //
#define AUDENC_ANA_CON3_PGAL_ACCFS_MASK         (1<<AUDENC_ANA_CON3_PGAL_ACCFS_POS)
#define AUDENC_ANA_CON3_PGAR_ACCFS_POS          (7) //
#define AUDENC_ANA_CON3_PGAR_ACCFS_MASK         (1<<AUDENC_ANA_CON3_PGAR_ACCFS_POS)
#define AUDENC_ANA_CON3_LOW_POWER_EN_POS        (8) //bit10,9,8, ULP = 010, other = 000
#define AUDENC_ANA_CON3_LOW_POWER_EN_MASK       (7<<AUDENC_ANA_CON3_LOW_POWER_EN_POS)
#define AUDENC_ANA_CON3_LOW_POWER_EN2_POS        (9)
#define AUDENC_ANA_CON3_LOW_POWER_EN2_MASK       (1<<AUDENC_ANA_CON3_LOW_POWER_EN2_POS)
#define AUDENC_ANA_CON3_LOW_POWER_EN3_POS        (10)
#define AUDENC_ANA_CON3_LOW_POWER_EN3_MASK       (1<<AUDENC_ANA_CON3_LOW_POWER_EN3_POS)

#define AUDENC_ANA_CON4                 (ABB_BASE + 0x210)
#define AUDENC_ANA_CON4_ADCFS_RESET_POS        (3) //reset
#define AUDENC_ANA_CON4_ADCFS_RESET_MASK       (1<<AUDENC_ANA_CON4_ADCFS_RESET_POS)

#define AUDENC_ANA_CON5                 (ABB_BASE + 0x214)
#define AUDENC_ANA_CON6                 (ABB_BASE + 0x218)
#define AUDENC_ANA_CON7                 (ABB_BASE + 0x21C)
#define AUDENC_ANA_CON7_RATEHALF_POS        (1) //LP = 1
#define AUDENC_ANA_CON7_RATEHALF_MASK       (1<<AUDENC_ANA_CON7_RATEHALF_POS)

#define AUDENC_ANA_CON8                 (ABB_BASE + 0x220)
#define AUDENC_ANA_CON9                 (ABB_BASE + 0x224) //ADC23 L
#define AUDENC_ANA_CON9_L_20K_EN_POS                (15) //20K = 1, 10K = 0
#define AUDENC_ANA_CON9_L_20K_EN_MASK               (1<<AUDENC_ANA_CON9_L_20K_EN_POS)
#define AUDENC_ANA_CON9_L_INPUT_SELECT_POS          (13) //14:13, all=10(L AMP)
#define AUDENC_ANA_CON9_L_INPUT_SELECT_MASK         (3<<AUDENC_ANA_CON9_L_INPUT_SELECT_POS)
#define AUDENC_ANA_CON9_L_POWER_UP_POS              (12)
#define AUDENC_ANA_CON9_L_POWER_UP_MASK             (1<<AUDENC_ANA_CON9_L_POWER_UP_POS)
#define AUDENC_ANA_CON9_L_PREAMP_GAIN_POS           (8) //11:8 4bit
#define AUDENC_ANA_CON9_L_PREAMP_GAIN_MASK          (0xF<<AUDENC_ANA_CON9_L_PREAMP_GAIN_POS)
#define AUDENC_ANA_CON9_L_PREAMP_INPUT_SELECT_POS   (4) //5:4 2bit, DIFF = 01(AIN0), SE = 00(None)
#define AUDENC_ANA_CON9_L_PREAMP_INPUT_SELECT_MASK  (3<<AUDENC_ANA_CON9_L_PREAMP_INPUT_SELECT_POS)
#define AUDENC_ANA_CON9_L_PREAMP_POWER_POS          (0) //L PREAMP POWER, all 1
#define AUDENC_ANA_CON9_L_PREAMP_POWER_MASK         (1<<AUDENC_ANA_CON9_L_PREAMP_POWER_POS)

#define AUDENC_ANA_CON10                (ABB_BASE + 0x228) //ADC23 R
#define AUDENC_ANA_CON10_R_20K_EN_POS                (15) //20K = 1, 10K = 0
#define AUDENC_ANA_CON10_R_20K_EN_MASK               (1<<AUDENC_ANA_CON10_R_20K_EN_POS)
#define AUDENC_ANA_CON10_R_INPUT_SELECT_POS          (13) //14:13, all=10(R AMP)
#define AUDENC_ANA_CON10_R_INPUT_SELECT_MASK         (3<<AUDENC_ANA_CON10_R_INPUT_SELECT_POS)
#define AUDENC_ANA_CON10_R_POWER_UP_POS              (12)
#define AUDENC_ANA_CON10_R_POWER_UP_MASK             (1<<AUDENC_ANA_CON10_R_POWER_UP_POS)
#define AUDENC_ANA_CON10_R_PREAMP_GAIN_POS           (8) //11:8 4bit
#define AUDENC_ANA_CON10_R_PREAMP_GAIN_MASK          (0xF<<AUDENC_ANA_CON10_R_PREAMP_GAIN_POS)
#define AUDENC_ANA_CON10_R_PREAMP_INPUT_SELECT_POS   (4) //5:4 2bit, DIFF = 01(AIN0), SE = 00(None)
#define AUDENC_ANA_CON10_R_PREAMP_INPUT_SELECT_MASK  (3<<AUDENC_ANA_CON10_R_PREAMP_INPUT_SELECT_POS)
#define AUDENC_ANA_CON10_R_PREAMP_POWER_POS          (0) //L PREAMP POWER, all 1
#define AUDENC_ANA_CON10_R_PREAMP_POWER_MASK         (1<<AUDENC_ANA_CON10_R_PREAMP_POWER_POS)

#define AUDENC_ANA_CON11                (ABB_BASE + 0x22C) //ADC23 Performance
//HP:000010 = 0x2, NM: 000110 = 0x6, LP: 111010 = 0x3A, ULP: 0x111001 = 39
#define AUDENC_ANA_CON11_PERFORMANCE_POS         (0) //6bit
#define AUDENC_ANA_CON11_PERFORMANCE_MASK        (0x3F<<AUDENC_ANA_CON11_PERFORMANCE_POS)

#define AUDENC_ANA_CON12                (ABB_BASE + 0x230)
#define AUDENC_ANA_CON12_CLK_FROM_DL_TO_UL_POS   (4) //all are 1
#define AUDENC_ANA_CON12_CLK_FROM_DL_TO_UL_MASK  (1<<AUDENC_ANA_CON12_CLK_FROM_DL_TO_UL_POS)
#define AUDENC_ANA_CON12_ADCDAC25FS_POS          (5) //ULP = 1, other = 0
#define AUDENC_ANA_CON12_ADCDAC25FS_MASK         (1<<AUDENC_ANA_CON12_ADCDAC25FS_POS)
#define AUDENC_ANA_CON12_PGAL_ACCFS_POS          (6) //
#define AUDENC_ANA_CON12_PGAL_ACCFS_MASK         (1<<AUDENC_ANA_CON12_PGAL_ACCFS_POS)
#define AUDENC_ANA_CON12_PGAR_ACCFS_POS          (7) //
#define AUDENC_ANA_CON12_PGAR_ACCFS_MASK         (1<<AUDENC_ANA_CON12_PGAR_ACCFS_POS)
#define AUDENC_ANA_CON12_LOW_POWER_EN_POS        (8) //bit10,9,8, ULP = 010, other = 000
#define AUDENC_ANA_CON12_LOW_POWER_EN_MASK       (7<<AUDENC_ANA_CON12_LOW_POWER_EN_POS)

#define AUDENC_ANA_CON13                (ABB_BASE + 0x234)
#define AUDENC_ANA_CON13_ADCFS_RESET_POS        (3) //reset
#define AUDENC_ANA_CON13_ADCFS_RESET_MASK       (1<<AUDENC_ANA_CON13_ADCFS_RESET_POS)

#define AUDENC_ANA_CON14                (ABB_BASE + 0x238)
#define AUDENC_ANA_CON15                (ABB_BASE + 0x23C)
#define AUDENC_ANA_CON16                (ABB_BASE + 0x240)
#define AUDENC_ANA_CON16_RATEHALF_POS           (1) //LP = 1
#define AUDENC_ANA_CON16_RATEHALF_MASK          (1<<AUDENC_ANA_CON16_RATEHALF_POS)
#define AUDENC_ANA_CON16_SPAREVA25_POS          (8) //11:8, 4bit, all 0011
#define AUDENC_ANA_CON16_SPAREVA25_MASK         (1<<AUDENC_ANA_CON16_SPAREVA25_POS)

#define AUDENC_ANA_CON17                (ABB_BASE + 0x244)
#define AUDENC_ANA_CON18                (ABB_BASE + 0x248)
#define AUDENC_ANA_CON19                (ABB_BASE + 0x24C)
#define AUDENC_ANA_CON20                (ABB_BASE + 0x250)
#define AUDENC_ANA_CON21                (ABB_BASE + 0x254)
#define AUDENC_ANA_CON22                (ABB_BASE + 0x258)
#define AUDENC_ANA_CON23                (ABB_BASE + 0x25C)
#define AUDENC_ANA_CON24                (ABB_BASE + 0x260)
#define AUDENC_ANA_CON25                (ABB_BASE + 0x264)
#define AUDENC_ANA_CON26                (ABB_BASE + 0x268)
#define AUDENC_ANA_CON27                (ABB_BASE + 0x26C)
#define AUDENC_ANA_CON28                (ABB_BASE + 0x270)
#define AUDENC_ANA_CON28_DMIC0_EN_POS               (0)
#define AUDENC_ANA_CON28_DMIC0_EN_MASK              (1<<AUDENC_ANA_CON28_DMIC0_EN_POS)
#define AUDENC_ANA_CON28_CLOCK_SOURCE_POS           (3)
#define AUDENC_ANA_CON28_CLOCK_SOURCE_MASK          (1<<AUDENC_ANA_CON28_CLOCK_SOURCE_POS)
#define AUDENC_ANA_CON28_POS_DUTY_POS               (4)
#define AUDENC_ANA_CON28_POS_DUTY_MASK              (3<<AUDENC_ANA_CON28_POS_DUTY_POS)
#define AUDENC_ANA_CON28_NEG_DUTY_POS               (6)
#define AUDENC_ANA_CON28_NEG_DUTY_MASK              (3<<AUDENC_ANA_CON28_NEG_DUTY_POS)
#define AUDENC_ANA_CON28_DMIC0_DATA_EN_POS          (13)
#define AUDENC_ANA_CON28_DMIC0_DATA_EN_MASK         (1<<AUDENC_ANA_CON28_DMIC0_DATA_EN_POS)

#define AUDENC_ANA_CON29                (ABB_BASE + 0x274)
#define AUDENC_ANA_CON29_DMIC1_EN_POS               (0)
#define AUDENC_ANA_CON29_DMIC1_EN_MASK              (1<<AUDENC_ANA_CON29_DMIC1_EN_POS)
#define AUDENC_ANA_CON29_CLOCK_SOURCE_POS           (3)
#define AUDENC_ANA_CON29_CLOCK_SOURCE_MASK          (1<<AUDENC_ANA_CON29_CLOCK_SOURCE_POS)
#define AUDENC_ANA_CON29_POS_DUTY_POS               (4)
#define AUDENC_ANA_CON29_POS_DUTY_MASK              (3<<AUDENC_ANA_CON29_POS_DUTY_POS)
#define AUDENC_ANA_CON29_NEG_DUTY_POS               (6)
#define AUDENC_ANA_CON29_NEG_DUTY_MASK              (3<<AUDENC_ANA_CON29_NEG_DUTY_POS)
#define AUDENC_ANA_CON29_DMIC1_DATA_EN_POS          (13)
#define AUDENC_ANA_CON29_DMIC1_DATA_EN_MASK         (1<<AUDENC_ANA_CON29_DMIC1_DATA_EN_POS)

#define AUDENC_ANA_CON30                (ABB_BASE + 0x278)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2EN_POS        (0)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2EN_MASK       (1<<AUDENC_ANA_CON30_RG_AUDDIGMIC2EN_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMICBIAS2_POS      (1)
#define AUDENC_ANA_CON30_RG_AUDDIGMICBIAS2_MASK     (3<<AUDENC_ANA_CON30_RG_AUDDIGMICBIAS2_POS)
#define AUDENC_ANA_CON30_RG_DMIC2HPCLKEN_POS        (3)
#define AUDENC_ANA_CON30_RG_DMIC2HPCLKEN_MASK       (1<<AUDENC_ANA_CON30_RG_DMIC2HPCLKEN_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2PDUTY_POS     (4)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2PDUTY_MASK    (3<<AUDENC_ANA_CON30_RG_AUDDIGMIC2PDUTY_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2NDUTY_POS     (6)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2NDUTY_MASK    (3<<AUDENC_ANA_CON30_RG_AUDDIGMIC2NDUTY_POS)
#define AUDENC_ANA_CON30_RG_DMIC2MONEN_POS          (8)
#define AUDENC_ANA_CON30_RG_DMIC2MONEN_MASK         (1<<AUDENC_ANA_CON30_RG_DMIC2MONEN_POS)
#define AUDENC_ANA_CON30_RG_DMIC2MONSEL_POS         (9)
#define AUDENC_ANA_CON30_RG_DMIC2MONSEL_MASK        (7<<AUDENC_ANA_CON30_RG_DMIC2MONSEL_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2CLK_SHARE_EN_POS (12)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2CLK_SHARE_EN_MASK (1<<AUDENC_ANA_CON30_RG_AUDDIGMIC2CLK_SHARE_EN_POS)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2DATA_EN_POS (13)
#define AUDENC_ANA_CON30_RG_AUDDIGMIC2DATA_EN_MASK (1<<AUDENC_ANA_CON30_RG_AUDDIGMIC2DATA_EN_POS)
#define AUDENC_ANA_CON30_RG_AUDSPAREVMIC2_POS  (14)
#define AUDENC_ANA_CON30_RG_AUDSPAREVMIC2_MASK (3<<AUDENC_ANA_CON30_RG_AUDSPAREVMIC2_POS)

#define AUDENC_ANA_CON31                (ABB_BASE + 0x27C)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3EN_POS (0)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3EN_MASK (1<<AUDENC_ANA_CON31_RG_AUDDIGMIC3EN_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMICBIAS3_POS (1)
#define AUDENC_ANA_CON31_RG_AUDDIGMICBIAS3_MASK (3<<AUDENC_ANA_CON31_RG_AUDDIGMICBIAS3_POS)
#define AUDENC_ANA_CON31_RG_DMIC3HPCLKEN_POS (3)
#define AUDENC_ANA_CON31_RG_DMIC3HPCLKEN_MASK (1<<AUDENC_ANA_CON31_RG_DMIC3HPCLKEN_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3PDUTY_POS (4)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3PDUTY_MASK (3<<AUDENC_ANA_CON31_RG_AUDDIGMIC3PDUTY_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3NDUTY_POS (6)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3NDUTY_MASK (3<<AUDENC_ANA_CON31_RG_AUDDIGMIC3NDUTY_POS)
#define AUDENC_ANA_CON31_RG_DMIC3MONEN_POS (8)
#define AUDENC_ANA_CON31_RG_DMIC3MONEN_MASK (1<<AUDENC_ANA_CON31_RG_DMIC3MONEN_POS)
#define AUDENC_ANA_CON31_RG_DMIC3MONSEL_POS (9)
#define AUDENC_ANA_CON31_RG_DMIC3MONSEL_MASK (7<<AUDENC_ANA_CON31_RG_DMIC3MONSEL_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3CLK_SHARE_EN_POS (12)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3CLK_SHARE_EN_MASK (1<<AUDENC_ANA_CON31_RG_AUDDIGMIC3CLK_SHARE_EN_POS)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3DATA_EN_POS (13)
#define AUDENC_ANA_CON31_RG_AUDDIGMIC3DATA_EN_MASK (1<<AUDENC_ANA_CON31_RG_AUDDIGMIC3DATA_EN_POS)
#define AUDENC_ANA_CON31_RG_AUDSPAREVMIC3_POS (14)
#define AUDENC_ANA_CON31_RG_AUDSPAREVMIC3_MASK (3<<AUDENC_ANA_CON31_RG_AUDSPAREVMIC3_POS)

#define AUDENC_ANA_CON32                (ABB_BASE + 0x280)
#define AUDENC_ANA_CON32_AUDUL_ADC_REV0_POS  (8)
#define AUDENC_ANA_CON32_AUDUL_ADC_REV0_MASK (1<<AUDENC_ANA_CON32_AUDUL_ADC_REV0_POS)
#define AUDENC_ANA_CON33                (ABB_BASE + 0x284)
#define AUDENC_ANA_CON34                (ABB_BASE + 0x288) //ADC01 L&R
#define AUDENC_ANA_CON34_L_CLK_POS    (2) //for L, bit 5,4,3,2, HP: 1010, ULP: 1001, other = 1000
#define AUDENC_ANA_CON34_L_CLK_MASK   (0xF<<AUDENC_ANA_CON34_L_CLK_POS)
#define AUDENC_ANA_CON34_ADC01_L_CLK_HALF_RST_POS    (3)
#define AUDENC_ANA_CON34_ADC01_L_CLK_HALF_RST_MASK   (0x1<<AUDENC_ANA_CON34_ADC01_L_CLK_HALF_RST_POS)
#define AUDENC_ANA_CON34_ADC01_L_CLK_FROM_DL_POS      (4)
#define AUDENC_ANA_CON34_ADC01_L_CLK_FROM_DL_MASK     (1<<AUDENC_ANA_CON34_ADC01_L_CLK_FROM_DL_POS)
#define AUDENC_ANA_CON34_ADC01LDO_L_SW_EN_POS         (5)
#define AUDENC_ANA_CON34_ADC01LDO_L_SW_EN_MASK        (1<<AUDENC_ANA_CON34_ADC01LDO_L_SW_EN_POS)
#define AUDENC_ANA_CON34_R_CLK_POS                    (10) //for R, bit 13,12,11,10, ULP: 1001, other = 1000
#define AUDENC_ANA_CON34_R_CLK_MASK                   (0xF<<AUDENC_ANA_CON34_R_CLK_POS)
#define AUDENC_ANA_CON34_ADC01_R_CLK_HALF_RST_POS     (11)
#define AUDENC_ANA_CON34_ADC01_R_CLK_HALF_RST_MASK    (1<<AUDENC_ANA_CON34_ADC01_R_CLK_HALF_RST_POS)
#define AUDENC_ANA_CON34_ADC01_R_CLK_FROM_DL_POS      (12)
#define AUDENC_ANA_CON34_ADC01_R_CLK_FROM_DL_MASK     (1<<AUDENC_ANA_CON34_ADC01_R_CLK_FROM_DL_POS)
#define AUDENC_ANA_CON34_ADC01LDO_R_SW_EN_POS         (13)
#define AUDENC_ANA_CON34_ADC01LDO_R_SW_EN_MASK        (1<<AUDENC_ANA_CON34_ADC01LDO_R_SW_EN_POS)

#define AUDENC_ANA_CON35                (ABB_BASE + 0x28C) //ADC23 L&R
#define AUDENC_ANA_CON35_L_CLK_POS    (2) //for L, bit 5,4,3,2, HP: 1010, ULP: 1001, other = 1000
#define AUDENC_ANA_CON35_L_CLK_MASK   (0xF<<AUDENC_ANA_CON35_L_CLK_POS)
#define AUDENC_ANA_CON35_R_CLK_POS    (10) //for R, bit 13,12,11,10, ULP: 1001, other = 1000
#define AUDENC_ANA_CON35_R_CLK_MASK   (0xF<<AUDENC_ANA_CON35_R_CLK_POS)
#define AUDENC_ANA_CON35_ADC23_L_CLK_HALF_RST_POS    (3)
#define AUDENC_ANA_CON35_ADC23_L_CLK_HALF_RST_MASK   (0x1<<AUDENC_ANA_CON35_ADC23_L_CLK_HALF_RST_POS)
#define AUDENC_ANA_CON35_ADC23_L_CLK_FROM_DL_POS      (4)
#define AUDENC_ANA_CON35_ADC23_L_CLK_FROM_DL_MASK     (1<<AUDENC_ANA_CON35_ADC23_L_CLK_FROM_DL_POS)
#define AUDENC_ANA_CON35_ADC23LDO_L_SW_EN_POS         (5)
#define AUDENC_ANA_CON35_ADC23LDO_L_SW_EN_MASK        (1<<AUDENC_ANA_CON35_ADC23LDO_L_SW_EN_POS)
#define AUDENC_ANA_CON35_ADC23_R_CLK_HALF_RST_POS     (11)
#define AUDENC_ANA_CON35_ADC23_R_CLK_HALF_RST_MASK    (1<<AUDENC_ANA_CON35_ADC23_R_CLK_HALF_RST_POS)
#define AUDENC_ANA_CON35_ADC23_R_CLK_FROM_DL_POS      (12)
#define AUDENC_ANA_CON35_ADC23_R_CLK_FROM_DL_MASK     (1<<AUDENC_ANA_CON35_ADC23_R_CLK_FROM_DL_POS)
#define AUDENC_ANA_CON35_ADC23LDO_R_SW_EN_POS         (13)
#define AUDENC_ANA_CON35_ADC23LDO_R_SW_EN_MASK        (1<<AUDENC_ANA_CON35_ADC23LDO_R_SW_EN_POS)

#define AUDENC_ANA_CON36                (ABB_BASE + 0x290)
#define AUDENC_ANA_CON36_L_ACC_SE_EN_POS    (5) //for L, SE = 1
#define AUDENC_ANA_CON36_L_ACC_SE_EN_MASK   (1<<AUDENC_ANA_CON36_L_ACC_SE_EN_POS)
#define AUDENC_ANA_CON36_L_GAIN_COMP_10K_POS    (8) //11:8,for L, SE&10K = 0010, other = 0000
#define AUDENC_ANA_CON36_L_GAIN_COMP_10K_MASK   (0xF<<AUDENC_ANA_CON36_L_GAIN_COMP_10K_POS)
#define AUDENC_ANA_CON36_L_GAIN_COMP_20K_POS    (12) //15:12,for L, SE&20K = 0010, other = 0000
#define AUDENC_ANA_CON36_L_GAIN_COMP_20K_MASK   (0xF<<AUDENC_ANA_CON36_L_GAIN_COMP_20K_POS)

#define AUDENC_ANA_CON37                (ABB_BASE + 0x294) //ADC01 L&R
#define AUDENC_ANA_CON37_PREAMP_L_REV_POS    (2) //for L, 3:2, all 01
#define AUDENC_ANA_CON37_PREAMP_L_REV_MASK   (3<<AUDENC_ANA_CON37_PREAMP_L_REV_POS)
#define AUDENC_ANA_CON37_R_ACC_SE_EN_POS     (13) //for R
#define AUDENC_ANA_CON37_R_ACC_SE_EN_MASK    (1<<AUDENC_ANA_CON37_R_ACC_SE_EN_POS)

#define AUDENC_ANA_CON38                (ABB_BASE + 0x298)//R
#define AUDENC_ANA_CON38_PREAMP_R_REV_POS       (10) //for R, 11:10, all 01
#define AUDENC_ANA_CON38_PREAMP_R_REV_MASK      (3<<AUDENC_ANA_CON38_PREAMP_R_REV_POS)
#define AUDENC_ANA_CON38_R_GAIN_COMP_20K_POS    (4) //for R, 7:4, 4bit, SE&20K = 0010, other = 0000
#define AUDENC_ANA_CON38_R_GAIN_COMP_20K_MASK   (0xF<<AUDENC_ANA_CON38_R_GAIN_COMP_20K_POS)
#define AUDENC_ANA_CON38_R_GAIN_COMP_10K_POS    (0) //for R, 3:0, 4bit, SE+HP/LP/ULP = 0010, other = 0000
#define AUDENC_ANA_CON38_R_GAIN_COMP_10K_MASK   (0xF<<AUDENC_ANA_CON38_R_GAIN_COMP_10K_POS)

#define AUDENC_ANA_CON39                (ABB_BASE + 0x29C)
#define AUDENC_ANA_CON39_L_ACC_SE_EN_POS        (5) //for L, SE = 1
#define AUDENC_ANA_CON39_L_ACC_SE_EN_MASK       (1<<AUDENC_ANA_CON39_L_ACC_SE_EN_POS)
#define AUDENC_ANA_CON39_L_GAIN_COMP_10K_POS    (8) //11:8,for L, SE&10K = 0010, other = 0000
#define AUDENC_ANA_CON39_L_GAIN_COMP_10K_MASK   (0xF<<AUDENC_ANA_CON39_L_GAIN_COMP_10K_POS)
#define AUDENC_ANA_CON39_L_GAIN_COMP_20K_POS    (12) //15:12,for L, SE&20K = 0010, other = 0000
#define AUDENC_ANA_CON39_L_GAIN_COMP_20K_MASK   (0xF<<AUDENC_ANA_CON39_L_GAIN_COMP_20K_POS)

#define AUDENC_ANA_CON40                (ABB_BASE + 0x2A0) //ADC23 L&R
#define AUDENC_ANA_CON40_PREAMP_L_REV_POS    (2) //for L, 3:2, all 01
#define AUDENC_ANA_CON40_PREAMP_L_REV_MASK   (3<<AUDENC_ANA_CON40_PREAMP_L_REV_POS)
#define AUDENC_ANA_CON40_R_ACC_SE_EN_POS     (13) //for R
#define AUDENC_ANA_CON40_R_ACC_SE_EN_MASK    (1<<AUDENC_ANA_CON40_R_ACC_SE_EN_POS)

#define AUDENC_ANA_CON41                (ABB_BASE + 0x2A4)
#define AUDENC_ANA_CON41_PREAMP_R_REV_POS       (10) //for R, 11:10, all 01
#define AUDENC_ANA_CON41_PREAMP_R_REV_MASK      (3<<AUDENC_ANA_CON41_PREAMP_R_REV_POS)
#define AUDENC_ANA_CON41_R_GAIN_COMP_20K_POS    (4) //for R, 7:4, 4bit, SE&20K = 0010, other = 0000
#define AUDENC_ANA_CON41_R_GAIN_COMP_20K_MASK   (0xF<<AUDENC_ANA_CON41_R_GAIN_COMP_20K_POS)
#define AUDENC_ANA_CON41_R_GAIN_COMP_10K_POS    (0) //for R, 3:0, 4bit, SE+HP/LP/ULP = 0010, other = 0000
#define AUDENC_ANA_CON41_R_GAIN_COMP_10K_MASK   (0xF<<AUDENC_ANA_CON41_R_GAIN_COMP_10K_POS)

#define AUDENC_ANA_CON42                (ABB_BASE + 0x2A8)
#define AUDENC_ANA_CON43                (ABB_BASE + 0x2AC)
#define AUDENC_ANA_CON44                (ABB_BASE + 0x2B0)
#define AUDENC_ANA_CON45                (ABB_BASE + 0x2B4)

#define VAD_ANA_CON0                   (ABB_BASE + 0x300)
#define VAD_ANA_CON1                   (ABB_BASE + 0x304)
#define VAD_ANA_CON2                   (ABB_BASE + 0x308)
#define VAD_ANA_CON3                   (ABB_BASE + 0x30C)
#define VAD_ANA_CON4                   (ABB_BASE + 0x310)

#define AUXADC_ANA_CON0                (ABB_BASE + 0x400)
#define AUXADC_ANA_CON1                (ABB_BASE + 0x404)

#define PMU2_ANA_CON0                  (ABB_BASE + 0x500)
#define PMU2_ANA_CON1                  (ABB_BASE + 0x504)
#define PMU2_ANA_RO                    (ABB_BASE + 0x510)


#define DMIC_CLK_SEL_0                  (ABB_CLK_BASE + 0x0700)
#define DMIC_CLK_SEL_0_DMIC0_POS        (0)
#define DMIC_CLK_SEL_0_DMIC0_MASK       (1<<DMIC_CLK_SEL_0_DMIC0_POS)
#define DMIC_CLK_SEL_0_DMIC1_POS        (8)
#define DMIC_CLK_SEL_0_DMIC1_MASK       (1<<DMIC_CLK_SEL_0_DMIC1_POS)
#define DMIC_CLK_SEL_0_DMIC2_POS        (16)
#define DMIC_CLK_SEL_0_DMIC2_MASK       (1<<DMIC_CLK_SEL_0_DMIC2_POS)
#define DMIC_CLK_SEL_0_DMIC3_POS        (24)
#define DMIC_CLK_SEL_0_DMIC3_MASK       (1<<DMIC_CLK_SEL_0_DMIC3_POS)

#define DMIC_CLK_SEL_1                  (ABB_CLK_BASE + 0x0704)
#define DMIC_CLK_SEL_1_DMIC4_POS        (0)
#define DMIC_CLK_SEL_1_DMIC4_MASK       (1<<DMIC_CLK_SEL_1_DMIC4_POS)
#define DMIC_CLK_SEL_1_DMIC5_POS        (8)
#define DMIC_CLK_SEL_1_DMIC5_MASK       (1<<DMIC_CLK_SEL_1_DMIC5_POS)
#define DMIC_CLK_SEL_1_GPIO_DMIC0_POS   (16)
#define DMIC_CLK_SEL_1_GPIO_DMIC0_MASK  (1<<DMIC_CLK_SEL_1_GPIO_DMIC0_POS)
#define DMIC_CLK_SEL_1_GPIO_DMIC1_POS   (24)
#define DMIC_CLK_SEL_1_GPIO_DMIC1_MASK  (1<<DMIC_CLK_SEL_1_GPIO_DMIC1_POS)

#define ABB_CLK_GEN_CFG_1               (ABB_CLK_BASE + 0x0714)
#define ABB_CLK_GEN_CFG_1_DIV_EN_POS                (0)
#define ABB_CLK_GEN_CFG_1_DIV_EN_MASK               (1<<ABB_CLK_GEN_CFG_1_DIV_EN_POS)
#define ABB_CLK_GEN_CFG_1_DIV_CHANGE_POS            (8)
#define ABB_CLK_GEN_CFG_1_DIV_CHANGE_MASK           (1<<ABB_CLK_GEN_CFG_1_DIV_CHANGE_POS)
#define ABB_CLK_GEN_CFG_1_DIV_SEL_POS               (16)
#define ABB_CLK_GEN_CFG_1_DIV_SEL_MASK              (0x7FFF<<ABB_CLK_GEN_CFG_1_DIV_SEL_POS)
#define ABB_CLK_GEN_CFG_2               (ABB_CLK_BASE + 0x0718)
#define ABB_CLK_GEN_CFG_2_DIV_EN_POS                (0)
#define ABB_CLK_GEN_CFG_2_DIV_EN_MASK               (1<<ABB_CLK_GEN_CFG_2_DIV_EN_POS)
#define ABB_CLK_GEN_CFG_2_DIV_CHANGE_POS            (8)
#define ABB_CLK_GEN_CFG_2_DIV_CHANGE_MASK           (1<<ABB_CLK_GEN_CFG_2_DIV_CHANGE_POS)
#define ABB_CLK_GEN_CFG_2_DIV_SEL_POS               (16)
#define ABB_CLK_GEN_CFG_2_DIV_SEL_MASK              (0x7FFF<<ABB_CLK_GEN_CFG_2_DIV_SEL_POS)
#define ABB_CLK_GEN_CFG_3               (ABB_CLK_BASE + 0x071C)
#define ABB_CLK_GEN_CFG_3_DIV_EN_POS                (0)
#define ABB_CLK_GEN_CFG_3_DIV_EN_MASK               (1<<ABB_CLK_GEN_CFG_3_DIV_EN_POS)
#define ABB_CLK_GEN_CFG_3_DIV_CHANGE_POS            (8)
#define ABB_CLK_GEN_CFG_3_DIV_CHANGE_MASK           (1<<ABB_CLK_GEN_CFG_3_DIV_CHANGE_POS)
#define ABB_CLK_GEN_CFG_3_DIV_SEL_POS               (16)
#define ABB_CLK_GEN_CFG_3_DIV_SEL_MASK              (0x7FFF<<ABB_CLK_GEN_CFG_3_DIV_SEL_POS)
#define ABB_CLK_GEN_CFG_4               (ABB_CLK_BASE + 0x0720)
#define ABB_CLK_GEN_CFG_4_DIV_EN_POS                (0)
#define ABB_CLK_GEN_CFG_4_DIV_EN_MASK               (1<<ABB_CLK_GEN_CFG_4_DIV_EN_POS)
#define ABB_CLK_GEN_CFG_4_DIV_CHANGE_POS            (8)
#define ABB_CLK_GEN_CFG_4_DIV_CHANGE_MASK           (1<<ABB_CLK_GEN_CFG_4_DIV_CHANGE_POS)
#define ABB_CLK_GEN_CFG_4_DIV_SEL_POS               (16)
#define ABB_CLK_GEN_CFG_4_DIV_SEL_MASK              (0x7FFF<<ABB_CLK_GEN_CFG_4_DIV_SEL_POS)
#define ABB_CLK_GEN_CFG_5               (ABB_CLK_BASE + 0x0724)
#define ABB_CLK_GEN_CFG_5_DIV_EN_POS                (0)
#define ABB_CLK_GEN_CFG_5_DIV_EN_MASK               (1<<ABB_CLK_GEN_CFG_4_DIV_EN_POS)
#define ABB_CLK_GEN_CFG_5_DIV_CHANGE_POS            (8)
#define ABB_CLK_GEN_CFG_5_DIV_CHANGE_MASK           (1<<ABB_CLK_GEN_CFG_4_DIV_CHANGE_POS)
#define ABB_CLK_GEN_CFG_5_DIV_SEL_POS               (16)
#define ABB_CLK_GEN_CFG_5_DIV_SEL_MASK              (0x7FFF<<ABB_CLK_GEN_CFG_4_DIV_SEL_POS)
#define ABB_CLK_GEN_CFG_6               (ABB_CLK_BASE + 0x0728)
#define ABB_CLK_GEN_CFG_6_DIV_EN_POS                (0)
#define ABB_CLK_GEN_CFG_6_DIV_EN_MASK               (1<<ABB_CLK_GEN_CFG_6_DIV_EN_POS)
#define ABB_CLK_GEN_CFG_6_DIV_CHANGE_POS            (8)
#define ABB_CLK_GEN_CFG_6_DIV_CHANGE_MASK           (1<<ABB_CLK_GEN_CFG_6_DIV_CHANGE_POS)
#define ABB_CLK_GEN_CFG_6_DIV_SEL_POS               (16)
#define ABB_CLK_GEN_CFG_6_DIV_SEL_MASK              (0x7FFF<<ABB_CLK_GEN_CFG_6_DIV_SEL_POS)
#define ABB_CLK_GEN_CFG_7               (ABB_CLK_BASE + 0x072C)
#define ABB_CLK_GEN_CFG_7_DIV_EN_POS                (0)
#define ABB_CLK_GEN_CFG_7_DIV_EN_MASK               (1<<ABB_CLK_GEN_CFG_7_DIV_EN_POS)
#define ABB_CLK_GEN_CFG_7_DIV_CHANGE_POS            (8)
#define ABB_CLK_GEN_CFG_7_DIV_CHANGE_MASK           (1<<ABB_CLK_GEN_CFG_7_DIV_CHANGE_POS)
#define ABB_CLK_GEN_CFG_7_DIV_SEL_POS               (16)
#define ABB_CLK_GEN_CFG_7_DIV_SEL_MASK              (0x7FFF<<ABB_CLK_GEN_CFG_7_DIV_SEL_POS)

/*****************************************************************************
 *           P L L      R E G I S T E R      D E F I N I T I O N
 *****************************************************************************/
#define APLL1_CTL0__F_RG_APLL1_DDS_PWR_ON               (XPLL_BASE + 0x0000)
#define APPL1_CTL0__F_RG_APLL1_DDS_ISO_EN               (XPLL_BASE + 0x0001)
#define APLL1_CTL0__F_RG_APLL1_V2I_EN                   (XPLL_BASE + 0x0003)
#define APLL1_CTL1__F_RG_APLL1_EN                       (XPLL_BASE + 0x0004)
#define APLL1_CTL11__F_RG_APLL1_LCDDS_PWDB              (XPLL_BASE + 0x002C)

#define APLL1_CTL10__F_RG_APLL1_LCDDS_TUNER_PCW_NCPO    (XPLL_BASE + 0x0028)
#define APLL1_CTL14__F_RG_APLL1_LCDDS_TUNER_PCW_NCPO    (XPLL_BASE + 0x0038)
#define APLL1_CTL12__F_RG_APLL1_LCDDS_TUNER_PCW_NCPO    (XPLL_BASE + 0x0030)
#define APLL1_CTL13__F_RG_APLL1_LCDDS_TUNER_EN          (XPLL_BASE + 0x0034)

#define APLL2_CTL0__F_RG_APLL2_DDS_PWR_ON               (XPLL_BASE + 0x0100)
#define APPL2_CTL0__F_RG_APLL2_DDS_ISO_EN               (XPLL_BASE + 0x0101)
#define APLL2_CTL0__F_RG_APLL2_V2I_EN                   (XPLL_BASE + 0x0103)
#define APLL2_CTL1__F_RG_APLL2_EN                       (XPLL_BASE + 0x0104)
#define APLL2_CTL11__F_RG_APLL2_LCDDS_PWDB              (XPLL_BASE + 0x012C)

#define APLL2_CTL10__F_RG_APLL2_LCDDS_TUNER_PCW_NCPO    (XPLL_BASE + 0x0128)
#define APLL2_CTL14__F_RG_APLL2_LCDDS_TUNER_PCW_NCPO    (XPLL_BASE + 0x0138)
#define APLL2_CTL12__F_RG_APLL2_LCDDS_TUNER_PCW_NCPO    (XPLL_BASE + 0x0130)
#define APLL2_CTL13__F_RG_APLL2_LCDDS_TUNER_EN          (XPLL_BASE + 0x0134)




/*****************************************************************************
 *           A N C      R E G I S T E R      D E F I N I T I O N
 *****************************************************************************/
/* Define in anc header. */


/*****************************************************************************
 *       VOICE     ACTIVITY     DETECTION     REGISTER     DEFINITION
 *****************************************************************************/

#define VAD_CTL0                        (VAD_BASE + 0x0000)
#define VAD_CTL0_EN_VAD_DIG_POS                         (0)
#define VAD_CTL0_EN_VAD_DIG_MASK                        (1<<VAD_CTL0_EN_VAD_DIG_POS)
#define VAD_CTL1                        (VAD_BASE + 0x0004)
#define VAD_CTL2                        (VAD_BASE + 0x0008)
#define VAD_CTL3                        (VAD_BASE + 0x000C)
#define VAD_CTL3_RST_MODE_POS                           (16)
#define VAD_CTL3_RST_MODE_MASK                          (1<<VAD_CTL3_RST_MODE_POS)
#define VAD_CTL4                        (VAD_BASE + 0x0010)
#define VAD_CTL5                        (VAD_BASE + 0x0014)
#define VAD_CTL5_MASK_THRES_POS                         (0)
#define VAD_CTL5_MASK_THRES_MASK                        (0xF<<VAD_CTL5_MASK_THRES_POS)
#define VAD_CTL5_MASK_IRQ_POS                           (16)
#define VAD_CTL5_MASK_IRQ_MASK                          (1<<VAD_CTL5_MASK_IRQ_POS)
#define VAD_DBG_CTL                     (VAD_BASE + 0x0018)
#define VAD_DBG_CTL_DBG_PORT_SEL_POS                    (16)
#define VAD_DBG_CTL_DBG_PORT_SEL_MASK                   (0xF<<VAD_DBG_CTL_DBG_PORT_SEL_POS)
#define VAD_DBG_CTL_DBG_LATCH_POS                       (24)
#define VAD_DBG_CTL_DBG_LATCH_MASK                      (1<<VAD_DBG_CTL_DBG_LATCH_POS)
#define VAD_DBG_MON                     (VAD_BASE + 0x001C)
#define VAD_CTL6                        (VAD_BASE + 0x0020)
#define VAD_CTL7                        (VAD_BASE + 0x0024)
#define VAD_CTL7_MASK_THRE1_POS                         (0)
#define VAD_CTL7_MASK_THRE1_MASK                        (0xFFF<<VAD_CTL7_MASK_THRE1_POS)


/*****************************************************************************
 *         A U X A D C      R E G I S T E R      D E F I N I T I O N
 *****************************************************************************/

#define AUXADC_DATA8                    (AUXADC_BASE + 0x30)

/*****************************************************************************
 *      I 2 S     S L A V E     R E G I S T E R      D E F I N I T I O N
 *****************************************************************************/
#define I2S_GLOBAL_CONTROL_OFFSET       (0x0000)
#define I2S_DL_CONTROL_OFFSET           (0x0004)
#define I2S_DL_CONTROL_WLEN_POS                         (1)
#define I2S_DL_CONTROL_WLEN_MASK                        (1<<I2S_DL_CONTROL_WLEN_POS)
#define I2S_DL_CONTROL_MODE_POS                         (2)
#define I2S_DL_CONTROL_MODE_MASK                        (1<<I2S_DL_CONTROL_MODE_POS)
#define I2S_DL_CONTROL_FMT_POS                          (3)
#define I2S_DL_CONTROL_FMT_MASK                         (1<<I2S_DL_CONTROL_FMT_POS)
#define I2S_DL_CONTROL_WS_INV_POS                       (5)
#define I2S_DL_CONTROL_WS_INV_MASK                      (1<<I2S_DL_CONTROL_WS_INV_POS)
#define I2S_DL_CONTROL_EQ_MODE_POS                      (7)
#define I2S_DL_CONTROL_EQ_MODE_MASK                     (1<<I2S_DL_CONTROL_EQ_MODE_POS)
#define I2S_DL_CONTROL_VALID_24BIT_POS                  (13)
#define I2S_DL_CONTROL_VALID_24BIT_MASK                 (1<<I2S_DL_CONTROL_VALID_24BIT_POS)
#define I2S_DL_CONTROL_RJ_POS                           (14)
#define I2S_DL_CONTROL_RJ_MASK                          (1<<I2S_DL_CONTROL_RJ_POS)
#define I2S_DL_CONTROL_INTERCONN_POS                    (16)
#define I2S_DL_CONTROL_INTERCONN_MASK                   (1<<I2S_DL_CONTROL_INTERCONN_POS)
#define I2S_DL_CONTROL_COUPLE_POS                       (17)
#define I2S_DL_CONTROL_COUPLE_MASK                      (1<<I2S_DL_CONTROL_COUPLE_POS)
#define I2S_DL_CONTROL_IN_WS_INV_POS                    (26)
#define I2S_DL_CONTROL_IN_WS_INV_MASK                   (1<<I2S_DL_CONTROL_IN_WS_INV_POS)
#define I2S_DL_CONTROL_IN_CK_INV_POS                    (27)
#define I2S_DL_CONTROL_IN_CK_INV_MASK                   (1<<I2S_DL_CONTROL_IN_CK_INV_POS)
#define I2S_DL_CONTROL_RST_POS                          (30)
#define I2S_DL_CONTROL_RST_MASK                         (1<<I2S_DL_CONTROL_RST_POS)
#define I2S_UL_CONTROL_OFFSET           (0x0008)
#define I2S_UL_CONTROL_WLEN_POS                         (1)
#define I2S_UL_CONTROL_WLEN_MASK                        (1<<I2S_UL_CONTROL_WLEN_POS)
#define I2S_UL_CONTROL_MODE_POS                         (2)
#define I2S_UL_CONTROL_MODE_MASK                        (1<<I2S_UL_CONTROL_MODE_POS)
#define I2S_UL_CONTROL_FMT_POS                          (3)
#define I2S_UL_CONTROL_FMT_MASK                         (1<<I2S_UL_CONTROL_FMT_POS)
#define I2S_UL_CONTROL_WS_INV_POS                       (5)
#define I2S_UL_CONTROL_WS_INV_MASK                      (1<<I2S_UL_CONTROL_WS_INV_POS)
#define I2S_UL_CONTROL_POS_EDGE_POS                     (6)
#define I2S_UL_CONTROL_POS_EDGE_MASK                    (1<<I2S_UL_CONTROL_POS_EDGE_POS)
#define I2S_UL_CONTROL_VALID_24BIT_POS                  (13)
#define I2S_UL_CONTROL_VALID_24BIT_MASK                 (1<<I2S_UL_CONTROL_VALID_24BIT_POS)
#define I2S_UL_CONTROL_RJ_POS                           (14)
#define I2S_UL_CONTROL_RJ_MASK                          (1<<I2S_UL_CONTROL_RJ_POS)
#define I2S_UL_CONTROL_LOW_BIT_ZERO_POS                 (15)
#define I2S_UL_CONTROL_LOW_BIT_ZERO_MASK                (1<<I2S_UL_CONTROL_LOW_BIT_ZERO_POS)
#define I2S_UL_CONTROL_COUPLE_POS                       (17)
#define I2S_UL_CONTROL_COUPLE_MASK                      (1<<I2S_UL_CONTROL_COUPLE_POS)
#define I2S_UL_CONTROL_IN_WS_INV_POS                    (26)
#define I2S_UL_CONTROL_IN_WS_INV_MASK                   (1<<I2S_UL_CONTROL_IN_WS_INV_POS)
#define I2S_UL_CONTROL_IN_CK_INV_POS                    (27)
#define I2S_UL_CONTROL_IN_CK_INV_MASK                   (1<<I2S_UL_CONTROL_IN_CK_INV_POS)
#define I2S_UL_CONTROL_RST_POS                          (30)
#define I2S_UL_CONTROL_RST_MASK                         (1<<I2S_UL_CONTROL_RST_POS)


#define I2S_SOFT_RESET_OFFSET           (0x000C)
#define I2S_DL_FIFO_OFFSET              (0x0010)
#define I2S_UL_FIFO_OFFSET              (0x0014)
#define I2S_DL_FIFO_STATUS_OFFSET       (0x0018)
#define I2S_UL_FIFO_STATUS_OFFSET       (0x001c)
#define I2S_SCAN_RSV_OFFSET             (0x0020)
#define I2S_GLOBAL_EN_CONTROL_OFFSET    (0x0030)
#define I2S_GLOBAL_EN_CONTROL_ENABLE_POS                (0)
#define I2S_GLOBAL_EN_CONTROL_ENABLE_MASK               (1<<I2S_GLOBAL_EN_CONTROL_ENABLE_POS)
#define I2S_GLOBAL_EN_CONTROL_DL_FIFO_EN_POS            (8)
#define I2S_GLOBAL_EN_CONTROL_DL_FIFO_EN_MASK           (1<<I2S_GLOBAL_EN_CONTROL_DL_FIFO_EN_POS)
#define I2S_GLOBAL_EN_CONTROL_UL_FIFO_EN_POS            (16)
#define I2S_GLOBAL_EN_CONTROL_UL_FIFO_EN_MASK           (1<<I2S_GLOBAL_EN_CONTROL_UL_FIFO_EN_POS)
#define I2S_GLOBAL_EN_CONTROL_PDN_POS                   (24)
#define I2S_GLOBAL_EN_CONTROL_PDN_MASK                  (1<<I2S_GLOBAL_EN_CONTROL_PDN_POS)
#define I2S_DL_SR_EN_CONTROL_OFFSET     (0x0034)
#define I2S_DL_SR_EN_CONTROL_DL_EN_POS                  (0)
#define I2S_DL_SR_EN_CONTROL_DL_EN_MASK                 (1<<I2S_DL_SR_EN_CONTROL_DL_EN_POS)
#define I2S_DL_SR_EN_CONTROL_DL_RATE_POS                (8)
#define I2S_DL_SR_EN_CONTROL_DL_RATE_MASK               (0x1F<<I2S_DL_SR_EN_CONTROL_DL_RATE_POS)
#define I2S_DL_SR_EN_CONTROL_DL_PDN_POS                 (16)
#define I2S_DL_SR_EN_CONTROL_DL_PDN_MASK                (1<<I2S_DL_SR_EN_CONTROL_DL_PDN_POS)
#define I2S_UL_SR_EN_CONTROL_OFFSET     (0x0038)
#define I2S_UL_SR_EN_CONTROL_UL_EN_POS                  (0)
#define I2S_UL_SR_EN_CONTROL_UL_EN_MASK                 (1<<I2S_UL_SR_EN_CONTROL_UL_EN_POS)
#define I2S_UL_SR_EN_CONTROL_UL_RATE_POS                (8)
#define I2S_UL_SR_EN_CONTROL_UL_RATE_MASK               (0x1F<<I2S_UL_SR_EN_CONTROL_UL_RATE_POS)
#define I2S_UL_SR_EN_CONTROL_UL_PDN_POS                 (16)
#define I2S_UL_SR_EN_CONTROL_UL_PDN_MASK                (1<<I2S_UL_SR_EN_CONTROL_UL_PDN_POS)
#define I2S_MONITOR_OFFSET              (0x003c)
#define I2S_DL_INT_CONTROL_OFFSET       (0x0040)
#define I2S_UL_INT_CONTROL_OFFSET       (0x0044)
#define I2S_INT_ACK_CONTROL_OFFSET      (0x0048)
#define I2S_SHARE_CK_CONTROL_OFFSET     (0x0050)
#define I2S_PLAY_EN_CONTROL_OFFSET      (0x0054)
#define I2S_INIT_DELAY_CNT_MON_OFFSET   (0x0058)
#define I2S_SHARE_EN_CONTROL_OFFSET     (0x005c)
#define I2S_DL_TDM_MODE_OFFSET          (0x0090)
#define I2S_DL_TDM_SETTING_OFFSET       (0x0094)
#define I2S_DL_TDM_STATUS_OFFSET        (0x0098)
#define I2S_UL_TDM_MODE_OFFSET          (0x009c)
#define I2S_UL_TDM_SETTING_OFFSET       (0x00a0)
#define I2S_UL_TDM_STATUS_OFFSET        (0x00a4)

/*****************************************************************************
 *       VOICE     WAKEUP     REGISTER     DEFINITION
 *****************************************************************************/

#define    AFE_VOW_TOP_CON0        (VOW_CTRL_BASE + 0x0000)
#define    AFE_VOW_TOP_CON1        (VOW_CTRL_BASE + 0x0004)
#define    AFE_VOW_TOP_CON2        (VOW_CTRL_BASE + 0x0008)
#define    AFE_VOW_TOP_CON3        (VOW_CTRL_BASE + 0x000C)
#define    AFE_VOW_TOP_CON4        (VOW_CTRL_BASE + 0x0010)
#define    AFE_VOW_TOP_MON0        (VOW_CTRL_BASE + 0x0014)
#define    AFE_VOW_VAD_CFG0        (VOW_CTRL_BASE + 0x0018)
#define    AFE_VOW_VAD_CFG1        (VOW_CTRL_BASE + 0x001C)
#define    AFE_VOW_VAD_CFG2        (VOW_CTRL_BASE + 0x0020)
#define    AFE_VOW_VAD_CFG3        (VOW_CTRL_BASE + 0x0024)
#define    AFE_VOW_VAD_CFG4        (VOW_CTRL_BASE + 0x0028)
#define    AFE_VOW_VAD_CFG5        (VOW_CTRL_BASE + 0x002C)
#define    AFE_VOW_VAD_CFG6        (VOW_CTRL_BASE + 0x0030)
#define    AFE_VOW_VAD_CFG6_K_ALPHA_RISE_CH1_POS  (4)
#define    AFE_VOW_VAD_CFG6_K_ALPHA_RISE_CH1_MASK (0xF<<AFE_VOW_VAD_CFG6_K_ALPHA_RISE_CH1_POS)
#define    AFE_VOW_VAD_CFG7        (VOW_CTRL_BASE + 0x0034)
#define    AFE_VOW_VAD_CFG7_K_ALPHA_RISE_CH2_POS  (4)
#define    AFE_VOW_VAD_CFG7_K_ALPHA_RISE_CH2_MASK (0xF<<AFE_VOW_VAD_CFG7_K_ALPHA_RISE_CH2_POS)
#define    AFE_VOW_VAD_CFG8        (VOW_CTRL_BASE + 0x0038)
#define    AFE_VOW_VAD_CFG9        (VOW_CTRL_BASE + 0x003C)
#define    AFE_VOW_VAD_CFG10        (VOW_CTRL_BASE + 0x0040)
#define    AFE_VOW_VAD_CFG11        (VOW_CTRL_BASE + 0x0044)
#define    AFE_VOW_VAD_CFG12        (VOW_CTRL_BASE + 0x0048)
#define    AFE_VOW_VAD_MON0        (VOW_CTRL_BASE + 0x004C)
#define    AFE_VOW_VAD_MON1        (VOW_CTRL_BASE + 0x0050)
#define    AFE_VOW_VAD_MON2        (VOW_CTRL_BASE + 0x0054)
#define    AFE_VOW_VAD_MON3        (VOW_CTRL_BASE + 0x0058)
#define    AFE_VOW_VAD_MON4        (VOW_CTRL_BASE + 0x005C)
#define    AFE_VOW_VAD_MON5        (VOW_CTRL_BASE + 0x0060)
#define    AFE_VOW_VAD_MON6        (VOW_CTRL_BASE + 0x0064)
#define    AFE_VOW_VAD_MON7        (VOW_CTRL_BASE + 0x0068)
#define    AFE_VOW_VAD_MON8        (VOW_CTRL_BASE + 0x006C)
#define    AFE_VOW_VAD_MON9        (VOW_CTRL_BASE + 0x0070)
#define    AFE_VOW_VAD_MON10        (VOW_CTRL_BASE + 0x0074)
#define    AFE_VOW_VAD_MON11        (VOW_CTRL_BASE + 0x0078)
#define    AFE_VOW_TGEN_CFG0        (VOW_CTRL_BASE + 0x007C)
#define    AFE_VOW_TGEN_CFG1        (VOW_CTRL_BASE + 0x0080)
#define    AFE_VOW_HPF_CFG0        (VOW_CTRL_BASE + 0x0084)
#define    AFE_VOW_HPF_CFG1        (VOW_CTRL_BASE + 0x0088)
#define    AFE_VOW_INTR_CLR        (VOW_CTRL_BASE + 0x008C)
#define    AFE_VOW_DMIC_CK_CON     (VOW_CTRL_BASE + 0x0090)
#define    AFE_VOW_DMIC_SEL        (VOW_CTRL_BASE + 0x0094)
#define    AFE_VOW_TOP_CON5        (VOW_CTRL_BASE + 0x0098)
#define    AFE_VOW_DEG_CON0        (VOW_CTRL_BASE + 0x009C)
#define    AFE_VOW_TOP_CON6        (VOW_CTRL_BASE + 0x00A0)

#define AFE_VOW_FIR_CON0          (VOW_CTRL_BASE + 0x00A4)
#define AFE_VOW_OBUF_BASE_ADDR    (VOW_CTRL_BASE + 0x00A8)
#define AFE_VOW_OBUF_END_ADDR     (VOW_CTRL_BASE + 0x00AC)
#define AFE_VOW_OBUF_PREROLL_CON  (VOW_CTRL_BASE + 0x00B0)
#define AFE_VOW_OBUF_KEYWORD_CON  (VOW_CTRL_BASE + 0x00B4)
#define AFE_VOW_DMA_IRQ_MASK      (VOW_CTRL_BASE + 0x00B8)
#define AFE_VOW_OBUF_WPTR         (VOW_CTRL_BASE + 0x00BC)
#define AFE_VOW_OBUF_KEYWORD_OFS  (VOW_CTRL_BASE + 0x00C0)
#define AFE_VOW_OUTPUT_SMP_CNT    (VOW_CTRL_BASE + 0x00C4)
#define AFE_VOW_OUTPUT_SMP_CNT_CLR (VOW_CTRL_BASE + 0x00C8)
#define AFE_VOW_OUTPUT_IRQ_CNT    (VOW_CTRL_BASE + 0x00CC)
#define AFE_VOW_MEM_INTF_MON0     (VOW_CTRL_BASE + 0x00D0)
#define AFE_VOW_MEM_INTF_MON1     (VOW_CTRL_BASE + 0x00D4)
#define AFE_VOW_FIR_COEF0         (VOW_CTRL_BASE + 0x0400)
#define AFE_VOW_FIR_COEF1         (VOW_CTRL_BASE + 0x0404)
#define AFE_VOW_FIR_COEF2         (VOW_CTRL_BASE + 0x0408)
#define AFE_VOW_FIR_COEF3         (VOW_CTRL_BASE + 0x040C)
#define AFE_VOW_FIR_COEF4         (VOW_CTRL_BASE + 0x0410)
#define AFE_VOW_FIR_COEF5         (VOW_CTRL_BASE + 0x0414)
#define AFE_VOW_FIR_COEF6         (VOW_CTRL_BASE + 0x0418)
#define AFE_VOW_FIR_COEF7         (VOW_CTRL_BASE + 0x041C)
#define AFE_VOW_FIR_COEF8         (VOW_CTRL_BASE + 0x0420)
#define AFE_VOW_FIR_COEF9         (VOW_CTRL_BASE + 0x0424)
#define AFE_VOW_FIR_COEF10        (VOW_CTRL_BASE + 0x0428)
#define AFE_VOW_FIR_COEF11        (VOW_CTRL_BASE + 0x042C)
#define AFE_VOW_FIR_COEF12        (VOW_CTRL_BASE + 0x0430)
#define AFE_VOW_FIR_COEF13        (VOW_CTRL_BASE + 0x0434)
#define AFE_VOW_FIR_COEF14        (VOW_CTRL_BASE + 0x0438)
#define AFE_VOW_FIR_COEF15        (VOW_CTRL_BASE + 0x043C)
#define AFE_VOW_FIR_COEF16        (VOW_CTRL_BASE + 0x0440)
#define AFE_VOW_FIR_COEF17        (VOW_CTRL_BASE + 0x0444)
#define AFE_VOW_FIR_COEF18        (VOW_CTRL_BASE + 0x0448)
#define AFE_VOW_FIR_COEF19        (VOW_CTRL_BASE + 0x044C)
#define AFE_VOW_FIR_COEF20        (VOW_CTRL_BASE + 0x0450)
#define AFE_VOW_FIR_COEF21        (VOW_CTRL_BASE + 0x0454)
#define AFE_VOW_FIR_COEF22        (VOW_CTRL_BASE + 0x0458)
#define AFE_VOW_FIR_COEF23        (VOW_CTRL_BASE + 0x045C)
#define AFE_VOW_FIR_COEF24        (VOW_CTRL_BASE + 0x0460)
#define AFE_VOW_FIR_COEF25        (VOW_CTRL_BASE + 0x0464)
#define AFE_VOW_FIR_COEF26        (VOW_CTRL_BASE + 0x0468)
#define AFE_VOW_FIR_COEF27        (VOW_CTRL_BASE + 0x046C)
#define AFE_VOW_FIR_COEF28        (VOW_CTRL_BASE + 0x0470)
#define AFE_VOW_FIR_COEF29        (VOW_CTRL_BASE + 0x0474)
#define AFE_VOW_FIR_COEF30        (VOW_CTRL_BASE + 0x0478)
#define AFE_VOW_FIR_COEF31        (VOW_CTRL_BASE + 0x047C)
#define AFE_VOW_FIR_COEF32        (VOW_CTRL_BASE + 0x0480)
#define AFE_VOW_FIR_COEF33        (VOW_CTRL_BASE + 0x0484)
#define AFE_VOW_FIR_COEF34        (VOW_CTRL_BASE + 0x0488)
#define AFE_VOW_FIR_COEF35        (VOW_CTRL_BASE + 0x048C)
#define AFE_VOW_FIR_COEF36        (VOW_CTRL_BASE + 0x0490)
#define AFE_VOW_FIR_COEF37        (VOW_CTRL_BASE + 0x0494)
#define AFE_VOW_FIR_COEF38        (VOW_CTRL_BASE + 0x0498)
#define AFE_VOW_FIR_COEF39        (VOW_CTRL_BASE + 0x049C)
#define AFE_VOW_FIR_COEF40        (VOW_CTRL_BASE + 0x04A0)
#define AFE_VOW_FIR_COEF41        (VOW_CTRL_BASE + 0x04A4)
#define AFE_VOW_FIR_COEF42        (VOW_CTRL_BASE + 0x04A8)
#define AFE_VOW_FIR_COEF43        (VOW_CTRL_BASE + 0x04AC)
#define AFE_VOW_FIR_COEF44        (VOW_CTRL_BASE + 0x04B0)
#define AFE_VOW_FIR_COEF45        (VOW_CTRL_BASE + 0x04B4)
#define AFE_VOW_FIR_COEF46        (VOW_CTRL_BASE + 0x04B8)
#define AFE_VOW_FIR_COEF47        (VOW_CTRL_BASE + 0x04BC)
#define AFE_VOW_FIR_COEF48        (VOW_CTRL_BASE + 0x04C0)
#define AFE_VOW_FIR_COEF49        (VOW_CTRL_BASE + 0x04C4)
#define AFE_VOW_FIR_COEF50        (VOW_CTRL_BASE + 0x04C8)
#define AFE_VOW_FIR_COEF51        (VOW_CTRL_BASE + 0x04CC)
#define AFE_VOW_FIR_COEF52        (VOW_CTRL_BASE + 0x04D0)
#define AFE_VOW_FIR_COEF53        (VOW_CTRL_BASE + 0x04D4)
#define AFE_VOW_FIR_COEF54        (VOW_CTRL_BASE + 0x04D8)
#define AFE_VOW_FIR_COEF55        (VOW_CTRL_BASE + 0x04DC)
#define AFE_VOW_FIR_COEF56        (VOW_CTRL_BASE + 0x04E0)
#define AFE_VOW_FIR_COEF57        (VOW_CTRL_BASE + 0x04E4)
#define AFE_VOW_FIR_COEF58        (VOW_CTRL_BASE + 0x04E8)
#define AFE_VOW_FIR_COEF59        (VOW_CTRL_BASE + 0x04EC)
#define AFE_VOW_FIR_COEF60        (VOW_CTRL_BASE + 0x04F0)
#define AFE_VOW_FIR_COEF61        (VOW_CTRL_BASE + 0x04F4)
#define AFE_VOW_FIR_COEF62        (VOW_CTRL_BASE + 0x04F8)
#define AFE_VOW_FIR_COEF63        (VOW_CTRL_BASE + 0x04FC)
#define AFE_VOW_FIR_COEF64        (VOW_CTRL_BASE + 0x0500)

#define    INFRA_MISC_CFG_BASE     0x422D0000
#define    NFRA_CFG_PERI2          (INFRA_MISC_CFG_BASE+0x0098)

#if 1
#define SPM_RG_BASE    (0x42140000)
#define INFRA_MISC_CFG 0x422D0000
#define SPM_VOW_SRAM_CONTROL_0                     ((volatile uint32_t*)(SPM_RG_BASE + 0x0560))
#define SPM_VOW_SRAM_CONTROL_1                     ((volatile uint32_t*)(SPM_RG_BASE + 0x0564))
#define INFRA_VOW_PROT_EN                          ((volatile uint32_t*)(INFRA_MISC_CFG + 0x80))  //prot_en for VOW
#define INFRA_VOW_PROT_RDY                         ((volatile uint32_t*)(INFRA_MISC_CFG + 0x84))  //prot_rdy for VOW
#endif
#endif /* __HAL_AUDIO_REGISTER_H__ */
