/* Copyright Statement:
 *
 * (C) 2018  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */

#ifndef __HAL_SW_DMA_INTERNAL_H__
#define __HAL_SW_DMA_INTERNAL_H__

#include "hal_sw_dma.h"

#ifdef HAL_SW_DMA_MODULE_ENABLED

#ifdef __cplusplus
extern "C" {
#endif

#define SW_DMA_CHANNEL_NUMBER       1
#define SW_DMA_CHANNEL_REG_BASE     GDMA_RG_1_1_BASE
#define SW_DMA_CHANNEL_IRQ_NUMBER   MCU_DMA1_IRQn
// #define SW_DMA_CHANNEL_CLOCK_CG     HAL_CLOCK_CG_FAST_DMA1

#define SW_DMA_IS_RUNNING()             (MCU_DMA_RG_GLB_STA & DMA_GLB_RUNNING_BIT_MASK(SW_DMA_CHANNEL_NUMBER))
#define SW_DMA_IS_TRIGGER_INTERRUPT()   (MCU_DMA_RG_GLB_STA & DMA_GLB_IRQ_STA_BIT_MASK(SW_DMA_CHANNEL_NUMBER))

void sw_dma_start_interrupt(hal_sw_dma_config_info_t *info);
void sw_dma_init(void);
void sw_dma_clear_irq(void);

#ifdef __cplusplus
}
#endif

#endif /* HAL_SW_DMA_MODULE_ENABLED */
#endif /* __HAL_SW_DMA_INTERNAL_H__ */

