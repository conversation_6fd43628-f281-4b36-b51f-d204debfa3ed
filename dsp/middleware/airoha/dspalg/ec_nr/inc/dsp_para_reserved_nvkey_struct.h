/* Copyright Statement:
 *
 * (C) 2022  Airoha Technology Corp. All rights reserved.
 *
 * This software/firmware and related documentation ("Airoha Software") are
 * protected under relevant copyright laws. The information contained herein
 * is confidential and proprietary to Airoha Technology Corp. ("Airoha") and/or its licensors.
 * Without the prior written permission of Airoha and/or its licensors,
 * any reproduction, modification, use or disclosure of Airoha Software,
 * and information contained herein, in whole or in part, shall be strictly prohibited.
 * You may only use, reproduce, modify, or distribute (as applicable) Airoha Software
 * if you have agreed to and been bound by the applicable license agreement with
 * Airoha ("License Agreement") and been granted explicit permission to do so within
 * the License Agreement ("Permitted User").  If you are not a Permitted User,
 * please cease any access or use of Airoha Software immediately.
 * BY OPENING THIS FILE, RECEIVER HEREBY UNEQUIVOCALLY ACKNOWLEDGES AND AGREES
 * THAT AIROHA SOFTWARE RECEIVED FROM AIROHA AND/OR ITS REPRESENTATIVES
 * ARE PROVIDED TO RECEIVER ON AN "AS-IS" BASIS ONLY. AIROHA EXPRESSLY DISCLAIMS ANY AND ALL
 * WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE OR NONINFRINGEMENT.
 * NEITHER DOES AIROHA PROVIDE ANY WARRANTY WHATSOEVER WITH RESPECT TO THE
 * SOFTWARE OF ANY THIRD PARTY WHICH MAY BE USED BY, INCORPORATED IN, OR
 * SUPPLIED WITH AIROHA SOFTWARE, AND RECEIVER AGREES TO LOOK ONLY TO SUCH
 * THIRD PARTY FOR ANY WARRANTY CLAIM RELATING THERETO. RECEIVER EXPRESSLY ACKNOWLEDGES
 * THAT IT IS RECEIVER'S SOLE RESPONSIBILITY TO OBTAIN FROM ANY THIRD PARTY ALL PROPER LICENSES
 * CONTAINED IN AIROHA SOFTWARE. AIROHA SHALL ALSO NOT BE RESPONSIBLE FOR ANY AIROHA
 * SOFTWARE RELEASES MADE TO RECEIVER'S SPECIFICATION OR TO CONFORM TO A PARTICULAR
 * STANDARD OR OPEN FORUM. RECEIVER'S SOLE AND EXCLUSIVE REMEDY AND AIROHA'S ENTIRE AND
 * CUMULATIVE LIABILITY WITH RESPECT TO AIROHA SOFTWARE RELEASED HEREUNDER WILL BE,
 * AT AIROHA'S OPTION, TO REVISE OR REPLACE AIROHA SOFTWARE AT ISSUE,
 * OR REFUND ANY SOFTWARE LICENSE FEES OR SERVICE CHARGE PAID BY RECEIVER TO
 * AIROHA FOR SUCH AIROHA SOFTWARE AT ISSUE.
 */
#ifndef _DSP_PARA_RESERVED_H_
#define _DSP_PARA_RESERVED_H_

#include "types.h"

/**
 * @brief Reserved DSP Control Parameter
 * @KeyID NVKEYID_DSP_FW_PARA_RESERVED 0xE007
 */
/*NvkeyDefine NVKEYID_DSP_FW_PARA_RESERVED*/
#ifdef AIR_AI_NR_PREMIUM_INEAR_270K_ENABLE

typedef struct DSP_PARA_RESERVED_s {
    S32 ReservedPara0;           /**< @Value 0   @Desc Reserved Para 0*/
    S32 ReservedPara1;           /**< @Value 0   @Desc Reserved Para 1*/
    S32 ReservedPara2;           /**< @Value 0   @Desc Reserved Para 2*/
    S32 ReservedPara3;           /**< @Value 0   @Desc Reserved Para 3*/
    S32 ReservedPara4;           /**< @Value 0   @Desc Reserved Para 4*/
    S32 ReservedPara5;           /**< @Value 0   @Desc Reserved Para 5*/
    S32 ReservedPara6;           /**< @Value 0   @Desc Reserved Para 6*/
    S32 ReservedPara7;           /**< @Value 0   @Desc Reserved Para 7*/
    S32 ReservedPara8;           /**< @Value 0   @Desc Reserved Para 8*/
    S32 ReservedPara9;           /**< @Value 0   @Desc Reserved Para 9*/
    S32 ReservedPara10;           /**< @Value 0   @Desc Reserved Para 10*/
    S32 ReservedPara11;           /**< @Value 0   @Desc Reserved Para 11*/
    S32 ReservedPara12;           /**< @Value 0   @Desc Reserved Para 12*/
    S32 ReservedPara13;           /**< @Value 0   @Desc Reserved Para 13*/
    S32 ReservedPara14;           /**< @Value 0   @Desc Reserved Para 14*/
    S32 ReservedPara15;           /**< @Value 0   @Desc Reserved Para 15*/
    S32 ReservedPara16;           /**< @Value 0   @Desc Reserved Para 16*/
    S32 ReservedPara17;           /**< @Value 0   @Desc Reserved Para 17*/
    S32 ReservedPara18;           /**< @Value 0   @Desc Reserved Para 18*/
    S32 ReservedPara19;           /**< @Value 0   @Desc Reserved Para 19*/
    S32 ReservedPara20;           /**< @Value 0   @Desc Reserved Para 20*/
    S32 ReservedPara21;           /**< @Value 0   @Desc Reserved Para 21*/
    S32 ReservedPara22;           /**< @Value 0   @Desc Reserved Para 22*/
    S32 ReservedPara23;           /**< @Value 0   @Desc Reserved Para 23*/
    S32 ReservedPara24;           /**< @Value 0   @Desc Reserved Para 24*/
    S32 ReservedPara25;           /**< @Value 0   @Desc Reserved Para 25*/
    S32 ReservedPara26;           /**< @Value 0   @Desc Reserved Para 26*/
    S32 ReservedPara27;           /**< @Value 0   @Desc Reserved Para 27*/
    S32 ReservedPara28;           /**< @Value 0   @Desc Reserved Para 28*/
    S32 ReservedPara29;           /**< @Value 0   @Desc Reserved Para 29*/
    S32 ReservedPara30;           /**< @Value 0   @Desc Reserved Para 30*/
    S32 ReservedPara31;           /**< @Value 0   @Desc Reserved Para 31*/
    S32 ReservedPara32;           /**< @Value 0   @Desc Reserved Para 32*/
    S32 ReservedPara33;           /**< @Value 0   @Desc Reserved Para 33*/
    S32 ReservedPara34;           /**< @Value 0   @Desc Reserved Para 34*/
    S32 ReservedPara35;           /**< @Value 0   @Desc Reserved Para 35*/
    S32 ReservedPara36;           /**< @Value 0   @Desc Reserved Para 36*/
    S32 ReservedPara37;           /**< @Value 0   @Desc Reserved Para 37*/
    S32 ReservedPara38;           /**< @Value 0   @Desc Reserved Para 38*/
    S32 ReservedPara39;           /**< @Value 0   @Desc Reserved Para 39*/
    S32 ReservedPara40;           /**< @Value 0   @Desc Reserved Para 40*/
    S32 ReservedPara41;           /**< @Value 0   @Desc Reserved Para 41*/
    S32 ReservedPara42;           /**< @Value 0   @Desc Reserved Para 42*/
    S32 ReservedPara43;           /**< @Value 0   @Desc Reserved Para 43*/
    S32 ReservedPara44;           /**< @Value 0   @Desc Reserved Para 44*/
    S32 ReservedPara45;           /**< @Value 0   @Desc Reserved Para 45*/
    S32 ReservedPara46;           /**< @Value 0   @Desc Reserved Para 46*/
    S32 ReservedPara47;           /**< @Value 0   @Desc Reserved Para 47*/
    S32 ReservedPara48;           /**< @Value 0   @Desc Reserved Para 48*/
    S32 ReservedPara49;           /**< @Value 0   @Desc Reserved Para 49*/
    S32 ReservedPara50;           /**< @Value 0   @Desc Reserved Para 50*/
    S32 ReservedPara51;           /**< @Value 0   @Desc Reserved Para 51*/
    S32 ReservedPara52;           /**< @Value 0   @Desc Reserved Para 52*/
    S32 ReservedPara53;           /**< @Value 0   @Desc Reserved Para 53*/
    S32 ReservedPara54;           /**< @Value 0   @Desc Reserved Para 54*/
    S32 ReservedPara55;           /**< @Value 0   @Desc Reserved Para 55*/
    S32 ReservedPara56;           /**< @Value 0   @Desc Reserved Para 56*/
    S32 ReservedPara57;           /**< @Value 0   @Desc Reserved Para 57*/
    S32 ReservedPara58;           /**< @Value 0   @Desc Reserved Para 58*/
    S32 ReservedPara59;           /**< @Value 0   @Desc Reserved Para 59*/
    S32 ReservedPara60;           /**< @Value 0   @Desc Reserved Para 60*/
    S32 ReservedPara61;           /**< @Value 0   @Desc Reserved Para 61*/
    S32 ReservedPara62;           /**< @Value 0   @Desc Reserved Para 62*/
    S32 ReservedPara63;           /**< @Value 0   @Desc Reserved Para 63*/
    S32 ReservedPara64;           /**< @Value 0   @Desc Reserved Para 64*/
    S32 ReservedPara65;           /**< @Value 0   @Desc Reserved Para 65*/
    S32 ReservedPara66;           /**< @Value 0   @Desc Reserved Para 66*/
    S32 ReservedPara67;           /**< @Value 0   @Desc Reserved Para 67*/
    S32 ReservedPara68;           /**< @Value 0   @Desc Reserved Para 68*/
    S32 ReservedPara69;           /**< @Value 0   @Desc Reserved Para 69*/
    S32 ReservedPara70;           /**< @Value 0   @Desc Reserved Para 70*/
    S32 ReservedPara71;           /**< @Value 0   @Desc Reserved Para 71*/
    S32 ReservedPara72;           /**< @Value 0   @Desc Reserved Para 72*/
    S32 ReservedPara73;           /**< @Value 0   @Desc Reserved Para 73*/
    S32 ReservedPara74;           /**< @Value 0   @Desc Reserved Para 74*/
    S32 ReservedPara75;           /**< @Value 0   @Desc Reserved Para 75*/
    S32 ReservedPara76;           /**< @Value 0   @Desc Reserved Para 76*/
    S32 ReservedPara77;           /**< @Value 0   @Desc Reserved Para 77*/
    S32 ReservedPara78;           /**< @Value 0   @Desc Reserved Para 78*/
    S32 ReservedPara79;           /**< @Value 0   @Desc Reserved Para 79*/
} PACKED DSP_PARA_RESERVED_STRU;

#else

typedef struct DSP_PARA_RESERVED_s {
    S32 ReservedPara0;           /**< @Value 0   @Desc Reserved Para 0*/
    S32 ReservedPara1;           /**< @Value 0   @Desc Reserved Para 1*/
    S32 ReservedPara2;           /**< @Value 0   @Desc Reserved Para 2*/
    S32 ReservedPara3;           /**< @Value 0   @Desc Reserved Para 3*/
    S32 ReservedPara4;           /**< @Value 0   @Desc Reserved Para 4*/
    S32 ReservedPara5;           /**< @Value 0   @Desc Reserved Para 5*/
    S32 ReservedPara6;           /**< @Value 0   @Desc Reserved Para 6*/
    S32 ReservedPara7;           /**< @Value 0   @Desc Reserved Para 7*/
    S32 ReservedPara8;           /**< @Value 0   @Desc Reserved Para 8*/
    S32 ReservedPara9;           /**< @Value 0   @Desc Reserved Para 9*/
    S32 ReservedPara10;           /**< @Value 0   @Desc Reserved Para 10*/
    S32 ReservedPara11;           /**< @Value 0   @Desc Reserved Para 11*/
    S32 ReservedPara12;           /**< @Value 0   @Desc Reserved Para 12*/
    S32 ReservedPara13;           /**< @Value 0   @Desc Reserved Para 13*/
    S32 ReservedPara14;           /**< @Value 0   @Desc Reserved Para 14*/
    S32 ReservedPara15;           /**< @Value 0   @Desc Reserved Para 15*/
    S32 ReservedPara16;           /**< @Value 0   @Desc Reserved Para 16*/
    S32 ReservedPara17;           /**< @Value 0   @Desc Reserved Para 17*/
    S32 ReservedPara18;           /**< @Value 0   @Desc Reserved Para 18*/
    S32 ReservedPara19;           /**< @Value 0   @Desc Reserved Para 19*/
    S32 ReservedPara20;           /**< @Value 0   @Desc Reserved Para 20*/
    S32 ReservedPara21;           /**< @Value 0   @Desc Reserved Para 21*/
    S32 ReservedPara22;           /**< @Value 0   @Desc Reserved Para 22*/
    S32 ReservedPara23;           /**< @Value 0   @Desc Reserved Para 23*/
    S32 ReservedPara24;           /**< @Value 0   @Desc Reserved Para 24*/
    S32 ReservedPara25;           /**< @Value 0   @Desc Reserved Para 25*/
    S32 ReservedPara26;           /**< @Value 0   @Desc Reserved Para 26*/
    S32 ReservedPara27;           /**< @Value 0   @Desc Reserved Para 27*/
    S32 ReservedPara28;           /**< @Value 0   @Desc Reserved Para 28*/
    S32 ReservedPara29;           /**< @Value 0   @Desc Reserved Para 29*/
    S32 ReservedPara30;           /**< @Value 0   @Desc Reserved Para 30*/
    S32 ReservedPara31;           /**< @Value 0   @Desc Reserved Para 31*/
    S32 ReservedPara32;           /**< @Value 0   @Desc Reserved Para 32*/
    S32 ReservedPara33;           /**< @Value 0   @Desc Reserved Para 33*/
    S32 ReservedPara34;           /**< @Value 0   @Desc Reserved Para 34*/
    S32 ReservedPara35;           /**< @Value 0   @Desc Reserved Para 35*/
    S32 ReservedPara36;           /**< @Value 0   @Desc Reserved Para 36*/
    S32 ReservedPara37;           /**< @Value 0   @Desc Reserved Para 37*/
    S32 ReservedPara38;           /**< @Value 0   @Desc Reserved Para 38*/
    S32 ReservedPara39;           /**< @Value 0   @Desc Reserved Para 39*/
    S32 ReservedPara40;           /**< @Value 0   @Desc Reserved Para 40*/
    S32 ReservedPara41;           /**< @Value 0   @Desc Reserved Para 41*/
    S32 ReservedPara42;           /**< @Value 0   @Desc Reserved Para 42*/
    S32 ReservedPara43;           /**< @Value 0   @Desc Reserved Para 43*/
    S32 ReservedPara44;           /**< @Value 0   @Desc Reserved Para 44*/
    S32 ReservedPara45;           /**< @Value 0   @Desc Reserved Para 45*/
    S32 ReservedPara46;           /**< @Value 0   @Desc Reserved Para 46*/
    S32 ReservedPara47;           /**< @Value 0   @Desc Reserved Para 47*/
    S32 ReservedPara48;           /**< @Value 0   @Desc Reserved Para 48*/
    S32 ReservedPara49;           /**< @Value 0   @Desc Reserved Para 49*/
    S32 ReservedPara50;           /**< @Value 0   @Desc Reserved Para 50*/
    S32 ReservedPara51;           /**< @Value 0   @Desc Reserved Para 51*/
    S32 ReservedPara52;           /**< @Value 0   @Desc Reserved Para 52*/
    S32 ReservedPara53;           /**< @Value 0   @Desc Reserved Para 53*/
    S32 ReservedPara54;           /**< @Value 0   @Desc Reserved Para 54*/
    S32 ReservedPara55;           /**< @Value 0   @Desc Reserved Para 55*/
    S32 ReservedPara56;           /**< @Value 0   @Desc Reserved Para 56*/
    S32 ReservedPara57;           /**< @Value 0   @Desc Reserved Para 57*/
    S32 ReservedPara58;           /**< @Value 0   @Desc Reserved Para 58*/
    S32 ReservedPara59;           /**< @Value 0   @Desc Reserved Para 59*/
    S32 ReservedPara60;           /**< @Value 0   @Desc Reserved Para 60*/
    S32 ReservedPara61;           /**< @Value 0   @Desc Reserved Para 61*/
    S32 ReservedPara62;           /**< @Value 0   @Desc Reserved Para 62*/
    S32 ReservedPara63;           /**< @Value 0   @Desc Reserved Para 63*/
    S32 ReservedPara64;           /**< @Value 0   @Desc Reserved Para 64*/
    S32 ReservedPara65;           /**< @Value 0   @Desc Reserved Para 65*/
    S32 ReservedPara66;           /**< @Value 0   @Desc Reserved Para 66*/
    S32 ReservedPara67;           /**< @Value 0   @Desc Reserved Para 67*/
    S32 ReservedPara68;           /**< @Value 0   @Desc Reserved Para 68*/
    S32 ReservedPara69;           /**< @Value 0   @Desc Reserved Para 69*/
    S32 ReservedPara70;           /**< @Value 0   @Desc Reserved Para 70*/
    S32 ReservedPara71;           /**< @Value 0   @Desc Reserved Para 71*/
    S32 ReservedPara72;           /**< @Value 0   @Desc Reserved Para 72*/
    S32 ReservedPara73;           /**< @Value 0   @Desc Reserved Para 73*/
    S32 ReservedPara74;           /**< @Value 0   @Desc Reserved Para 74*/
    S32 ReservedPara75;           /**< @Value 0   @Desc Reserved Para 75*/
    S32 ReservedPara76;           /**< @Value 0   @Desc Reserved Para 76*/
    S32 ReservedPara77;           /**< @Value 0   @Desc Reserved Para 77*/
    S32 ReservedPara78;           /**< @Value 0   @Desc Reserved Para 78*/
    S32 ReservedPara79;           /**< @Value 0   @Desc Reserved Para 79*/
    S32 ReservedPara80;           /**< @Value 0   @Desc Reserved Para 80*/
    S32 ReservedPara81;           /**< @Value 0   @Desc Reserved Para 81*/
    S32 ReservedPara82;           /**< @Value 0   @Desc Reserved Para 82*/
    S32 ReservedPara83;           /**< @Value 0   @Desc Reserved Para 83*/
    S32 ReservedPara84;           /**< @Value 0   @Desc Reserved Para 84*/
    S32 ReservedPara85;           /**< @Value 0   @Desc Reserved Para 85*/
    S32 ReservedPara86;           /**< @Value 0   @Desc Reserved Para 86*/
    S32 ReservedPara87;           /**< @Value 0   @Desc Reserved Para 87*/
    S32 ReservedPara88;           /**< @Value 0   @Desc Reserved Para 88*/
    S32 ReservedPara89;           /**< @Value 0   @Desc Reserved Para 89*/
    S32 ReservedPara90;           /**< @Value 0   @Desc Reserved Para 90*/
    S32 ReservedPara91;           /**< @Value 0   @Desc Reserved Para 91*/
    S32 ReservedPara92;           /**< @Value 0   @Desc Reserved Para 92*/
    S32 ReservedPara93;           /**< @Value 0   @Desc Reserved Para 93*/
    S32 ReservedPara94;           /**< @Value 0   @Desc Reserved Para 94*/
    S32 ReservedPara95;           /**< @Value 0   @Desc Reserved Para 95*/
    S32 ReservedPara96;           /**< @Value 0   @Desc Reserved Para 96*/
    S32 ReservedPara97;           /**< @Value 0   @Desc Reserved Para 97*/
    S32 ReservedPara98;           /**< @Value 0   @Desc Reserved Para 98*/
    S32 ReservedPara99;           /**< @Value 0   @Desc Reserved Para 99*/
    S32 ReservedPara100;           /**< @Value 0   @Desc Reserved Para 100*/
    S32 ReservedPara101;           /**< @Value 0   @Desc Reserved Para 101*/
    S32 ReservedPara102;           /**< @Value 0   @Desc Reserved Para 102*/
    S32 ReservedPara103;           /**< @Value 0   @Desc Reserved Para 103*/
    S32 ReservedPara104;           /**< @Value 0   @Desc Reserved Para 104*/
    S32 ReservedPara105;           /**< @Value 0   @Desc Reserved Para 105*/
    S32 ReservedPara106;           /**< @Value 0   @Desc Reserved Para 106*/
    S32 ReservedPara107;           /**< @Value 0   @Desc Reserved Para 107*/
    S32 ReservedPara108;           /**< @Value 0   @Desc Reserved Para 108*/
    S32 ReservedPara109;           /**< @Value 0   @Desc Reserved Para 109*/
    S32 ReservedPara110;           /**< @Value 0   @Desc Reserved Para 110*/
    S32 ReservedPara111;           /**< @Value 0   @Desc Reserved Para 111*/
    S32 ReservedPara112;           /**< @Value 0   @Desc Reserved Para 112*/
    S32 ReservedPara113;           /**< @Value 0   @Desc Reserved Para 113*/
    S32 ReservedPara114;           /**< @Value 0   @Desc Reserved Para 114*/
    S32 ReservedPara115;           /**< @Value 0   @Desc Reserved Para 115*/
    S32 ReservedPara116;           /**< @Value 0   @Desc Reserved Para 116*/
    S32 ReservedPara117;           /**< @Value 0   @Desc Reserved Para 117*/
    S32 ReservedPara118;           /**< @Value 0   @Desc Reserved Para 118*/
    S32 ReservedPara119;           /**< @Value 0   @Desc Reserved Para 119*/
    S32 ReservedPara120;           /**< @Value 0   @Desc Reserved Para 120*/
    S32 ReservedPara121;           /**< @Value 0   @Desc Reserved Para 121*/
    S32 ReservedPara122;           /**< @Value 0   @Desc Reserved Para 122*/
    S32 ReservedPara123;           /**< @Value 0   @Desc Reserved Para 123*/
    S32 ReservedPara124;           /**< @Value 0   @Desc Reserved Para 124*/
    S32 ReservedPara125;           /**< @Value 0   @Desc Reserved Para 125*/
    S32 ReservedPara126;           /**< @Value 0   @Desc Reserved Para 126*/
    S32 ReservedPara127;           /**< @Value 0   @Desc Reserved Para 127*/
} PACKED DSP_PARA_RESERVED_STRU;

#endif

#endif /* _DSP_PARA_RESERVED_H_ */
